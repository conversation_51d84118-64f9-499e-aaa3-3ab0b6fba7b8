<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>3.3.7</version>
		<relativePath/>
	</parent>
	<groupId>com.optahub.awi</groupId>
	<artifactId>service</artifactId>
	<version>0.0.1-SNAPSHOT</version>
	<name>service</name>
	<description>Spring Boot Service Project for Optahub</description>
	<properties>
		<java.version>17</java.version>
		<spring-ai.version>1.0.0-M5</spring-ai.version>
	</properties>
	<repositories>
		<repository>
			<id>spring-milestones</id>
			<url>https://repo.spring.io/milestone</url>
			<snapshots>
				<enabled>false</enabled>
			</snapshots>
		</repository>
		<repository>
			<id>spring-snapshots</id>
			<url>https://repo.spring.io/snapshot</url>
			<snapshots>
				<enabled>true</enabled>
			</snapshots>
		</repository>
	</repositories>
	<dependencies>
		<!-- Starters begin -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
			<exclusions>
				<exclusion>
					<groupId>org.springframework</groupId>
					<artifactId>spring-webmvc</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.springframework</groupId>
					<artifactId>spring-web</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web-services</artifactId>
			<exclusions>
				<exclusion>
					<groupId>org.springframework.boot</groupId>
					<artifactId>spring-boot-starter-tomcat</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-security</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.security</groupId>
			<artifactId>spring-security-config</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-validation</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-cache</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-logging</artifactId>
		</dependency>
		<!-- Starters end -->
		<dependency>
			<groupId>org.springframework.data</groupId>
			<artifactId>spring-data-elasticsearch</artifactId>
			<version>5.2.0</version>
			<exclusions>
				<!--<exclusion>
					<groupId>org.elasticsearch.client</groupId>
					<artifactId>elasticsearch-rest-high-level-client</artifactId>
				</exclusion>-->
				<!--<exclusion>
					<artifactId>elasticsearch-java</artifactId>
					<groupId>co.elastic.clients</groupId>
				</exclusion>-->
			</exclusions>
		</dependency>
		<!--<dependency>
			<groupId>co.elastic.clients</groupId>
			<artifactId>elasticsearch-java</artifactId>
			<version>8.15.5</version>
		</dependency>-->
		<!--<dependency>
			<groupId>org.elasticsearch.client</groupId>
			<artifactId>elasticsearch-rest-high-level-client</artifactId>
			<version>7.17.10</version>
			<exclusions>
			<exclusion>
				<groupId>org.elasticsearch</groupId>
				<artifactId>elasticsearch</artifactId>
			</exclusion>
			</exclusions>
		</dependency>-->
		<dependency>
			<groupId>org.springframework.ai</groupId>
			<artifactId>spring-ai-elasticsearch-store-spring-boot-starter</artifactId>
			<version>1.0.0-M5</version>
			<exclusions>
				<exclusion>
					<artifactId>validation-api</artifactId>
					<groupId>javax.validation</groupId>
				</exclusion>
				<exclusion>
					<artifactId>HdrHistogram</artifactId>
					<groupId>org.hdrhistogram</groupId>
				</exclusion>
				<exclusion>
					<artifactId>error_prone_annotations</artifactId>
					<groupId>com.google.errorprone</groupId>
				</exclusion>
				<exclusion>
					<artifactId>jedis</artifactId>
					<groupId>redis.clients</groupId>
				</exclusion>
				<exclusion>
					<artifactId>elasticsearch-java</artifactId>
					<groupId>co.elastic.clients</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.springframework.ai</groupId>
			<artifactId>spring-ai-openai-spring-boot-starter</artifactId>
			<version>1.0.0.M2</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.ai</groupId>
			<artifactId>spring-ai-openai</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.ai</groupId>
			<artifactId>spring-ai-vertex-ai-embedding-spring-boot-starter</artifactId>
			<version>1.0.0.M2</version>
			<exclusions>
				<exclusion>
					<artifactId>google-cloud-aiplatform</artifactId>
					<groupId>com.google.cloud</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.google.guava</groupId>
			<artifactId>guava</artifactId>
			<version>19.0</version>
		</dependency>
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<version>1.18.24</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.json</groupId>
			<artifactId>json</artifactId>
			<version>20231013</version>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-text</artifactId>
			<version>1.11.0</version>
		</dependency>

		<dependency>
			<groupId>io.jsonwebtoken</groupId>
			<artifactId>jjwt</artifactId>
			<version>0.12.6</version>
		</dependency>

		<dependency>
			<groupId>com.amazonaws</groupId>
			<artifactId>aws-java-sdk-s3</artifactId>
			<version>1.12.762</version>
			<exclusions>
				<exclusion>
					<artifactId>commons-logging</artifactId>
					<groupId>commons-logging</groupId>
				</exclusion>
				<exclusion>
					<artifactId>httpclient</artifactId>
					<groupId>org.apache.httpcomponents</groupId>
				</exclusion>
				<exclusion>
					<artifactId>joda-time</artifactId>
					<groupId>joda-time</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi-ooxml</artifactId>
			<version>5.2.3</version>
		</dependency>

		<dependency>
			<groupId>org.mapstruct</groupId>
			<artifactId>mapstruct</artifactId>
			<version>1.5.5.Final</version>
		</dependency>

		<!-- MapStruct Processor for Annotation Processing -->
		<dependency>
			<groupId>org.mapstruct</groupId>
			<artifactId>mapstruct-processor</artifactId>
			<version>1.5.5.Final</version>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>com.opencsv</groupId>
			<artifactId>opencsv</artifactId>
			<version>5.7.1</version>
			<exclusions>
				<exclusion>
					<artifactId>commons-text</artifactId>
					<groupId>org.apache.commons</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.jsoup</groupId>
			<artifactId>jsoup</artifactId>
			<version>1.15.3</version>
		</dependency>

		<dependency>
			<groupId>org.springdoc</groupId>
			<artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
			<version>2.5.0</version>
		</dependency>

		<dependency>
			<groupId>jakarta.xml.bind</groupId>
			<artifactId>jakarta.xml.bind-api</artifactId>
			<version>4.0.2</version>
		</dependency>
		<dependency>
			<groupId>org.glassfish.jaxb</groupId>
			<artifactId>jaxb-runtime</artifactId>
			<version>4.0.5</version>
			<exclusions>
				<exclusion>
					<groupId>javax.xml.bind</groupId>
					<artifactId>jaxb-api</artifactId>
				</exclusion>
				<exclusion>
					<artifactId>jaxb-core</artifactId>
					<groupId>org.glassfish.jaxb</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.glassfish.jaxb</groupId>
			<artifactId>jaxb-xjc</artifactId>
			<version>4.0.0</version>
			<exclusions>
				<exclusion>
					<artifactId>rngom</artifactId>
					<groupId>com.sun.xml.bind.external</groupId>
				</exclusion>
				<exclusion>
					<artifactId>jaxb-core</artifactId>
					<groupId>org.glassfish.jaxb</groupId>
				</exclusion>
				<exclusion>
					<artifactId>txw2</artifactId>
					<groupId>org.glassfish.jaxb</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.glassfish.jaxb</groupId>
			<artifactId>txw2</artifactId>
			<version>4.0.5</version>
		</dependency>
		<dependency>
			<groupId>com.sun.xml.bind</groupId>
			<artifactId>jaxb-core</artifactId>
			<version>3.0.0</version>
		</dependency>
		<dependency>
			<groupId>joda-time</groupId>
			<artifactId>joda-time</artifactId>
			<version>2.12.7</version>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.datatype</groupId>
			<artifactId>jackson-datatype-jsr310</artifactId>
		</dependency>
		<dependency>
			<groupId>com.github.ben-manes.caffeine</groupId>
			<artifactId>caffeine</artifactId>
		</dependency>
	</dependencies>

	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>jakarta.xml.bind</groupId>
				<artifactId>jakarta.xml.bind-api</artifactId>
				<version>4.0.2</version>
			</dependency>
			<dependency>
				<groupId>org.glassfish.jaxb</groupId>
				<artifactId>jaxb-runtime</artifactId>
				<version>4.0.5</version>
				<exclusions>
					<exclusion>
						<groupId>javax.xml.bind</groupId>
						<artifactId>jaxb-api</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>org.springframework.ai</groupId>
				<artifactId>spring-ai-bom</artifactId>
				<version>0.8.0</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>

		</dependencies>

	</dependencyManagement>

	<build>
		<plugins>
			<plugin>
				<groupId>com.sun.xml.ws</groupId>
				<artifactId>jaxws-maven-plugin</artifactId>
				<version>4.0.3</version> <executions>
				<execution>
					<id>Product-Service-Generate-WSImport</id>
					<goals>
						<goal>wsimport</goal>
					</goals>
					<configuration>
						<wsdlDirectory>${project.basedir}/src/main/resources/wsdl</wsdlDirectory>
						<wsdlFiles>
							<wsdlFile>ProductDataService.wsdl</wsdlFile>
						</wsdlFiles>
						<sourceDestDir>${project.build.directory}/generated-sources/productservice</sourceDestDir>
						<packageName>org.optahub.promostandards.wsdl.productservice</packageName>
						<keep>true</keep>
						<extension>true</extension> <vmArgs>
						<vmArg>-Djavax.xml.accessExternalSchema=all</vmArg>
						<vmArg>--add-opens</vmArg>
						<vmArg>java.base/java.lang=ALL-UNNAMED</vmArg>
						<vmArg>--add-opens</vmArg>
						<vmArg>java.base/java.util=ALL-UNNAMED</vmArg>
						<vmArg>--add-opens</vmArg>
						<vmArg>java.base/java.net=ALL-UNNAMED</vmArg>
					</vmArgs>
						<verbose>true</verbose>
					</configuration>
				</execution>
				<execution>
					<id>Inventory-Service-Generate-WSImport</id>
					<goals>
						<goal>wsimport</goal>
					</goals>
					<configuration>
						<wsdlDirectory>${project.basedir}/src/main/resources/wsdl</wsdlDirectory>
						<wsdlFiles>
							<wsdlFile>InventoryService.wsdl</wsdlFile>
						</wsdlFiles>
						<sourceDestDir>${project.build.directory}/generated-sources/inventory</sourceDestDir>
						<packageName>org.optahub.promostandards.wsdl.inventory</packageName>
						<keep>true</keep>
						<extension>true</extension>
						<vmArgs>
							<vmArg>-Djavax.xml.accessExternalSchema=all</vmArg>
							<vmArg>--add-opens</vmArg>
							<vmArg>java.base/java.lang=ALL-UNNAMED</vmArg>
							<vmArg>--add-opens</vmArg>
							<vmArg>java.base/java.util=ALL-UNNAMED</vmArg>
							<vmArg>--add-opens</vmArg>
							<vmArg>java.base/java.net=ALL-UNNAMED</vmArg>
						</vmArgs>
						<verbose>true</verbose>
					</configuration>
				</execution>
				<execution>
					<id>Pricing-Service-Generate-WSImport</id>
					<goals>
						<goal>wsimport</goal>
					</goals>
					<configuration>
						<wsdlDirectory>${project.basedir}/src/main/resources/wsdl</wsdlDirectory>
						<wsdlFiles>
							<wsdlFile>PricingAndConfiguration.wsdl</wsdlFile>
						</wsdlFiles>
						<sourceDestDir>${project.build.directory}/generated-sources/pricing</sourceDestDir>
						<packageName>org.optahub.promostandards.wsdl.pricing</packageName>
						<keep>true</keep>
						<extension>true</extension>
						<vmArgs>
							<vmArg>-Djavax.xml.accessExternalSchema=all</vmArg>
							<vmArg>--add-opens</vmArg>
							<vmArg>java.base/java.lang=ALL-UNNAMED</vmArg>
							<vmArg>--add-opens</vmArg>
							<vmArg>java.base/java.util=ALL-UNNAMED</vmArg>
							<vmArg>--add-opens</vmArg>
							<vmArg>java.base/java.net=ALL-UNNAMED</vmArg>
						</vmArgs>
						<verbose>true</verbose>
					</configuration>
				</execution>
				<execution>
					<id>Media-Service-Generate-WSImport</id>
					<goals>
						<goal>wsimport</goal>
					</goals>
					<configuration>
						<wsdlDirectory>${project.basedir}/src/main/resources/wsdl</wsdlDirectory>
						<wsdlFiles>
							<wsdlFile>MediaContentService.wsdl</wsdlFile>
						</wsdlFiles>
						<sourceDestDir>${project.build.directory}/generated-sources/media</sourceDestDir>
						<packageName>org.optahub.promostandards.wsdl.media</packageName>
						<keep>true</keep>
						<extension>true</extension>
						<vmArgs>
							<vmArg>-Djavax.xml.accessExternalSchema=all</vmArg>
							<vmArg>--add-opens</vmArg>
							<vmArg>java.base/java.lang=ALL-UNNAMED</vmArg>
							<vmArg>--add-opens</vmArg>
							<vmArg>java.base/java.util=ALL-UNNAMED</vmArg>
							<vmArg>--add-opens</vmArg>
							<vmArg>java.base/java.net=ALL-UNNAMED</vmArg>
						</vmArgs>
						<verbose>true</verbose>
					</configuration>
				</execution>
			</executions>
			</plugin>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.13.0</version>
				<configuration>
					<source>${java.version}</source>
					<target>${java.version}</target>
					<release>${java.version}</release>
					<annotationProcessorPaths>
						<path>
							<groupId>org.projectlombok</groupId>
							<artifactId>lombok</artifactId>
							<version>1.18.32</version> </path>
						<path>
							<groupId>org.mapstruct</groupId>
							<artifactId>mapstruct-processor</artifactId>
							<version>1.5.5.Final</version> </path>
					</annotationProcessorPaths>
				</configuration>
			</plugin>

			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<version>3.3.7</version>
			</plugin>
		</plugins>
	</build>
</project>
