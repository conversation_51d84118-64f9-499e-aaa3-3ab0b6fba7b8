================================================================================
AWI_Backend V2 - PROJECT CHANGES LOG
================================================================================
Date: 2025-08-04
Purpose: Document all code changes made to run the project successfully
Author: Nural

This file contains ALL changes made to the original codebase to enable the 
project to run in development mode without Elasticsearch dependency.

================================================================================
OVERVIEW OF CHANGES
================================================================================

The main issue was that the application was configured for production/UAT 
environment with Elasticsearch dependencies, but we needed to run it in 
development mode without Elasticsearch. The changes include:

1. Configuration fixes (missing properties)
2. Profile-based component exclusions
3. Elasticsearch auto-configuration disabling
4. CORS configuration fix

================================================================================
DETAILED CHANGES
================================================================================

FILE: src/main/resources/application.properties
CHANGE: Line 2
ORIGINAL: spring.profiles.active=uat
MODIFIED: spring.profiles.active=dev
REASON: Switch from UAT profile to development profile to avoid Elasticsearch 
        dependencies and use local configuration.

--------------------------------------------------------------------------------

FILE: src/main/resources/application-dev.properties
CHANGE: Line 3 (INSERT AFTER)
ORIGINAL: elastic.host = 127.0.0.1
ADDED AFTER LINE 2:
elastic.port = 9200

REASON: Missing elastic.port property that was referenced in the application 
        but not defined in dev profile.

--------------------------------------------------------------------------------

FILE: src/main/resources/application-dev.properties  
CHANGE: Lines 4-7 (REPLACE)
ORIGINAL: 
# Elasticsearch connection timeout settings
spring.elasticsearch.connection-timeout=1s
spring.elasticsearch.socket-timeout=1s

MODIFIED:
# Elasticsearch connection timeout settings (disabled for dev)
# spring.elasticsearch.connection-timeout=1s
# spring.elasticsearch.socket-timeout=1s
spring.data.elasticsearch.repositories.enabled=false

REASON: Disable Elasticsearch repositories and connection settings for 
        development mode to prevent connection attempts.

--------------------------------------------------------------------------------

FILE: src/main/resources/application-dev.properties
CHANGE: Line 10 (REPLACE)
ORIGINAL: cros.origin.url = http://localhost:4200
MODIFIED: cors.origin.url = http://localhost:5173

REASON: Fixed typo in property name (cros -> cors) and updated port from 4200 
        (Angular default) to 5173 (Vite default) to match the frontend setup.

--------------------------------------------------------------------------------

FILE: src/main/resources/application-dev.properties
CHANGE: Lines 52-53 (INSERT AFTER LINE 52)
ORIGINAL: spring.ai.openai.embedding.options.model=text-embedding-ada-002
ADDED AFTER:
spring.ai.openai.chat.options.model=gpt-4o

REASON: Missing required OpenAI chat model configuration that was referenced 
        in the AI configuration but not defined.

--------------------------------------------------------------------------------

FILE: src/main/resources/application-dev.properties
CHANGE: Line 34 (INSERT AFTER LINE 33)
ORIGINAL: aws.s3.bucket = awi-esb
ADDED AFTER:
aws.s3.history.bucket = awi-esb

REASON: Missing S3 history bucket configuration required by the chat history 
        service for storing conversation data.

--------------------------------------------------------------------------------

FILE: src/main/java/com/optahub/awi/service/Application.java
CHANGE: Lines 3-12 (REPLACE)
ORIGINAL:
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;

@SpringBootApplication
@EnableCaching

MODIFIED:
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.elasticsearch.ElasticsearchDataAutoConfiguration;
import org.springframework.boot.autoconfigure.elasticsearch.ElasticsearchRestClientAutoConfiguration;
import org.springframework.cache.annotation.EnableCaching;

@SpringBootApplication(exclude = {
    ElasticsearchDataAutoConfiguration.class,
    ElasticsearchRestClientAutoConfiguration.class
})
@EnableCaching

REASON: Exclude Elasticsearch auto-configuration classes to prevent Spring 
        from trying to auto-configure Elasticsearch connections in dev mode.

--------------------------------------------------------------------------------

FILE: src/main/java/com/optahub/awi/service/chatbot/controller/IndexSageProductController.java
CHANGE: Lines 12-17 (REPLACE)
ORIGINAL:
import com.optahub.awi.service.chatbot.service.IndexSageProductService;
import org.springframework.web.multipart.MultipartFile;

@RestController

MODIFIED:
import com.optahub.awi.service.chatbot.service.IndexSageProductService;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.context.annotation.Profile;

@RestController
@Profile("!dev")

REASON: Exclude this controller from development profile since it depends on 
        Elasticsearch repositories that are disabled in dev mode.

--------------------------------------------------------------------------------

FILE: src/main/java/com/optahub/awi/service/chatbot/service/IndexSageProductService.java
CHANGE: Lines 68-74 (REPLACE)
ORIGINAL:
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

@Service
@Slf4j

MODIFIED:
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.context.annotation.Profile;

@Service
@Slf4j
@Profile("!dev")

REASON: Exclude this service from development profile since it depends on 
        Elasticsearch repositories and clients that are disabled in dev mode.

--------------------------------------------------------------------------------

FILE: src/main/java/com/optahub/awi/service/chatbot/repository/SageProductRepository.java
CHANGE: Lines 12-17 (REPLACE)
ORIGINAL:
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository

MODIFIED:
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.context.annotation.Profile;

@Repository
@Profile("!dev")

REASON: Exclude this Elasticsearch repository from development profile to 
        prevent Spring from trying to instantiate it without Elasticsearch.

--------------------------------------------------------------------------------

FILE: src/main/java/com/optahub/awi/service/chatbot/repository/VendorRepository.java
CHANGE: Lines 4-11 (REPLACE)
ORIGINAL:
import org.springframework.data.elasticsearch.repository.ElasticsearchRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository

MODIFIED:
import org.springframework.data.elasticsearch.repository.ElasticsearchRepository;
import org.springframework.stereotype.Repository;
import org.springframework.context.annotation.Profile;

import java.util.List;

@Repository
@Profile("!dev")

REASON: Exclude this Elasticsearch repository from development profile to 
        prevent Spring from trying to instantiate it without Elasticsearch.

--------------------------------------------------------------------------------

FILE: src/main/java/com/optahub/awi/service/chatbot/service/VendorService.java
CHANGE: Lines 7-22 (REPLACE)
ORIGINAL:
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j

MODIFIED:
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.context.annotation.Profile;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
@Profile("!dev")

REASON: Exclude this service from development profile since it depends on 
        VendorRepository which is an Elasticsearch repository.

--------------------------------------------------------------------------------

FILE: src/main/java/com/optahub/awi/service/chatbot/controller/VendorController.java
CHANGE: Lines 8-21 (REPLACE)
ORIGINAL:
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.validation.Valid;
import java.util.List;
import java.util.NoSuchElementException;

import lombok.AllArgsConstructor;

@RestController
@RequestMapping("/api/vendors")
@AllArgsConstructor

MODIFIED:
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.context.annotation.Profile;

import jakarta.validation.Valid;
import java.util.List;
import java.util.NoSuchElementException;

import lombok.AllArgsConstructor;

@RestController
@RequestMapping("/api/vendors")
@AllArgsConstructor
@Profile("!dev")

REASON: Exclude this controller from development profile since it depends on 
        VendorService which requires Elasticsearch repositories.

================================================================================
SUMMARY OF PROFILE-BASED EXCLUSIONS
================================================================================

Components excluded from 'dev' profile (using @Profile("!dev")):
1. IndexSageProductController - Elasticsearch-dependent product indexing
2. IndexSageProductService - Elasticsearch product indexing service  
3. SageProductRepository - Elasticsearch repository for products
4. VendorRepository - Elasticsearch repository for vendors
5. VendorService - Service depending on VendorRepository
6. VendorController - Controller depending on VendorService

These components will be available in 'uat' and 'prod' profiles where 
Elasticsearch is properly configured.

================================================================================
ROLLBACK INSTRUCTIONS
================================================================================

To revert ALL changes and return to the original state:

1. Revert application.properties:
   Line 2: Change "spring.profiles.active=dev" back to "spring.profiles.active=uat"

2. Revert application-dev.properties:
   - Remove line 3: "elastic.port = 9200"
   - Lines 4-7: Uncomment Elasticsearch settings, remove disable line
   - Line 10: Change "cors.origin.url" back to "cros.origin.url" and port to 4200
   - Remove line after 52: "spring.ai.openai.chat.options.model=gpt-4o"  
   - Remove line after 33: "aws.s3.history.bucket = awi-esb"

3. Revert Application.java:
   - Remove Elasticsearch exclusion imports and exclude clause
   - Return to simple @SpringBootApplication annotation

4. Remove @Profile("!dev") annotations from:
   - IndexSageProductController
   - IndexSageProductService  
   - SageProductRepository
   - VendorRepository
   - VendorService
   - VendorController

5. Remove corresponding import statements:
   - Remove "import org.springframework.context.annotation.Profile;" from all files

After these changes, the application will return to its original state requiring
Elasticsearch and UAT configuration to run properly.

================================================================================
END OF CHANGES LOG
================================================================================
