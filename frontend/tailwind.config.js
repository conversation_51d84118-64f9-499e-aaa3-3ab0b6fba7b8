/** @type {import('tailwindcss').Config} */
export default {
  content: ['./index.html', './src/**/*.{js,ts,jsx,tsx}'],
  darkMode: 'media',
  theme: {
    extend: {
      colors: {
        optamark: {
          blue: 'rgb(var(--optamark-blue) / <alpha-value>)',
          secondary: 'rgb(var(--optamark-secondary) / <alpha-value>)',
          dark: 'rgb(var(--optamark-dark) / <alpha-value>)',
          gray: 'rgb(var(--optamark-gray) / <alpha-value>)',
        },
      },
      fontFamily: {
        sans: ['Inter', 'sans-serif'],
      },
    },
  },
  plugins: [],
};