<?xml version="1.0" encoding="UTF-8"?>
<!-- Generator: Adobe Illustrator 24.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg xmlns:cc="http://creativecommons.org/ns#" xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:svg="http://www.w3.org/2000/svg" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" id="svg2" x="0px" y="0px" viewBox="0 0 160 41.8" style="enable-background:new 0 0 160 41.8;" xml:space="preserve">
<style type="text/css">
	.st0{clip-path:url(#SVGID_4_);}
	.st1{opacity:0.8;clip-path:url(#SVGID_5_);}
	.st2{fill:#FFFFFF;}
	.st3{fill:#373D41;}
	.st4{opacity:0.9;clip-path:url(#SVGID_6_);fill:#2BABE2;}
	.st5{opacity:0.9;clip-path:url(#SVGID_6_);fill:#50B748;}
	.st6{opacity:0.9;clip-path:url(#SVGID_6_);fill:#EB6A24;}
	.st7{opacity:0.9;clip-path:url(#SVGID_6_);fill:#FDD404;}
</style>
<g>
	<g>
		<g>
			<defs>
				<path id="SVGID_3_" d="M156.1,8.3c-0.2-0.2-0.5-0.5-0.8-0.9v0c-0.1-0.1-0.1-0.1-0.2-0.2v0c-0.2-0.2-0.4-0.4-0.5-0.6      c-0.6-0.6-1.2-1.3-1.7-1.8c0,0-0.1-0.1-0.1-0.1c-0.1-0.1-0.2-0.2-0.3-0.3c0,0,0,0,0,0c0,0,0,0,0,0c0,0-0.1-0.1-0.1-0.1      c-0.1-0.1-0.2-0.2-0.2-0.3c0,0-0.1-0.1-0.1-0.1c0,0-0.1-0.1-0.1-0.1c0,0-0.1-0.1-0.1-0.1c0,0,0,0,0,0s0,0,0,0      c-0.1,0.1-1,1-1.9,2c-0.1,0.1-0.1,0.1-0.2,0.2c-0.1,0.1-0.2,0.2-0.3,0.3l0,0c-0.8,0.8-1.5,1.6-1.9,2.1c-2.4,2.5-2.4,6.6,0,9.1      c2,2.1,5,2.4,7.4,1v0c0.1,0,0.1-0.1,0.2-0.1c0,0,0,0,0.1,0v0c0.3-0.2,0.7-0.5,1-0.8C158.5,14.9,158.5,10.8,156.1,8.3z"></path>
			</defs>
			<defs>
				<path id="SVGID_2_" d="M151.9,20.1L151.9,20.1c-1.8,0-3.6-0.8-4.9-2.1c-2.7-2.8-2.7-7.4,0-10.2l4.9-5.2l1.6,1.7l3.3,3.5      c2.7,2.8,2.7,7.4,0,10.2c-0.3,0.3-0.6,0.6-1,0.8v0l-0.4,0.3C154.3,19.8,153.1,20.1,151.9,20.1z M151.9,5l-3.7,3.9      c-2.1,2.2-2.1,5.7,0,7.9c1,1,2.3,1.6,3.7,1.6c0.9,0,1.7-0.2,2.5-0.7l0.3-0.2c0.3-0.2,0.6-0.5,0.8-0.7c2.1-2.2,2.1-5.7,0-7.9      L151.9,5z"></path>
			</defs>
			<use xlink:href="#SVGID_3_" style="overflow:visible;fill:#FFFFFF;"></use>
			<use xlink:href="#SVGID_2_" style="overflow:visible;fill:#FFFFFF;"></use>
			<clipPath id="SVGID_4_">
				<use xlink:href="#SVGID_3_" style="overflow:visible;"></use>
			</clipPath>
			<clipPath id="SVGID_5_" class="st0">
				<use xlink:href="#SVGID_2_" style="overflow:visible;"></use>
			</clipPath>
			<g class="st1">
				<ellipse class="st2" cx="145.4" cy="4.8" rx="8.8" ry="9.3"></ellipse>
				<path class="st2" d="M145.4,14.9c-5.3,0-9.6-4.6-9.6-10.2s4.3-10.2,9.6-10.2c5.3,0,9.6,4.6,9.6,10.2S150.7,14.9,145.4,14.9z       M145.4-3.7c-4.4,0-7.9,3.8-7.9,8.5s3.6,8.5,7.9,8.5c4.4,0,7.9-3.8,7.9-8.5S149.8-3.7,145.4-3.7z"></path>
			</g>
			<g class="st1">
				<ellipse class="st2" cx="156.3" cy="1.8" rx="8.8" ry="9.3"></ellipse>
				<path class="st2" d="M156.3,12c-5.3,0-9.6-4.6-9.6-10.2s4.3-10.2,9.6-10.2c5.3,0,9.6,4.6,9.6,10.2S161.6,12,156.3,12z       M156.3-6.7c-4.4,0-7.9,3.8-7.9,8.5s3.6,8.5,7.9,8.5c4.4,0,7.9-3.8,7.9-8.5S160.6-6.7,156.3-6.7z"></path>
			</g>
			<g class="st1">
				<ellipse class="st2" cx="159.6" cy="18.8" rx="8.8" ry="9.3"></ellipse>
				<path class="st2" d="M159.6,29c-5.3,0-9.6-4.6-9.6-10.2s4.3-10.2,9.6-10.2s9.6,4.6,9.6,10.2S164.9,29,159.6,29z M159.6,10.3      c-4.4,0-7.9,3.8-7.9,8.5s3.6,8.5,7.9,8.5c4.4,0,7.9-3.8,7.9-8.5S163.9,10.3,159.6,10.3z"></path>
			</g>
			<g class="st1">
				<ellipse class="st2" cx="147.5" cy="21.6" rx="8.8" ry="9.3"></ellipse>
				<path class="st2" d="M147.5,31.7c-5.3,0-9.6-4.6-9.6-10.2s4.3-10.2,9.6-10.2s9.6,4.6,9.6,10.2S152.7,31.7,147.5,31.7z       M147.5,13.1c-4.4,0-7.9,3.8-7.9,8.5s3.6,8.5,7.9,8.5s7.9-3.8,7.9-8.5S151.8,13.1,147.5,13.1z"></path>
			</g>
		</g>
	</g>
	<g>
		<path class="st3" d="M127.9,7.6h4.9v12.6l5.8-6.3h5.9l-6.6,6.9l6.9,10.6h-5.7l-4.6-7.1l-1.7,1.8v5.3h-4.9V7.6z M116.4,13.9h4.9    v3.5c0.5-1.2,1.2-2.1,2.1-2.9c0.9-0.7,2-1,3.5-1v5.2h-0.3c-1.6,0-2.9,0.5-3.9,1.5c-0.9,1-1.4,2.5-1.4,4.6v6.4h-4.9V13.9z     M104.1,31.6c-0.8,0-1.6-0.1-2.3-0.3c-0.7-0.2-1.4-0.6-1.9-1c-0.5-0.5-1-1-1.3-1.7c-0.3-0.7-0.5-1.4-0.5-2.3v-0.1    c0-1,0.2-1.8,0.5-2.5c0.3-0.7,0.8-1.3,1.4-1.7c0.6-0.5,1.4-0.8,2.2-1c0.9-0.2,1.8-0.3,2.8-0.3c0.9,0,1.7,0.1,2.3,0.2    c0.7,0.1,1.3,0.3,1.9,0.5v-0.3c0-1-0.3-1.8-0.9-2.4c-0.6-0.5-1.6-0.8-2.8-0.8c-1,0-1.8,0.1-2.5,0.2c-0.7,0.2-1.5,0.4-2.3,0.7    l-1.2-3.8c1-0.4,2-0.7,3-1c1-0.3,2.3-0.4,3.8-0.4c1.4,0,2.5,0.2,3.5,0.5c1,0.3,1.8,0.8,2.4,1.4c0.7,0.7,1.1,1.4,1.4,2.4    c0.3,0.9,0.5,2,0.5,3.2v10.1h-4.8v-1.9c-0.6,0.7-1.3,1.2-2.2,1.6C106.3,31.4,105.3,31.6,104.1,31.6z M105.6,28.2    c1.2,0,2.1-0.3,2.8-0.8c0.7-0.6,1.1-1.3,1.1-2.2v-0.9c-0.4-0.2-0.9-0.4-1.4-0.5c-0.5-0.1-1.1-0.2-1.7-0.2c-1.1,0-1.9,0.2-2.5,0.6    c-0.6,0.4-0.9,1-0.9,1.8v0.1c0,0.7,0.2,1.2,0.7,1.6C104.1,28,104.8,28.2,105.6,28.2z M70.3,13.9h4.9v2.5c0.3-0.4,0.6-0.7,0.9-1.1    c0.3-0.3,0.7-0.6,1.1-0.9c0.4-0.3,0.9-0.5,1.4-0.6c0.5-0.2,1.1-0.2,1.7-0.2c1.2,0,2.1,0.2,3,0.7c0.8,0.5,1.5,1.2,1.9,2    c0.8-0.9,1.6-1.6,2.5-2.1c0.9-0.5,2-0.7,3.2-0.7c1.8,0,3.3,0.5,4.3,1.6c1,1.1,1.6,2.7,1.6,4.8v11.3H92v-9.7c0-1.2-0.2-2.1-0.7-2.6    C90.7,18.3,90,18,89.1,18c-0.9,0-1.7,0.3-2.2,0.9c-0.5,0.6-0.8,1.5-0.8,2.6v9.7h-4.9v-9.7c0-1.2-0.2-2.1-0.7-2.6    c-0.5-0.6-1.2-0.9-2.1-0.9c-0.9,0-1.7,0.3-2.2,0.9c-0.5,0.6-0.8,1.5-0.8,2.6v9.7h-4.9V13.9z M58,31.6c-0.8,0-1.6-0.1-2.3-0.3    c-0.7-0.2-1.4-0.6-1.9-1c-0.5-0.5-1-1-1.3-1.7c-0.3-0.7-0.5-1.4-0.5-2.3v-0.1c0-1,0.2-1.8,0.5-2.5c0.3-0.7,0.8-1.3,1.4-1.7    c0.6-0.5,1.4-0.8,2.2-1c0.9-0.2,1.8-0.3,2.8-0.3c0.9,0,1.7,0.1,2.3,0.2c0.7,0.1,1.3,0.3,1.9,0.5v-0.3c0-1-0.3-1.8-0.9-2.4    c-0.6-0.5-1.6-0.8-2.8-0.8c-1,0-1.8,0.1-2.5,0.2c-0.7,0.2-1.5,0.4-2.3,0.7l-1.2-3.8c1-0.4,2-0.7,3-1c1-0.3,2.3-0.4,3.8-0.4    c1.4,0,2.5,0.2,3.5,0.5c1,0.3,1.8,0.8,2.4,1.4c0.7,0.7,1.1,1.4,1.4,2.4c0.3,0.9,0.5,2,0.5,3.2v10.1h-4.8v-1.9    c-0.6,0.7-1.3,1.2-2.2,1.6C60.3,31.4,59.2,31.6,58,31.6z M59.5,28.2c1.2,0,2.1-0.3,2.8-0.8c0.7-0.6,1.1-1.3,1.1-2.2v-0.9    c-0.4-0.2-0.9-0.4-1.4-0.5c-0.5-0.1-1.1-0.2-1.7-0.2c-1.1,0-1.9,0.2-2.5,0.6c-0.6,0.4-0.9,1-0.9,1.8v0.1c0,0.7,0.2,1.2,0.7,1.6    C58.1,28,58.7,28.2,59.5,28.2z M47.5,31.6c-0.8,0-1.4-0.1-2.1-0.2c-0.6-0.2-1.1-0.4-1.6-0.9c-0.4-0.4-0.8-0.9-1-1.6    c-0.2-0.7-0.4-1.5-0.4-2.5v-8.2h-2.1v-4.2h2.1V9.4h4.9v4.4h4.1v4.2h-4.1v7.4c0,1.1,0.5,1.7,1.6,1.7c0.9,0,1.7-0.2,2.4-0.6v4    c-0.5,0.3-1.1,0.5-1.7,0.7C49,31.5,48.3,31.6,47.5,31.6z M21.7,13.9h4.9v2.5c0.6-0.8,1.3-1.5,2.2-2c0.9-0.5,1.9-0.8,3.2-0.8    c1,0,2,0.2,3,0.6c1,0.4,1.8,1,2.5,1.7c0.7,0.8,1.3,1.7,1.8,2.8c0.4,1.1,0.7,2.4,0.7,3.8v0.1c0,1.5-0.2,2.7-0.7,3.8    c-0.4,1.1-1,2.1-1.8,2.8C36.9,30,36,30.6,35.1,31c-1,0.4-2,0.6-3,0.6c-1.3,0-2.4-0.3-3.3-0.8c-0.9-0.5-1.6-1.1-2.2-1.8v7.5h-4.9    V13.9z M30.8,27.4c0.6,0,1.1-0.1,1.6-0.3c0.5-0.2,1-0.6,1.3-1c0.4-0.4,0.7-0.9,0.9-1.5c0.2-0.6,0.3-1.2,0.3-2v-0.1    c0-0.7-0.1-1.4-0.3-2c-0.2-0.6-0.5-1.1-0.9-1.5c-0.4-0.4-0.8-0.7-1.3-1c-0.5-0.2-1.1-0.3-1.6-0.3c-0.6,0-1.1,0.1-1.6,0.3    c-0.5,0.2-1,0.6-1.3,1c-0.4,0.4-0.7,0.9-0.9,1.5c-0.2,0.6-0.3,1.3-0.3,2v0.1c0,0.7,0.1,1.4,0.3,2c0.2,0.6,0.5,1.1,0.9,1.5    c0.4,0.4,0.8,0.7,1.3,1C29.7,27.3,30.2,27.4,30.8,27.4z M10.7,31.6c-1.3,0-2.6-0.2-3.7-0.7c-1.1-0.5-2.1-1.1-3-1.9    c-0.8-0.8-1.5-1.8-2-2.9c-0.5-1.1-0.7-2.3-0.7-3.5v-0.1c0-1.3,0.2-2.4,0.7-3.5C2.5,18,3.1,17,4,16.2c0.8-0.8,1.8-1.5,3-1.9    c1.2-0.5,2.4-0.7,3.8-0.7c1.3,0,2.6,0.2,3.7,0.7c1.1,0.5,2.1,1.1,3,1.9c0.8,0.8,1.5,1.8,2,2.9c0.5,1.1,0.7,2.3,0.7,3.5v0.1    c0,1.3-0.2,2.4-0.7,3.5c-0.5,1.1-1.1,2.1-2,2.9c-0.8,0.8-1.8,1.5-3,1.9C13.3,31.4,12,31.6,10.7,31.6z M10.7,27.4    c0.7,0,1.4-0.1,1.9-0.4c0.6-0.2,1-0.6,1.4-1c0.4-0.4,0.7-0.9,0.9-1.5c0.2-0.6,0.3-1.2,0.3-1.8v-0.1c0-0.6-0.1-1.3-0.3-1.8    c-0.2-0.6-0.5-1.1-0.9-1.5c-0.4-0.4-0.9-0.8-1.5-1.1c-0.6-0.3-1.2-0.4-1.9-0.4c-0.7,0-1.4,0.1-1.9,0.4c-0.6,0.2-1,0.6-1.4,1    c-0.4,0.4-0.7,0.9-0.9,1.5c-0.2,0.6-0.3,1.2-0.3,1.8v0.1c0,0.6,0.1,1.3,0.3,1.8C6.7,25,7,25.5,7.4,25.9c0.4,0.4,0.9,0.8,1.4,1.1    C9.4,27.3,10.1,27.4,10.7,27.4z"></path>
	</g>
	<g>
		<defs>
			<path id="SVGID_1_" d="M156.1,8.3c-0.2-0.2-0.5-0.5-0.8-0.9v0c-0.1-0.1-0.1-0.1-0.2-0.2v0c-0.2-0.2-0.4-0.4-0.5-0.6     c-0.6-0.6-1.2-1.3-1.7-1.8c0,0-0.1-0.1-0.1-0.1c-0.1-0.1-0.2-0.2-0.3-0.3c0,0,0,0,0,0c0,0,0,0,0,0c0,0-0.1-0.1-0.1-0.1     c-0.1-0.1-0.2-0.2-0.2-0.3c0,0-0.1-0.1-0.1-0.1c0,0-0.1-0.1-0.1-0.1c0,0-0.1-0.1-0.1-0.1c0,0,0,0,0,0s0,0,0,0c-0.1,0.1-1,1-1.9,2     c-0.1,0.1-0.1,0.1-0.2,0.2c-0.1,0.1-0.2,0.2-0.3,0.3l0,0c-0.8,0.8-1.5,1.6-1.9,2.1c-2.4,2.5-2.4,6.6,0,9.1c2,2.1,5,2.4,7.4,1v0     c0.1,0,0.1-0.1,0.2-0.1c0,0,0,0,0.1,0v0c0.3-0.2,0.7-0.5,1-0.8C158.5,14.9,158.5,10.8,156.1,8.3z"></path>
		</defs>
		<clipPath id="SVGID_6_">
			<use xlink:href="#SVGID_1_" style="overflow:visible;"></use>
		</clipPath>
		<ellipse class="st4" cx="145.4" cy="4.8" rx="8.8" ry="9.3"></ellipse>
		<ellipse class="st5" cx="156.3" cy="1.8" rx="8.8" ry="9.3"></ellipse>
		<ellipse class="st6" cx="159.6" cy="18.8" rx="8.8" ry="9.3"></ellipse>
		<ellipse class="st7" cx="147.5" cy="21.6" rx="8.8" ry="9.3"></ellipse>
	</g>
	<g>
		<path class="st3" d="M31.2,34.8h0.9l1.9,4.4h-1l-0.4-1h-1.8l-0.4,1h-1L31.2,34.8z M32.2,37.3l-0.6-1.4L31,37.3H32.2z"></path>
		<path class="st3" d="M36.5,34.8h2c0.5,0,0.9,0.1,1.1,0.4c0.2,0.2,0.3,0.4,0.3,0.7v0c0,0.1,0,0.2,0,0.3s-0.1,0.2-0.1,0.3    c-0.1,0.1-0.1,0.1-0.2,0.2c-0.1,0.1-0.1,0.1-0.2,0.1c0.3,0.1,0.5,0.2,0.6,0.4c0.1,0.2,0.2,0.4,0.2,0.7v0c0,0.2,0,0.4-0.1,0.5    c-0.1,0.1-0.2,0.3-0.3,0.4c-0.1,0.1-0.3,0.2-0.5,0.2c-0.2,0-0.4,0.1-0.7,0.1h-2.1V34.8z M38.3,36.6c0.2,0,0.4,0,0.5-0.1    c0.1-0.1,0.2-0.2,0.2-0.4v0c0-0.1-0.1-0.3-0.2-0.3c-0.1-0.1-0.3-0.1-0.5-0.1h-0.9v0.9H38.3z M38.6,38.3c0.2,0,0.4,0,0.5-0.1    c0.1-0.1,0.2-0.2,0.2-0.4v0c0-0.1-0.1-0.3-0.2-0.3c-0.1-0.1-0.3-0.1-0.5-0.1h-1.1v1H38.6z"></path>
		<path class="st3" d="M41,34.8h2c0.6,0,1,0.1,1.3,0.4c0.2,0.2,0.4,0.6,0.4,1v0c0,0.4-0.1,0.6-0.3,0.9c-0.2,0.2-0.4,0.4-0.7,0.5    l1.1,1.6h-1.1l-0.9-1.4h0h-0.7v1.4h-1V34.8z M42.9,36.9c0.2,0,0.4-0.1,0.5-0.2c0.1-0.1,0.2-0.3,0.2-0.4v0c0-0.2-0.1-0.4-0.2-0.5    c-0.1-0.1-0.3-0.2-0.6-0.2h-1v1.3H42.9z"></path>
		<path class="st3" d="M47,34.8h0.9l1.9,4.4h-1l-0.4-1h-1.8l-0.4,1h-1L47,34.8z M48,37.3l-0.6-1.4l-0.6,1.4H48z"></path>
		<path class="st3" d="M50.4,34.8h0.9l2,2.7v-2.7h0.9v4.4h-0.8l-2.1-2.8v2.8h-0.9V34.8z"></path>
		<path class="st3" d="M55.3,34.8H57c0.3,0,0.7,0.1,0.9,0.2c0.3,0.1,0.5,0.3,0.7,0.5s0.4,0.4,0.5,0.7c0.1,0.3,0.2,0.5,0.2,0.9v0    c0,0.3-0.1,0.6-0.2,0.9c-0.1,0.3-0.3,0.5-0.5,0.7S58.2,38.9,58,39c-0.3,0.1-0.6,0.2-0.9,0.2h-1.7V34.8z M57,38.3    c0.2,0,0.4,0,0.5-0.1c0.2-0.1,0.3-0.2,0.4-0.3c0.1-0.1,0.2-0.3,0.3-0.4c0.1-0.2,0.1-0.3,0.1-0.5v0c0-0.2,0-0.4-0.1-0.5    c-0.1-0.2-0.2-0.3-0.3-0.4c-0.1-0.1-0.3-0.2-0.4-0.3c-0.2-0.1-0.3-0.1-0.5-0.1h-0.7v2.6H57z"></path>
		<path class="st3" d="M62.1,34.8h1l1.1,1.8l1.1-1.8h1v4.4h-0.9v-2.8l-1.2,1.9h0L63,36.3v2.8h-0.9V34.8z"></path>
		<path class="st3" d="M69,34.8h0.9l1.9,4.4h-1l-0.4-1h-1.8l-0.4,1h-1L69,34.8z M70,37.3l-0.6-1.4l-0.6,1.4H70z"></path>
		<path class="st3" d="M72.4,34.8h0.9l2,2.7v-2.7h0.9v4.4h-0.8l-2.1-2.8v2.8h-0.9V34.8z"></path>
		<path class="st3" d="M78.8,34.8h0.9l1.9,4.4h-1l-0.4-1h-1.8l-0.4,1h-1L78.8,34.8z M79.8,37.3l-0.6-1.4l-0.6,1.4H79.8z"></path>
		<path class="st3" d="M84.3,39.2c-0.3,0-0.7-0.1-0.9-0.2c-0.3-0.1-0.5-0.3-0.7-0.5c-0.2-0.2-0.4-0.4-0.5-0.7    C82.1,37.6,82,37.3,82,37v0c0-0.3,0.1-0.6,0.2-0.9c0.1-0.3,0.3-0.5,0.5-0.7c0.2-0.2,0.4-0.4,0.7-0.5s0.6-0.2,0.9-0.2    c0.2,0,0.4,0,0.5,0c0.2,0,0.3,0.1,0.4,0.1c0.1,0,0.3,0.1,0.4,0.2s0.2,0.2,0.3,0.3l-0.6,0.7c-0.1-0.1-0.2-0.1-0.2-0.2    c-0.1-0.1-0.2-0.1-0.3-0.1c-0.1,0-0.2-0.1-0.3-0.1c-0.1,0-0.2,0-0.3,0c-0.2,0-0.3,0-0.5,0.1c-0.2,0.1-0.3,0.2-0.4,0.3    c-0.1,0.1-0.2,0.3-0.3,0.4C83.1,36.6,83,36.8,83,37v0c0,0.2,0,0.4,0.1,0.6c0.1,0.2,0.2,0.3,0.3,0.4c0.1,0.1,0.3,0.2,0.4,0.3    s0.3,0.1,0.5,0.1c0.4,0,0.7-0.1,0.9-0.3v-0.6h-1v-0.8h1.9v1.9c-0.2,0.2-0.5,0.4-0.8,0.5C85.1,39.2,84.7,39.2,84.3,39.2z"></path>
		<path class="st3" d="M87.1,34.8h3.3v0.9H88v0.9h2.1v0.9H88v0.9h2.4v0.9h-3.3V34.8z"></path>
		<path class="st3" d="M91.3,34.8h1l1.1,1.8l1.1-1.8h1v4.4h-0.9v-2.8l-1.2,1.9h0l-1.2-1.8v2.8h-0.9V34.8z"></path>
		<path class="st3" d="M96.7,34.8h3.3v0.9h-2.3v0.9h2.1v0.9h-2.1v0.9h2.4v0.9h-3.3V34.8z"></path>
		<path class="st3" d="M100.8,34.8h0.9l2,2.7v-2.7h0.9v4.4h-0.8l-2.1-2.8v2.8h-0.9V34.8z"></path>
		<path class="st3" d="M106.8,35.7h-1.3v-0.9h3.6v0.9h-1.3v3.5h-1V35.7z"></path>
		<path class="st3" d="M113.7,39.2c-0.3,0-0.6-0.1-0.9-0.2c-0.3-0.1-0.5-0.3-0.7-0.5c-0.2-0.2-0.4-0.4-0.5-0.7    c-0.1-0.3-0.2-0.6-0.2-0.9v0c0-0.3,0.1-0.6,0.2-0.9c0.1-0.3,0.3-0.5,0.5-0.7c0.2-0.2,0.4-0.4,0.7-0.5c0.3-0.1,0.6-0.2,0.9-0.2    c0.2,0,0.4,0,0.6,0c0.2,0,0.3,0.1,0.5,0.1c0.1,0.1,0.3,0.1,0.4,0.2c0.1,0.1,0.2,0.2,0.3,0.3l-0.6,0.7c-0.2-0.2-0.3-0.3-0.5-0.4    c-0.2-0.1-0.4-0.1-0.6-0.1c-0.2,0-0.4,0-0.5,0.1c-0.2,0.1-0.3,0.2-0.4,0.3c-0.1,0.1-0.2,0.3-0.3,0.4s-0.1,0.3-0.1,0.5v0    c0,0.2,0,0.4,0.1,0.5c0.1,0.2,0.1,0.3,0.3,0.4c0.1,0.1,0.2,0.2,0.4,0.3c0.2,0.1,0.3,0.1,0.5,0.1c0.2,0,0.5,0,0.6-0.1    c0.2-0.1,0.3-0.2,0.5-0.4l0.6,0.6c-0.1,0.1-0.2,0.2-0.3,0.3s-0.3,0.2-0.4,0.2c-0.1,0.1-0.3,0.1-0.5,0.2    C114.1,39.2,113.9,39.2,113.7,39.2z"></path>
		<path class="st3" d="M118.4,39.2c-0.3,0-0.6-0.1-0.9-0.2c-0.3-0.1-0.5-0.3-0.7-0.5c-0.2-0.2-0.4-0.4-0.5-0.7    c-0.1-0.3-0.2-0.6-0.2-0.9v0c0-0.3,0.1-0.6,0.2-0.9c0.1-0.3,0.3-0.5,0.5-0.7c0.2-0.2,0.5-0.4,0.7-0.5s0.6-0.2,0.9-0.2    c0.3,0,0.6,0.1,0.9,0.2c0.3,0.1,0.5,0.3,0.7,0.5c0.2,0.2,0.4,0.4,0.5,0.7c0.1,0.3,0.2,0.6,0.2,0.9v0c0,0.3-0.1,0.6-0.2,0.9    c-0.1,0.3-0.3,0.5-0.5,0.7c-0.2,0.2-0.5,0.4-0.7,0.5C119,39.2,118.7,39.2,118.4,39.2z M118.4,38.4c0.2,0,0.4,0,0.5-0.1    c0.2-0.1,0.3-0.2,0.4-0.3c0.1-0.1,0.2-0.3,0.3-0.4c0.1-0.2,0.1-0.3,0.1-0.5v0c0-0.2,0-0.4-0.1-0.5c-0.1-0.2-0.2-0.3-0.3-0.4    c-0.1-0.1-0.3-0.2-0.4-0.3c-0.2-0.1-0.3-0.1-0.5-0.1c-0.2,0-0.4,0-0.5,0.1c-0.2,0.1-0.3,0.2-0.4,0.3c-0.1,0.1-0.2,0.3-0.3,0.4    c-0.1,0.2-0.1,0.3-0.1,0.5v0c0,0.2,0,0.4,0.1,0.5c0.1,0.2,0.2,0.3,0.3,0.4c0.1,0.1,0.3,0.2,0.4,0.3    C118,38.3,118.2,38.4,118.4,38.4z"></path>
		<path class="st3" d="M121.5,34.8h1l1.1,1.8l1.1-1.8h1v4.4H125v-2.8l-1.2,1.9h0l-1.2-1.8v2.8h-0.9V34.8z"></path>
		<path class="st3" d="M127,34.8h1.8c0.3,0,0.5,0,0.7,0.1c0.2,0.1,0.4,0.2,0.5,0.3c0.1,0.1,0.3,0.3,0.3,0.5c0.1,0.2,0.1,0.4,0.1,0.6    v0c0,0.3,0,0.5-0.1,0.7c-0.1,0.2-0.2,0.4-0.4,0.5c-0.2,0.1-0.3,0.2-0.6,0.3c-0.2,0.1-0.4,0.1-0.7,0.1h-0.7v1.3h-1V34.8z M128.7,37    c0.2,0,0.4-0.1,0.6-0.2c0.1-0.1,0.2-0.3,0.2-0.5v0c0-0.2-0.1-0.4-0.2-0.5c-0.1-0.1-0.3-0.2-0.6-0.2h-0.7V37H128.7z"></path>
		<path class="st3" d="M132.6,34.8h0.9l1.9,4.4h-1l-0.4-1h-1.8l-0.4,1h-1L132.6,34.8z M133.6,37.3l-0.6-1.4l-0.6,1.4H133.6z"></path>
		<path class="st3" d="M136,34.8h0.9l2,2.7v-2.7h0.9v4.4h-0.8l-2.1-2.8v2.8H136V34.8z"></path>
		<path class="st3" d="M142.2,37.4l-1.7-2.6h1.1l1,1.7l1.1-1.7h1.1l-1.7,2.6v1.7h-1V37.4z"></path>
	</g>
</g>
</svg>
