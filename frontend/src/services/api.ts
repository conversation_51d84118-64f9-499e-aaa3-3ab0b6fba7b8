import { AuthResponse, ChatApiResponse, ChatHistoryItem, CompanyApiResponse, FilterOptions, LoginCredentials} from '../types';

const baseUrl = import.meta.env.VITE_BACKEND_URL;


export const loginUser = async (credentials: LoginCredentials): Promise<AuthResponse> => {
  try {
     const response = await fetch(`${baseUrl}/auth/login`, {
       method: 'POST',
       headers: { 'Content-Type': 'application/json' },
       body: JSON.stringify(credentials),
     });
     return await response.json();
  } catch (error) {
    console.error('<PERSON><PERSON> failed:', error);
    throw new Error('<PERSON><PERSON> failed. Please check your credentials and try again.');
  }
};

// Chat API
export const sendChatMessage = async (
  message: string, 
  filters: FilterOptions & { chatId?: string }
): Promise<{ data: ChatApiResponse; chatId?: string; chatTitle?: string }> => {
  try {
    const user = localStorage.getItem('user');
    const username = user ? JSON.parse(user).username : '';
    const query = encodeURIComponent(message);
    const requiredResults = filters.requiredResults || 10;
    const neighbours = filters.neighbours || 1000;
    const productSource = filters.productSource || 'all';
    const url = new URL(`${baseUrl}/search/product`);
    const companies = filters.companies || [];

    url.searchParams.append('query', query);
    url.searchParams.append('requiredResults', requiredResults.toString());
    url.searchParams.append('neighbours', neighbours.toString());
    url.searchParams.append('useContextual', 'true');
    url.searchParams.append('companyNames', companies.join(','));

    if (productSource === 'optamark') {
      url.searchParams.append('isOptamark', 'true');
    } else if (productSource === 'non-optamark') {
      url.searchParams.append('isOptamark', 'false');
    }

    const chatId = filters.chatId || "";

    const response = await fetch(url.toString(), {
      method: 'GET',
      headers: {
        'X-Username': username,
        'chatId': chatId,
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
      }
    });

    const data = await response.json();
    const returnedChatId = response.headers.get('Chat-Id');
    const chatTitle = response.headers.get('Chat-Title');

    return { data, chatId: returnedChatId || undefined, chatTitle: chatTitle || undefined };
  } catch (error) {
    console.error('Failed to send message:', error);
    throw new Error('Failed to send message. Please try again.');
  }
};


export const getChatSession = async (sessionId: string): Promise<any[]> => {
  try {
    const user = localStorage.getItem('user');
    const username = user ? JSON.parse(user).username : '';
    const response = await fetch(`${baseUrl}/history/messages`, {
      method: 'GET',
      headers: {
        'X-Username': username,
        'chatId': sessionId,
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
      },
    });
    if (!response.ok) throw new Error('Failed to fetch chat session');
    return await response.json();
  } catch (error) {
    console.error('Failed to fetch chat session:', error);
    throw new Error('Failed to load chat session. Please try again.');
  }
};

export const fetchChatHistory = async (username: string): Promise<ChatHistoryItem[]> => {
  const response = await fetch(`${baseUrl}/history/list`, {
    headers: {
      'X-Username': username,
      'Authorization': `Bearer ${localStorage.getItem('token')}`,
    },
  });
  if (!response.ok) throw new Error('Failed to fetch chat history');
  return await response.json();
};

export const deleteChatHistory = async (chatId: string): Promise<void> => {
  const user = localStorage.getItem('user');
  const username = user ? JSON.parse(user).username : '';
  const response = await fetch(`${baseUrl}/history/delete`, {
    method: 'DELETE',
    headers: {
      'X-Username': username,
      'chatId': chatId,
      'Authorization': `Bearer ${localStorage.getItem('token')}`,
    },
  });
  if (!response.ok) throw new Error('Failed to delete chat history');
};

export const renameChatTitle = async (chatId: string, title: string): Promise<void> => {
  const user = localStorage.getItem('user');
  const username = user ? JSON.parse(user).username : '';
  const response = await fetch(`${baseUrl}/history/update-title?title=${encodeURIComponent(title)}`, {
    method: "PUT",
    headers: {
      "X-Username": username,
      "chatId": chatId,
      "Authorization": `Bearer ${localStorage.getItem('token')}`,
    },
  });
  if (!response.ok) throw new Error("Failed to rename chat");
};

export const fetchCompanies = async (): Promise<string[]> => {
  try {
    const response = await fetch(`${baseUrl}/company/names`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Content-Type': 'application/json',
      },
    });
    
    if (!response.ok) throw new Error('Failed to fetch companies');
    
    const data: CompanyApiResponse[] = await response.json();
    
    if (Array.isArray(data)) {
      return data
        .filter((item): item is CompanyApiResponse => item && typeof item === 'object' && 'name' in item && typeof item.name === 'string')
        .map(item => item.name.trim())
        .filter(name => name.length > 0);
    }
    
    return [];
  } catch (error) {
    console.error('Failed to fetch companies:', error);
    throw new Error('Failed to fetch companies. Please try again.');
  }
};

export const searchProductBySPC = async (spcCode: string, chatId?: string): Promise<any> => {
  try {
    console.log('API: searchProductBySPC called with:', spcCode, chatId); 
    
    const user = localStorage.getItem('user');
    const username = user ? JSON.parse(user).username : '';
    
    const url = new URL(`${baseUrl}/search/product/spc`);
    url.searchParams.append('spc', spcCode);
    
    const response = await fetch(url.toString(), {
      method: 'GET',
      headers: {
        'X-Username': username,
        'chatId': chatId || '',
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Content-Type': 'application/json',
      }
    });

    console.log('API: SPC search response status:', response.status);
    if (!response.ok) throw new Error('SPC search failed');
    
    const data = await response.json();
    const returnedChatId = response.headers.get('Chat-Id');
    const chatTitle = response.headers.get('Chat-Title');
    
    console.log('API: SPC search result:', data);
    return { data, chatId: returnedChatId || undefined, chatTitle: chatTitle || undefined };
  } catch (error) {
    console.error('SPC search error:', error);
    throw error;
  }
};

export const searchProductByItemNum = async (itemNum: string, chatId?: string): Promise<any> => {
  try {    
    const user = localStorage.getItem('user');
    const username = user ? JSON.parse(user).username : '';

    const url = new URL(`${baseUrl}/search/product/item-num`);
    url.searchParams.append('itemNum', itemNum);
    
    const response = await fetch(url.toString(), {
      method: 'GET',
      headers: {
        'X-Username': username,
        'chatId': chatId || '',
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Content-Type': 'application/json',
      },
    });

    console.log('API: Item search response status:', response.status); 
    if (!response.ok) throw new Error('Product search failed');
    
    const data = await response.json();
    const returnedChatId = response.headers.get('Chat-Id');
    const chatTitle = response.headers.get('Chat-Title');
    
    console.log('API: Item search result:', data);
    return { data, chatId: returnedChatId || undefined, chatTitle: chatTitle || undefined };
  } catch (error) {
    console.error('Product search error:', error);
    throw error;
  }
};


