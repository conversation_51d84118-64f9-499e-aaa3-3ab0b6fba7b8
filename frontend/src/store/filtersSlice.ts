import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { FilterOptions } from '../types';

const initialState: FilterOptions = {
  requiredResults: 10,
  neighbours: 1000,
  productSource: 'all',
  companies: []
};

const filtersSlice = createSlice({
  name: 'filters',
  initialState,
  reducers: {
    updateFilters: (state, action: PayloadAction<Partial<FilterOptions>>) => {
      return { ...state, ...action.payload };
    },
    updateCompanies: (state, action: PayloadAction<string[]>) => {
      state.companies = action.payload;
    },
    clearFilters: () => {
      return initialState;
    },
    setRequiredResults: (state, action: PayloadAction<number>) => {
      state.requiredResults = action.payload;
    },
    setProductSource: (state, action: PayloadAction<'all' | 'optamark' | 'non-optamark'>) => {
      state.productSource = action.payload;
    },
    setNeighbours: (state, action: PayloadAction<number>) => {
      state.neighbours = action.payload;
    }
  }
});

export const { 
  updateFilters, 
  updateCompanies, 
  clearFilters, 
  setRequiredResults, 
  setProductSource,
  setNeighbours
} = filtersSlice.actions;

export default filtersSlice.reducer;