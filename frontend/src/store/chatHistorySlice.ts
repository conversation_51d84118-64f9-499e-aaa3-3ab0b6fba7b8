import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { ChatHistoryItem } from '../types';

interface ChatHistoryState {
  chatHistory: ChatHistoryItem[];
}

const initialState: ChatHistoryState = {
  chatHistory: [],
};

const chatHistorySlice = createSlice({
  name: 'chatHistory',
  initialState,
  reducers: {
    setChatHistory(state, action: PayloadAction<ChatHistoryItem[]>) {
      state.chatHistory = action.payload;
    },
    addChat(state, action: PayloadAction<ChatHistoryItem>) {
      state.chatHistory.unshift(action.payload);
    },
    removeChat(state, action: PayloadAction<string>) {
      state.chatHistory = state.chatHistory.filter(chat => chat.chatId !== action.payload);
    },
    updateChat(state, action: PayloadAction<{ chatId: string; updatedAt: number }>) {
      const chat = state.chatHistory.find(c => c.chatId === action.payload.chatId);
      if (chat) chat.updatedAt = action.payload.updatedAt;
    },
  },
});

export const { setChatHistory, addChat, removeChat, updateChat } = chatHistorySlice.actions;
export default chatHistorySlice.reducer;