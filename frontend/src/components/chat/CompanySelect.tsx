import React, { useState, useEffect, useRef } from 'react';
import { ChevronDown, Search, X } from 'lucide-react';
import { useDispatch, useSelector } from 'react-redux';
import { fetchCompanies } from '../../services/api';
import { updateCompanies } from '../../store/filtersSlice';
import { RootState } from '../../store';

const CompanySelect: React.FC = () => {
  const dispatch = useDispatch();
  const selectedCompanies = useSelector((state: RootState) => state.filters.companies);
  
  const [isOpen, setIsOpen] = useState(false);
  const [companies, setCompanies] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [dropdownPosition, setDropdownPosition] = useState<'bottom' | 'top'>('bottom');
  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const loadCompanies = async () => {
      setLoading(true);
      try {
        const companiesData = await fetchCompanies();
        setCompanies(companiesData);
      } catch (error) {
        console.error('Failed to load companies:', error);
        setCompanies([]);
      } finally {
        setLoading(false);
      }
    };

    if (isOpen && companies.length === 0) {
      loadCompanies();
    }
  }, [isOpen, companies.length]);

  useEffect(() => {
    if (isOpen && dropdownRef.current) {
      const rect = dropdownRef.current.getBoundingClientRect();
      const windowHeight = window.innerHeight;
      const dropdownHeight = 240;
      const spaceBelow = windowHeight - rect.bottom;
      const spaceAbove = rect.top;

      if (spaceBelow < dropdownHeight && spaceAbove > dropdownHeight) {
        setDropdownPosition('top');
      } else {
        setDropdownPosition('bottom');
      }
    }
  }, [isOpen]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setSearchTerm('');
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const filteredCompanies = companies
    .filter(company => company && typeof company === 'string')
    .filter(company => 
      company.toLowerCase().includes(searchTerm.toLowerCase())
    );

  const handleToggleCompany = (company: string) => {
    if (selectedCompanies.includes(company)) {
      dispatch(updateCompanies(selectedCompanies.filter(c => c !== company)));
    } else {
      dispatch(updateCompanies([...selectedCompanies, company]));
    }
  };

  const handleClearAll = () => {
    dispatch(updateCompanies([]));
    setIsOpen(false);
    setSearchTerm('');
  };

  const removeCompany = (company: string) => {
    dispatch(updateCompanies(selectedCompanies.filter(c => c !== company)));
  };

  const handleDropdownToggle = () => {
    setIsOpen(!isOpen);
    if (!isOpen) {
      setSearchTerm('');
    }
  };

  return (
    <div className="relative" ref={dropdownRef}>
      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
        Companies
      </label>
      
      {/* Selected Companies Tags */}
      {selectedCompanies.length > 0 && (
        <div className="flex flex-wrap gap-2 mb-2">
          {selectedCompanies.map((company, index) => (
            <span
              key={`${company}-${index}`}
              className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
            >
              {company}
              <button
                type="button"
                onClick={() => removeCompany(company)}
                className="ml-1 inline-flex items-center justify-center w-4 h-4 rounded-full hover:bg-blue-200 dark:hover:bg-blue-800 transition-colors"
                title={`Remove ${company}`}
              >
                <X size={12} />
              </button>
            </span>
          ))}
        </div>
      )}
      
      <button
        type="button"
        onClick={handleDropdownToggle}
        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md bg-white dark:bg-gray-800 text-left focus:ring-blue-500 focus:border-blue-500 focus:outline-none flex items-center justify-between transition-colors"
      >
        <span className={selectedCompanies.length > 0 ? 'text-gray-900 dark:text-white' : 'text-gray-500 dark:text-gray-400'}>
          {selectedCompanies.length > 0 
            ? `${selectedCompanies.length} ${selectedCompanies.length === 1 ? 'company' : 'companies'} selected`
            : 'Select companies...'
          }
        </span>
        <ChevronDown 
          size={16} 
          className={`transform transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`} 
        />
      </button>

      {isOpen && (
        <div 
          className={`absolute z-50 w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-700 rounded-md shadow-lg max-h-60 overflow-hidden ${
            dropdownPosition === 'top' 
              ? 'bottom-full mb-1'
              : 'top-full mt-1'
          }`}
        >
          {/* Search Input */}
          <div className="p-2 border-b border-gray-200 dark:border-gray-700">
            <div className="relative">
              <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Search companies..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-sm focus:ring-blue-500 focus:border-blue-500 focus:outline-none"
                autoFocus
              />
            </div>
          </div>

          {/* Clear All Option */}
          {selectedCompanies.length > 0 && (
            <button
              type="button"
              onClick={handleClearAll}
              className="w-full px-3 py-2 text-left hover:bg-gray-100 dark:hover:bg-gray-700 text-red-600 dark:text-red-400 flex items-center border-b border-gray-200 dark:border-gray-700 transition-colors"
            >
              <X size={16} className="mr-2" />
              Clear all selections
            </button>
          )}

          {/* Company List */}
          <div className="max-h-40 overflow-y-auto">
            {loading ? (
              <div className="px-3 py-2 text-center text-gray-500">
                <div className="flex items-center justify-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
                  Loading companies...
                </div>
              </div>
            ) : filteredCompanies.length === 0 ? (
              <div className="px-3 py-2 text-center text-gray-500">
                {searchTerm ? `No companies found for "${searchTerm}"` : 'No companies available'}
              </div>
            ) : (
              filteredCompanies.map((company, index) => (
                <label
                  key={`${company}-${index}`}
                  className="flex items-center px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer transition-colors"
                >
                  <input
                    type="checkbox"
                    checked={selectedCompanies.includes(company)}
                    onChange={() => handleToggleCompany(company)}
                    className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
                  />
                  <span className="ml-2 text-sm text-gray-900 dark:text-white flex-1">
                    {company}
                  </span>
                </label>
              ))
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default CompanySelect;