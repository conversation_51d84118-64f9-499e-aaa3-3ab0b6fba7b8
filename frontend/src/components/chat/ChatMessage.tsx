import React from 'react';
import { ChatMessage as ChatMessageType } from '../../types';
import ProductCard from './ProductCard';
import { BotIcon, User } from 'lucide-react';
import { format } from 'date-fns';

interface ChatMessageProps {
  message: ChatMessageType;
}

const ChatMessage: React.FC<ChatMessageProps> = ({ message }) => {
  const isBot = message.type === 'bot';
  //const timestamp = new Date(message.timestamp);
  const formattedTime = format(new Date(), 'p');
  
  return (
    <div className={`py-4 px-4 md:px-8 ${isBot ? 'bg-gray-50 dark:bg-gray-800/50' : ''} message-appear`}>
      <div className="max-w-4xl mx-auto">
        <div className="flex items-start gap-4">
          <div className="flex-shrink-0">
            {isBot ? (
              <div className="w-8 h-8 rounded-full bg-blue-600 text-white flex items-center justify-center">
                <BotIcon size={18} />
              </div>
            ) : (
              <div className="w-8 h-8 rounded-full bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-300 flex items-center justify-center">
                <User size={18} />
              </div>
            )}
          </div>
          
          <div className="flex-grow">
            <div className="flex items-center">
              <span className="font-medium text-gray-900 dark:text-white">
                {isBot ? 'Optamark AI' : 'You'}
              </span>
              <span className="ml-2 text-xs text-gray-500 dark:text-gray-400">
                {formattedTime}
              </span>
            </div>
            
            <div className="mt-1 text-gray-800 dark:text-gray-200">
              {message.content}
            </div>
            
            {isBot && message.suggestions && message.suggestions.length > 0 && (
              <div className="mt-4 grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
                {message.suggestions.map((suggestion) => (
                  <ProductCard 
                    key={suggestion.productID}
                    product={suggestion} 
                  />
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChatMessage;