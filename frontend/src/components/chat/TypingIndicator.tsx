import React from 'react';
import { BotIcon } from 'lucide-react';

const TypingIndicator: React.FC = () => {
  return (
    <div className="py-4 px-4 md:px-8 bg-gray-50 dark:bg-gray-800/50 message-appear">
      <div className="max-w-4xl mx-auto">
        <div className="flex items-start gap-4">
          <div className="flex-shrink-0">
            <div className="w-8 h-8 rounded-full bg-blue-600 text-white flex items-center justify-center">
              <BotIcon size={18} />
            </div>
          </div>
          
          <div className="flex-grow">
            <div className="flex items-center">
              <span className="font-medium text-gray-900 dark:text-white">
                Optamark AI
              </span>
            </div>
            
            <div className="mt-1 text-gray-800 dark:text-gray-200 typing-indicator">
              <span>•</span>
              <span>•</span>
              <span>•</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TypingIndicator;