import React, { useState, useRef } from 'react';
import { ArrowUp, Settings, X } from 'lucide-react';
import { FilterOptions } from '../../types';
import CompanySelect from './CompanySelect';
import { parseSearchCommand, getCommandSuggestions, SearchCommand } from '../../utils/commandParser';

interface ChatInputProps {
  onSendMessage: (message: string, command?: SearchCommand) => void;
  isLoading: boolean;
  filters: FilterOptions;
  onFiltersChange: (filters: FilterOptions) => void;
  showFilters: boolean;
  setShowFilters: (show: boolean) => void;
}

const ChatInput: React.FC<ChatInputProps> = ({ 
  onSendMessage, 
  isLoading, 
  filters, 
  onFiltersChange,
  showFilters,
  setShowFilters
}) => {
  const [message, setMessage] = useState('');
  const [resultsInput, setResultsInput] = useState(filters.requiredResults.toString());
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [selectedSuggestion, setSelectedSuggestion] = useState(0);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (message.trim() && !isLoading) {
      const command = parseSearchCommand(message.trim());
      onSendMessage(message, command);
      setMessage('');
      setShowSuggestions(false);
    }
  };
  
  const toggleFilters = () => {
    setShowFilters(!showFilters);
  };
  
  const handleResultsChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/\D/g, '');
    if (value === '' || Number(value) <= 50) {
      setResultsInput(value);
      if (value !== '') {
        onFiltersChange({ ...filters, requiredResults: Number(value) });
      }
    }
  };

  const handleResultsBlur = () => {
    if (resultsInput === '' || Number(resultsInput) < 1) {
      setResultsInput('3');
      onFiltersChange({ ...filters, requiredResults: 1 });
    }
  };

  const handleMessageChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    setMessage(value);

    if (value.startsWith('/')) {
      const commandSuggestions = getCommandSuggestions(value);
      setSuggestions(commandSuggestions);
      setShowSuggestions(commandSuggestions.length > 0);
      setSelectedSuggestion(0);
    } else {
      setShowSuggestions(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (showSuggestions && suggestions.length > 0) {
      if (e.key === 'ArrowDown') {
        e.preventDefault();
        setSelectedSuggestion((prev) => 
          prev < suggestions.length - 1 ? prev + 1 : 0
        );
      } else if (e.key === 'ArrowUp') {
        e.preventDefault();
        setSelectedSuggestion((prev) => 
          prev > 0 ? prev - 1 : suggestions.length - 1
        );
      } else if (e.key === 'Tab' || e.key === 'Enter') {
        if (suggestions[selectedSuggestion]) {
          e.preventDefault();
          setMessage(suggestions[selectedSuggestion] + ' ');
          setShowSuggestions(false);
          return;
        }
      } else if (e.key === 'Escape') {
        setShowSuggestions(false);
      }
    }

    if (e.key === 'Enter' && !e.shiftKey && !showSuggestions) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  const selectSuggestion = (suggestion: string) => {
    setMessage(suggestion + ' ');
    setShowSuggestions(false);
    textareaRef.current?.focus();
  };

  const currentCommand = parseSearchCommand(message);
  const isCommand = currentCommand.type !== 'normal';

  return (
    <div className="border-t border-gray-200 dark:border-gray-800 p-4 bg-white dark:bg-gray-900">
      {showFilters && (
        <div className="mb-4 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg slide-in">
          <div className="flex justify-between items-center mb-3">
            <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300">Search Filters</h3>
            <button 
              onClick={toggleFilters}
              className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-white"
            >
              <X size={18} />
            </button>
          </div>
          
          <div className="space-y-4">
            <CompanySelect/>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Number of Results
              </label>
              <input
                type="text"
                value={resultsInput}
                onChange={handleResultsChange}
                onBlur={handleResultsBlur}
                placeholder="Enter a number (max 50)"
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-white text-sm"
              />
              <span className="text-xs text-gray-500 dark:text-gray-400">You can enter numbers only (max 50).</span>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Product Source
              </label>
              <div className="space-y-2">
                <label className="flex items-center">
                  <input
                    type="radio"
                    checked={filters.productSource === 'all'}
                    onChange={() => onFiltersChange({ ...filters, productSource: 'all' })}
                    className="h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                  />
                  <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">All Products</span>
                </label>
                
                <label className="flex items-center">
                  <input
                    type="radio"
                    checked={filters.productSource === 'optamark'}
                    onChange={() => onFiltersChange({ ...filters, productSource: 'optamark' })}
                    className="h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                  />
                  <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">Optamark Products Only</span>
                </label>
                
                <label className="flex items-center">
                  <input
                    type="radio"
                    checked={filters.productSource === 'non-optamark'}
                    onChange={() => onFiltersChange({ ...filters, productSource: 'non-optamark' })}
                    className="h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                  />
                  <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">Non-Optamark Products Only</span>
                </label>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Command Info Bar */}
      {isCommand && (
        <div className="mb-2 p-2 bg-blue-50 dark:bg-blue-900/30 rounded-md">
          <span className="text-xs font-medium text-blue-700 dark:text-blue-300">
            {currentCommand.type.toUpperCase()} Command: {currentCommand.query || 'Enter search parameters'}
          </span>
        </div>
      )}
      
      <form onSubmit={handleSubmit} className="flex items-end gap-2">
        <button
          type="button"
          onClick={toggleFilters}
          className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-white rounded-full hover:bg-gray-100 dark:hover:bg-gray-800"
          title="Search Filters"
        >
          <Settings size={20} />
        </button>
        
        <div className="flex-grow relative">
          <textarea
            ref={textareaRef}
            value={message}
            onChange={handleMessageChange}
            onKeyDown={handleKeyDown}
            placeholder="Ask about products... (Type / for commands: /spc, /item)"
            className="w-full p-3 pr-10 border border-gray-300 dark:border-gray-700 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-white resize-none"
            rows={1}
            disabled={isLoading}
            onFocus={() => setShowFilters(false)}
          />

          {/* Command Suggestions */}
          {showSuggestions && suggestions.length > 0 && (
            <div className="absolute bottom-full left-0 right-0 mb-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-700 rounded-md shadow-lg z-10">
              {suggestions.map((suggestion, index) => (
                <div
                  key={suggestion}
                  className={`px-3 py-2 cursor-pointer text-sm ${
                    index === selectedSuggestion
                      ? 'bg-blue-50 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300'
                      : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700'
                  }`}
                  onClick={() => selectSuggestion(suggestion)}
                >
                  <span className="font-medium">{suggestion}</span>
                  <span className="ml-2 text-xs text-gray-500">
                    {suggestion === '/spc' && 'Search by SPC code'}
                    {suggestion === '/item' && 'Search by item code'}
                  </span>
                </div>
              ))}
            </div>
          )}
          
          <button
            type="submit"
            disabled={!message.trim() || isLoading}
            className="absolute right-2 bottom-2 p-2 text-white bg-blue-600 rounded-full hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <ArrowUp size={18} />
          </button>
        </div>
      </form>

      {/* Command Help */}
      <div className="mt-2 text-xs text-gray-500 dark:text-gray-400">
        Commands: <span className="font-mono">/SPC [SPC code]</span> • <span className="font-mono">/ITEM [Item code]</span>
      </div>
    </div>
  );
};

export default ChatInput;