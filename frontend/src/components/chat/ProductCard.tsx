import React, { useState } from 'react';
import { ProductSuggestion } from '../../types';
import { ExternalLink, Copy } from 'lucide-react';

interface ProductCardProps {
  product: ProductSuggestion;
}

const ProductCard: React.FC<ProductCardProps> = ({ product }) => {
  const [copied, setCopied] = useState(false);

  const handleCopy = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopied(true);
      setTimeout(() => setCopied(false), 1000);
    } catch (err) {
      setCopied(false);
    }
  };

  return (
    <div className="product-card overflow-hidden rounded-lg border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900 shadow-sm">
      {product.url && (
        <div className="aspect-video w-full overflow-hidden bg-gray-100 dark:bg-gray-800">
          {product.thumbPicLink ? (
            <img 
              src={product.thumbPicLink}
              alt={product.prName}
              className="w-full h-full object-cover"
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.style.display = 'none';
                const parent = target.parentElement;
                if (parent) {
                  parent.innerHTML = `
                    <div class="w-full h-full flex items-center justify-center bg-gray-50 dark:bg-gray-800">
                      <div class="text-center">
                        <svg class="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                        <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">No image available</p>
                      </div>
                    </div>
                  `;
                }
              }}
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center bg-gray-50 dark:bg-gray-800">
              <div className="text-center">
                <svg className="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
                <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">No image available</p>
              </div>
            </div>
          )}
        </div>
      )}
      
      <div className="p-4">
        <div className="flex items-start justify-between">
          <h3 className="text-sm font-medium text-gray-900 dark:text-white line-clamp-2">
            {product.prName}
          </h3>
          {product.isOptamark && (
            <span className="inline-flex items-center rounded-full bg-blue-100 dark:bg-blue-900 px-2 py-0.5 text-xs font-medium text-blue-800 dark:text-blue-200">
              Optamark
            </span>
          )}
        </div>

        {product.companyName && product.companyName.trim() !== '' && (
          <div className="mt-1 text-xs text-gray-600 dark:text-gray-400">
            <span className="font-semibold">Company:</span> {product.companyName}
          </div>
        )}
        {product.spc && product.spc.trim() !== '' && (
          <div className="text-xs text-gray-600 dark:text-gray-400 flex items-center">
            <span className="font-semibold">SPC:</span> {product.spc}
            <button
              onClick={() => handleCopy(product.spc!)}
              className="ml-2 p-1 rounded hover:bg-gray-200 dark:hover:bg-gray-700"
              title="Copy SPC"
              type="button"
            >
              <Copy size={14} />
            </button>
            {copied && (
              <span className="ml-1 text-green-500 text-xs">Copied!</span>
            )}
          </div>
        )}
        {product.itemNum && product.itemNum.toString().trim() !== '' && (
          <div className="text-xs text-gray-600 dark:text-gray-400">
            <span className="font-semibold">Item #:</span> {product.itemNum}
          </div>
        )}

        <p className="mt-2 text-xs text-gray-600 dark:text-gray-400 line-clamp-3">
          {product.description}
        </p>

        <div className="mt-2 flex items-center justify-between">
          {(product.minPrc || product.maxPrc) && (
            <p className="text-sm font-semibold text-gray-900 dark:text-white">
              ${product.minPrc}
              {product.maxPrc &&
                product.minPrc !== product.maxPrc &&
                ` - $${product.maxPrc}`}
            </p>
          )}

          <a
            href={product.url}
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center rounded-md bg-blue-50 dark:bg-blue-900/40 px-2 py-1 text-xs font-medium text-blue-700 dark:text-blue-300 hover:bg-blue-100 dark:hover:bg-blue-900"
          >
            View <ExternalLink size={12} className="ml-1" />
          </a>
        </div>
      </div>
    </div>
  );
};

export default ProductCard;