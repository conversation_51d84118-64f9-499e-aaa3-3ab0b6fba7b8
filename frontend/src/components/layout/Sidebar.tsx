import React, { useEffect, useState, useRef } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { User, Plus, Trash2, Pencil, Loader } from 'lucide-react';
import { useAuth } from "../../context/AuthContext";
import { fetchChatHistory, deleteChatHistory, renameChatTitle } from "../../services/api";
import { useDispatch, useSelector } from "react-redux";
import { removeChat, setChatHistory } from "../../store/chatHistorySlice";
import { clearFilters } from "../../store/filtersSlice";
import { RootState } from "../../store";

interface SidebarProps {
  isOpen: boolean;
  onClose: () => void;
}

const SidebarContent: React.FC<{
  user: any;
  logout: () => void;
  location: any;
  onClose?: () => void;
}> = ({ user, logout, location, onClose }) => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const chatHistory = useSelector(
    (state: RootState) => state.chatHistory.chatHistory
  );
  const [loading, setLoading] = useState(false);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [editTitle, setEditTitle] = useState<string>('');
  const [pendingDeleteId, setPendingDeleteId] = useState<string | null>(null);
  const [deleting, setDeleting] = useState(false);
  const renameTimeout = useRef<number | null>(null);

  useEffect(() => {
    const getHistory = async () => {
      if (!user?.username) return;
      setLoading(true);
      try {
        const history = await fetchChatHistory(user.username);
        dispatch(setChatHistory(history));
      } catch (e) {
        dispatch(setChatHistory([]));
      } finally {
        setLoading(false);
      }
    };
    getHistory();
  }, [user, dispatch]);

  const handleDelete = async (id: string) => {
    try {
      await deleteChatHistory(id);
      dispatch(removeChat(id)); 
      if (location.pathname === `/history/${id}`) {
        navigate("/");
      }
    } catch (error) {
      console.error('Failed to delete chat:', error);
    }
  };

  const handleRename = (id: string, currentTitle: string) => {
    setEditingId(id);
    setEditTitle(currentTitle);
  };

  // Debounced input handler
  const handleRenameInput = (chatId: string, value: string) => {
    setEditTitle(value);
    if (renameTimeout.current) clearTimeout(renameTimeout.current);
    renameTimeout.current = setTimeout(async () => {
      if (value.trim()) {
        try {
          await renameChatTitle(chatId, value.trim());
          dispatch(setChatHistory(
            chatHistory.map(chat =>
              chat.chatId === chatId ? { ...chat, title: value.trim() } : chat
            )
          ));
        } catch (error) {
          console.error(error);
        }
      }
    }, 600);
  };

  const handleNewChatClick = () => {
    dispatch(clearFilters());
    if (onClose) onClose();
    navigate("/");
  };

  const handleChatHistoryClick = (chatId: string) => {
    dispatch(clearFilters());
    if (onClose) onClose();
    navigate(`/history/${chatId}`);
  };

  return (
    <div className="pt-5 pb-4 px-4 flex flex-col h-full">
      {/* Logo */}
      <div className="flex items-center mb-6 flex-nowrap">
        <img
          src="/logo-light.svg"
          alt="Optamark Logo"
          className="block dark:hidden h-8"
        />
        <img
          src="/logo-dark.svg"
          alt="Optamark Logo"
          className="hidden dark:block h-8"
        />
        <span className="ml-2 text-xl font-semibold text-gray-900 dark:text-white whitespace-nowrap">
          AI Search
        </span>
      </div>

      {/* New Chat Button - Updated to use new handler */}
      <button
        className={`flex items-center w-full px-4 py-3 mb-2 text-base font-medium rounded-md bg-blue-600 text-white hover:bg-blue-700 transition`}
        onClick={handleNewChatClick}
      >
        <Plus size={20} className="mr-3" />
        New Chat
      </button>

      {/* Chat History List */}
      <div className="flex-grow overflow-y-auto">
        <nav className="space-y-1">
          {loading ? (
            <div className="flex justify-center items-center py-6">
              <Loader
                size={20}
                className="text-blue-600 dark:text-blue-400 animate-spin"
              />
            </div>
          ) : chatHistory.length === 0 ? (
            <div className="text-gray-500 text-sm px-4 py-2">
              No chat history found.
            </div>
          ) : (
            chatHistory.map((chat) => (
              <div
                key={chat.chatId}
                className={`group flex items-center px-4 py-3 rounded-md cursor-pointer ${
                  location.pathname === `/history/${chat.chatId}`
                    ? "bg-blue-50 text-blue-700 dark:bg-blue-900/30 dark:text-blue-200"
                    : "text-gray-600 hover:bg-gray-50 dark:text-gray-300 dark:hover:bg-gray-800"
                }`}
                onClick={() => handleChatHistoryClick(chat.chatId)}
              >
                {editingId === chat.chatId ? (
                  <input
                    type="text"
                    value={editTitle}
                    onChange={(e) => handleRenameInput(chat.chatId, e.target.value)}
                    onBlur={() => setEditingId(null)}
                    className="w-full px-2 py-1 border border-gray-300 dark:border-gray-700 bg-gray-100 dark:bg-gray-800 rounded focus:outline-none focus:ring-2 focus:ring-blue-400 text-base"
                    autoFocus
                    onKeyDown={async (e) => {
                      if (e.key === "Enter") {
                        if (editTitle.trim()) {
                          try {
                            await renameChatTitle(chat.chatId, editTitle.trim());
                            dispatch(setChatHistory(
                              chatHistory.map(chat =>
                                chat.chatId === editingId ? { ...chat, title: editTitle.trim() } : chat
                              )
                            ));
                          } catch (error) {
                            console.error(error);
                          }
                        }
                        setEditingId(null);
                      }
                      if (e.key === "Escape") {
                        setEditingId(null);
                        setEditTitle(chat.title);
                      }
                    }}
                  />
                ) : (
                  <span className="flex-1 truncate" title={chat.title}>
                    {chat.title}
                  </span>
                )}
                {editingId !== chat.chatId && (
                  <>
                    <button
                      className="ml-2 text-gray-400 hover:text-blue-600 p-1 rounded opacity-0 group-hover:opacity-100"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleRename(chat.chatId, chat.title);
                      }}
                      title="Rename"
                    >
                      <Pencil size={16} />
                    </button>
                    <button
                      className="ml-1 text-gray-400 hover:text-red-600 p-1 rounded opacity-0 group-hover:opacity-100"
                      onClick={(e) => {
                        e.stopPropagation();
                        setPendingDeleteId(chat.chatId);
                      }}
                      title="Delete"
                    >
                      <Trash2 size={16} />
                    </button>
                  </>
                )}
              </div>
            ))
          )}
        </nav>
      </div>

      {/* User info and sign out (mobile only) */}
      {user && (
        <div className="mt-auto border-t border-gray-200 dark:border-gray-700 pt-4 md:hidden">
          <div className="flex items-center px-4 py-2">
            <img
              src={
                user.avatar ||
                `https://ui-avatars.com/api/?name=${user.name}&background=random`
              }
              alt={user.name}
              className="w-10 h-10 rounded-full"
            />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-700 dark:text-gray-300">
                {user.name}
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                {user.email}
              </p>
            </div>
          </div>
          <button
            onClick={logout}
            className="mt-2 w-full flex items-center px-4 py-2 text-base font-medium text-gray-500 hover:text-gray-700 hover:bg-gray-50 dark:text-gray-400 dark:hover:text-white dark:hover:bg-gray-800 rounded-md"
          >
            <User size={20} className="mr-3" />
            Sign Out
          </button>
        </div>
      )}

      {/* Delete confirmation modal */}
      {pendingDeleteId && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40">
          <div className="bg-white dark:bg-gray-900 rounded-lg shadow-lg p-6 w-full max-w-xs">
            <h2 className="text-lg font-semibold mb-2 text-gray-900 dark:text-white">Delete Chat?</h2>
            <p className="mb-4 text-gray-700 dark:text-gray-300">Are you sure you want to delete this chat? This action cannot be undone.</p>
            <div className="flex justify-end space-x-2">
              <button
                className="px-4 py-2 rounded bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 hover:bg-gray-300 dark:hover:bg-gray-600"
                onClick={() => setPendingDeleteId(null)}
              >
                Cancel
              </button>
              <button
                className="px-4 py-2 rounded bg-red-600 text-white hover:bg-red-700 flex items-center justify-center"
                disabled={deleting}
                onClick={async () => {
                  setDeleting(true);
                  await handleDelete(pendingDeleteId);
                  setDeleting(false);
                  setPendingDeleteId(null);
                }}
              >
                {deleting ? (
                  <Loader size={16} className="animate-spin mr-2" />
                ) : null}
                Delete
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

const Sidebar: React.FC<SidebarProps> = ({ isOpen, onClose }) => {
  const { user, logout } = useAuth();
  const location = useLocation();
  
  // Mobile sidebar (overlay)
  if (isOpen) {
    return (
      <div className="fixed inset-0 z-40 md:hidden" onClick={onClose}>
        <div
          className="fixed inset-0 bg-gray-600 bg-opacity-75"
          aria-hidden="true"
        ></div>
        <div
          className="relative flex flex-col w-full max-w-xs bg-white dark:bg-gray-900 h-full"
          onClick={(e) => e.stopPropagation()}
        >
          <SidebarContent
            user={user}
            logout={logout}
            location={location}
            onClose={onClose}
          />
        </div>
      </div>
    );
  }

  // Desktop sidebar (always visible)
  return (
    <aside className="hidden md:flex md:flex-col md:w-64 md:fixed md:inset-y-0 bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-800 z-30">
      <SidebarContent user={user} logout={logout} location={location} />
    </aside>
  );
};

export default Sidebar;
