export interface User {
  id: string;
  name: string;
  username: string;
  email: string;
  avatar?: string;
}

export interface LoginCredentials {
  email_or_username: string;
  password: string;
}

export interface AuthResponse {
  data: {
    user: User;
    access_token: string;
  }
}

export interface ChatApiResponse {
  assistantReply: string;
  searchResults: ProductSuggestion[];
}

export interface ProductSuggestion {
  productID: string;
  url: string;
  score: number;
  spc: string;
  prName: string;
  "category": string;
  "secondaryCategory": string;
  "itemNum": string;
  "skus": [],
  "description": string;
  "colors": string;
  "themes": string;
  "prodTime": string;
  "suppID": string;
  "lineName": string;
  "companyName": string;
  "minPrc": string;
  "maxPrc": string;
  "minNet": string;
  "maxNet": string;
  "thumbPicLink": string;
  "isOptamark": string;
  "categoryGroup": string;
  "productSku": string;
  "minimumQty": string;
  "unitPrice": string;
  "totalPrice": string;
}

export interface ChatApiResponse {
  assistantReply: string;
  searchResults: ProductSuggestion[];
}

export interface ChatMessage {
  id: string;
  type: 'user' | 'bot';
  content: string;
  timestamp: string;
  suggestions?: ProductSuggestion[];
}

export interface ChatSession {
  id: string;
  title: string;
  lastMessage: string;
  timestamp: string;
  messages: ChatMessage[];
}

export interface ChatHistoryResponse {
  sessions: ChatSession[];
  totalPages: number;
  currentPage: number;
}

export interface FilterOptions {
  requiredResults: number;
  productSource: 'all' | 'optamark' | 'non-optamark';
  neighbours: number;
  companies: string[];
}

export interface ChatHistoryItem {
  chatId: string;
  title: string;
  createdAt: number;
  updatedAt: number;
}

export interface CompanyApiResponse {
  name: string;
}