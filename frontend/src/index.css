@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --optamark-blue: 43 154 243;
  --optamark-secondary: 72 167 212;
  --optamark-dark: 24 28 40;
  --optamark-gray: 55 61 65;
}

body {
  font-family: 'Inter', sans-serif;
  @apply bg-gray-50 text-gray-900 dark:bg-[#1E2028] dark:text-gray-100;
}

* {
    scrollbar-width: auto;
    scrollbar-color: #666b76 #0000;
}

.chat-container {
  height: calc(100vh - 64px);
}

.message-appear {
  animation: fadeIn 0.3s ease-out forwards;
}

.slide-in {
  animation: slideIn 0.4s ease-out forwards;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.product-card {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.product-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.page-transition {
  animation: pageTransition 0.3s ease-out forwards;
}

@keyframes pageTransition {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.typing-indicator span {
  animation: blink 1.4s infinite both;
}

.typing-indicator span:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-indicator span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes blink {
  0% {
    opacity: 0.1;
  }
  20% {
    opacity: 1;
  }
  100% {
    opacity: 0.1;
  }
}

