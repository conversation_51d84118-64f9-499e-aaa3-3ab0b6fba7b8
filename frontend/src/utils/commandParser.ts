export interface SearchCommand {
  type: 'spc' | 'item' | 'normal';
  command?: string;
  query: string;
  params?: string[];
}

export const parseSearchCommand = (message: string): SearchCommand => {
  if (!message.startsWith('/')) {
    return { type: 'normal', query: message };
  }

  const parts = message.slice(1).split(' ');
  const command = parts[0].toLowerCase();
  const remainingText = parts.slice(1).join(' ');

  switch (command) {
    case 'spc':
      return {
        type: 'spc',
        command: 'spc',
        query: remainingText,
        params: parts.slice(1)
      };
    case 'item':
      return {
        type: 'item',
        command: 'item',
        query: remainingText,
        params: parts.slice(1)
      };
    default:
      return { type: 'normal', query: message };
  }
};

export const getCommandSuggestions = (input: string): string[] => {
  if (!input.startsWith('/')) return [];
  
  const commands = ['/spc', '/item'];
  const typed = input.toLowerCase();
  
  return commands.filter(cmd => cmd.startsWith(typed));
};