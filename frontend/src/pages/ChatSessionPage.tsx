import React, { useState, useEffect, useRef } from 'react';
import { usePara<PERSON>, Link } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { getChatSession, sendChatMessage, searchProductBySPC, searchProductByItemNum } from '../services/api';
import ChatMessage from '../components/chat/ChatMessage';
import ChatInput from '../components/chat/ChatInput';
import TypingIndicator from '../components/chat/TypingIndicator';
import { ArrowLeft, Loader } from 'lucide-react';
import { FilterOptions } from '../types';
import { SearchCommand } from '../utils/commandParser';
import { RootState } from '../store';

const ChatSessionPage: React.FC = () => {
  const { sessionId } = useParams<{ sessionId: string }>();
  const selectedCompanies = useSelector((state: RootState) => state.filters.companies);
  const [history, setHistory] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<FilterOptions>({
    requiredResults: 5,
    neighbours: 1000,
    productSource: 'all',
    companies: []
  });
  const [showFilters, setShowFilters] = useState(false);
  const [sending, setSending] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    setFilters(prev => ({
      ...prev,
      companies: selectedCompanies
    }));
  }, [selectedCompanies]);

  useEffect(() => {
    const fetchHistory = async () => {
      if (!sessionId) return;
      setIsLoading(true);
      setError(null);
      try {
        const data = await getChatSession(sessionId);
        setHistory(data);
      } catch (e) {
        setError('Failed to load chat history.');
      } finally {
        setIsLoading(false);
      }
    };
    fetchHistory();
  }, [sessionId]);

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [history, sending]);

  const handleSendMessage = async (content: string, command?: SearchCommand) => {
    setSending(true);
    
    try {
      let response;
      let assistantReply: string;
      let searchResults: any[] = [];

      switch (command?.type) {
        case 'spc':
          const spcCode = command.params?.[0] || '';
          
          if (!spcCode) {
            throw new Error('SPC code is required. Usage: /spc [SPC_CODE]');
          }
          
          response = await searchProductBySPC(spcCode, sessionId);
          
          assistantReply = response.data?.assistantReply || response.data?.message || `SPC search results for code: ${spcCode}`;
          searchResults = response.data?.searchResults || response.data?.results || response.data?.suggestions || [];
          break;

        case 'item':
          const itemQuery = command.query || '';
          if (!itemQuery) {
            throw new Error('Search query is required. Usage: /item [search_query]');
          }
          
          response = await searchProductByItemNum(itemQuery, sessionId);
          
          assistantReply = response.data?.assistantReply || response.data?.message || `Item search results for: ${itemQuery}`;
          searchResults = response.data?.searchResults || response.data?.results || response.data?.suggestions || [];
          break;

        default:
          const result = await sendChatMessage(content, { ...filters, chatId: sessionId });
          
          assistantReply = result.data.assistantReply;
          searchResults = result.data.searchResults;
          break;
      }

      setHistory(prev => [
        ...prev,
        {
          userQuery: content,
          assistantResponse: assistantReply,
          searchResults,
          timestamp: Date.now() / 1000,
        }
      ]);

    } catch (error) {      
      setHistory(prev => [
        ...prev,
        {
          userQuery: content,
          assistantResponse: `Error: ${error || 'Sorry, I encountered an error processing your request. Please try again.'}`,
          searchResults: [],
          timestamp: Date.now() / 1000,
        }
      ]);
    } finally {
      setSending(false);
    }
  };

  return (
    <div className="flex flex-col h-full chat-container">
      <div className="flex-grow overflow-y-auto">
        {isLoading ? (
          <div className="flex flex-col items-center justify-center h-full py-12">
            <Loader size={36} className="text-blue-600 animate-spin mb-4" />
            <p className="text-gray-600 dark:text-gray-300">Loading chat session...</p>
          </div>
        ) : error ? (
          <div className="flex flex-col items-center justify-center h-full py-12">
            <div className="text-center">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                {error}
              </h3>
              <p className="text-gray-500 dark:text-gray-400 mb-6">
                Unable to load this chat session.
              </p>
              <Link
                to="/history"
                className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <ArrowLeft size={16} className="mr-2" />
                Back to History
              </Link>
            </div>
          </div>
        ) : (
          <div>
            {history.map((entry, idx) => (
              <React.Fragment key={idx}>
                <ChatMessage
                  message={{
                    id: `user-${idx}`,
                    type: 'user',
                    content: decodeURIComponent(entry.userQuery),
                    timestamp: new Date(entry.timestamp * 1000).toISOString(),
                  }}
                />
                <ChatMessage
                  message={{
                    id: `bot-${idx}`,
                    type: 'bot',
                    content: entry.assistantResponse,
                    timestamp: new Date(entry.timestamp * 1000).toISOString(),
                    suggestions: entry.searchResults,
                  }}
                />
              </React.Fragment>
            ))}
            {sending && <TypingIndicator />}
            <div ref={messagesEndRef} />
          </div>
        )}
      </div>
      
      {/* Updated ChatInput to support command parameter */}
      <ChatInput
        onSendMessage={handleSendMessage}
        isLoading={sending}
        filters={filters}
        onFiltersChange={setFilters}
        showFilters={showFilters}
        setShowFilters={setShowFilters}
      />
    </div>
  );
};

export default ChatSessionPage;