import React, { useState, useRef, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import ChatInput from '../components/chat/ChatInput';
import ChatMessage from '../components/chat/ChatMessage';
import TypingIndicator from '../components/chat/TypingIndicator';
import { ChatMessage as ChatMessageType, FilterOptions } from '../types';
import { sendChatMessage, searchProductBySPC, searchProductByItemNum} from '../services/api';
import { useNavigate } from 'react-router-dom';
import { addChat } from '../store/chatHistorySlice';
import { SearchCommand } from '../utils/commandParser';
import { RootState } from '../store';

const ChatPage: React.FC = () => {
  const selectedCompanies = useSelector((state: RootState) => state.filters.companies);
  const [messages, setMessages] = useState<ChatMessageType[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [chatId, setChatId] = useState<string>('');
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const [filters, setFilters] = useState<FilterOptions>({
    requiredResults: 5,
    neighbours: 1000,
    productSource: 'all',
    companies: []
  });
  const [showFilters, setShowFilters] = useState(false);
  const navigate = useNavigate();
  const dispatch = useDispatch();
  
  useEffect(() => {
    setFilters(prev => ({
      ...prev,
      companies: selectedCompanies
    }));
  }, [selectedCompanies]);
  
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };
  
  useEffect(() => {
    scrollToBottom();
  }, [messages]);
  
  const handleSendMessage = async (content: string, command?: SearchCommand) => {
    const userMessage: ChatMessageType = {
      id: `user-${Date.now()}`,
      type: "user",
      content,
      timestamp: new Date().toISOString(),
    };
    setMessages((prev) => [...prev, userMessage]);
    setIsLoading(true);

    try {
      let response;

      switch (command?.type) {
        case 'spc':
          const spcCode = command.params?.[0] || '';
          
          if (!spcCode) {
            throw new Error('SPC code is required. Usage: /spc [SPC_CODE]');
          }
          
          response = await searchProductBySPC(spcCode, chatId);
          
          if (response.chatId) {
            setChatId(response.chatId);
          }
          
          const spcBotMessage: ChatMessageType = {
            id: `bot-${Date.now()}`,
            type: "bot",
            content: response.data?.assistantReply || response.data?.message || `SPC search results for code: ${spcCode}`,
            timestamp: new Date().toISOString(),
            suggestions: response.data?.searchResults || response.data?.results || response.data?.suggestions || [],
          };
          setMessages((prev) => [...prev, spcBotMessage]);

          if (response.chatId && !chatId) {
            dispatch(addChat({
              chatId: response.chatId,
              title: response.chatTitle || `SPC: ${spcCode}`,
              createdAt: Date.now() / 1000,
              updatedAt: Date.now() / 1000,
            }));

            navigate(`/history/${response.chatId}`, {
              state: {
                firstMessage: {
                  userQuery: content,
                  assistantResponse: response.data?.assistantReply || `SPC search for ${spcCode}`,
                  searchResults: response.data?.searchResults || [],
                  title: response.chatTitle || `SPC: ${spcCode}`,
                }
              }
            });
            return;
          }
          break;

        case 'item':
          const itemQuery = command.query || '';
          if (!itemQuery) {
            throw new Error('Search query is required. Usage: /item [search_query]');
          }
          
          response = await searchProductByItemNum(itemQuery, chatId);
          
          if (response.chatId) {
            setChatId(response.chatId);
          }
          
          const itemBotMessage: ChatMessageType = {
            id: `bot-${Date.now()}`,
            type: "bot",
            content: response.data?.assistantReply || response.data?.message || `Item search results for: ${itemQuery}`,
            timestamp: new Date().toISOString(),
            suggestions: response.data?.searchResults || response.data?.results || response.data?.suggestions || [],
          };
          setMessages((prev) => [...prev, itemBotMessage]);

          if (response.chatId && !chatId) {
            dispatch(addChat({
              chatId: response.chatId,
              title: response.chatTitle || `Item: ${itemQuery}`,
              createdAt: Date.now() / 1000,
              updatedAt: Date.now() / 1000,
            }));

            navigate(`/history/${response.chatId}`, {
              state: {
                firstMessage: {
                  userQuery: content,
                  assistantResponse: response.data?.assistantReply || `Item search for ${itemQuery}`,
                  searchResults: response.data?.searchResults || [],
                  title: response.chatTitle || `Item: ${itemQuery}`,
                }
              }
            });
            return;
          }
          break;

        default:
          console.log('Calling normal chat with:', content, { ...filters, chatId });
          const result = await sendChatMessage(content, { ...filters, chatId });

          if (result.chatId) {
            setChatId(result.chatId);
          }

          if (result.chatId && !chatId) {
            dispatch(addChat({
              chatId: result.chatId,
              title: result.chatTitle || 'New Chat',
              createdAt: Date.now() / 1000,
              updatedAt: Date.now() / 1000,
            }));

            navigate(`/history/${result.chatId}`, {
              state: {
                firstMessage: {
                  userQuery: content,
                  assistantResponse: result.data.assistantReply,
                  searchResults: result.data.searchResults,
                  title: result.chatTitle || 'New Chat',
                }
              }
            });
            return;
          }

          const botMessage: ChatMessageType = {
            id: `bot-${Date.now()}`,
            type: "bot",
            content: result.data.assistantReply,
            timestamp: new Date().toISOString(),
            suggestions: result.data.searchResults,
          };
          setMessages((prev) => [...prev, botMessage]);
          break;
      }

    } catch (error) {
      console.error('Search error:', error);
      const errorMessage: ChatMessageType = {
        id: `error-${Date.now()}`,
        type: 'bot',
        content: `Error: ${error || 'I encountered an error processing your request. Please try again.'}`,
        timestamp: new Date().toISOString()
      };
      setMessages((prev) => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <div className="flex flex-col h-full chat-container">
      <div className="flex-grow overflow-y-auto">
        {messages.length === 0 ? (
          <div className="h-full flex flex-col items-center justify-center p-6 text-center">
            <div className="w-16 h-16 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center mb-4">
               <img
                src="/logo-light.svg"
                alt="Optamark Logo"
                className="block dark:hidden h-8"
              />
              <img
                src="/logo-dark.svg"
                alt="Optamark Logo"
                className="hidden dark:block h-8"
              />
            </div>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
              Welcome to Optamark AI Product Search
            </h2>
            <p className="text-gray-600 dark:text-gray-300 max-w-md">
              Ask me about products, and I'll suggest some great options for you. Try asking for office supplies, electronics, or any other product category.
            </p>
            <div className="mt-6 space-y-3 w-full max-w-md">
              <button
                onClick={() => handleSendMessage("What office chairs do you recommend?")}
                className="w-full p-3 text-left rounded-lg border border-gray-300 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800 text-gray-700 dark:text-gray-200"
              >
                "What office chairs do you recommend?"
              </button>
              <button
                onClick={() => handleSendMessage("I need a laptop for graphic design")}
                className="w-full p-3 text-left rounded-lg border border-gray-300 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800 text-gray-700 dark:text-gray-200"
              >
                "I need a laptop for graphic design"
              </button>
              <button
                onClick={() => handleSendMessage("What printers have the lowest cost per page?")}
                className="w-full p-3 text-left rounded-lg border border-gray-300 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800 text-gray-700 dark:text-gray-200"
              >
                "What printers have the lowest cost per page?"
              </button>
              
              {/* Add some command examples */}
              <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                <p className="text-sm text-gray-500 dark:text-gray-400 mb-2">Try slash commands:</p>
                <button
                  onClick={() => handleSendMessage("/spc ABC123 alternatives")}
                  className="w-full p-2 text-left rounded-lg border border-gray-300 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800 text-gray-700 dark:text-gray-200 text-sm"
                >
                  "/spc ABC123 alternatives"
                </button>
              </div>
            </div>
          </div>
        ) : (
          <div>
            {messages.map((message, index) => (
              <ChatMessage key={message.id || index} message={message} />
            ))}
            {isLoading && <TypingIndicator />}
            <div ref={messagesEndRef} />
          </div>
        )}
      </div>
      
      <ChatInput 
        onSendMessage={handleSendMessage} 
        isLoading={isLoading} 
        filters={filters}
        onFiltersChange={setFilters}
        showFilters={showFilters}
        setShowFilters={setShowFilters}
      />
    </div>
  );
};

export default ChatPage;