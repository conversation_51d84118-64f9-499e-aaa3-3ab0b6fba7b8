elastic.namespace = uat
#elastic.host = *************
#elastic.host = **************
elastic.host = 127.0.0.1
sage.auth.id = 231324
sage.login.id = tarang
sage.auth.key = 7f6822ea32321592d88fb6fca8fb790e
sage.connect.url = https://www.promoplace.com/ws/ws.dll/ConnectAPI
cors.origin.url = https://awi-beta.anythingwithink.com,https://uat-aichat.optamarkgraphics.com,http://localhost:5173

# Auth login
auth.login.url = https://awi-beta-api.anythingwithink.com/login

# sheet folder and name
excel.sheet.folderPath = apparel-printing-price/
screen.printing = screen-printing-price.xlsx
dtf.printing = dtf-printing-price.xlsx
embriodery.printing = embriodery-printing-price.xlsx

# S3 bucket
#aws.accessKeyId = ********************
#aws.secretKey = mGVabo4CX6qag2mli7O4QiojK9V1h4QZ2JacA+v/
#aws.region = us-east-2
#aws.s3.bucket = awi-esb

# S3 bucket
aws.accessKeyId=********************
aws.secretKey=gHX5Vuk5keociG4gc2PeXXvYiF8XKDtHxYfOQ3C5
aws.region = us-east-2
aws.s3.bucket = awi-esb
aws.s3.history.bucket = awi-chat-history

# ODP franchise sage supplier filter and add markup 43%
franchise.id=6
markup.percentage=0.43

#Max sage record
sage.max.record = 50000
sage.max.request.attempt = 3

logging.level.root = info
logging.level.org.elasticsearch.script = trace


# optamark product excel api
optamark.product.excel.api = https://www.optamark.com/services/api.php?method=getProductSheet&token=cqlk6y7F9FUW4VXp6uVL

# Upload file limit
spring.servlet.multipart.max-file-size=20MB
spring.servlet.multipart.max-request-size=20MB

# Swagger configuration
springdoc.api-docs.enabled=true
springdoc.swagger-ui.enabled=true


# Open AI configuration
spring.ai.openai.api-key=********************************************************************************************************************************************************************
spring.ai.openai.chat.options.model=gpt-4o
spring.ai.openai.embedding.options.model=text-embedding-ada-002

knn.min-score=0.35

# Swagger configuration
springdoc.api-docs.path=/v3/api-docs
springdoc.swagger-ui.path=/swagger-ui.html
