# application.properties
spring.profiles.active=dev
logging.level.root=INFO
#spring-boot.run.jvmArguments=--add-opens java.base/java.lang=ALL-UNNAMED --add-opens java.base/sun.misc=ALL-UNNAMED

prompt.title=Generate a concise title (maximum 4 words) for a product search conversation.\nONLY output the title with no explanation or additional text.\n\nExamples:\nQuery: \"I need comfortable running shoes for a marathon\"\nTitle: Marathon Running Shoes\n\nQuery: \"Do you have office chairs with lumbar support?\"\nTitle: Lumbar Support Chairs\n\nQuery: \"%s\"\nTitle:
#prompt.query-refiner=Refine the user query for product search. Maximum 15 words.\nONLY output the refined query, nothing else.\n\nConversion Rules:\n- Keep product type and key features (color, size, material)\n- Remove filler phrases like \"can you show me\" or \"I want\"\n- Focus on searchable attributes\n\nExamples:\nContext: \"User was looking at leather bags.\"\nInput: \"Can you also show me some in black color?\"\nOutput: black leather bags\n\nContext: \"%s\"\nInput: \"%s\"\nOutput:
prompt.context-summary=You are a chat history summarizer for an intelligent e-commerce product search assistant.\n\nYour Task:\n- Maintain a numbered, chronological summary of the conversation, user intent and search outcomes.\n- ONLY add ONE new line for the most recent interaction.\n- DO NOT repeat or paraphrase earlier points.\n- Infer the correct number (e.g., #1, #2) based on the existing summary.\n- Format: \"#N. [Concise summary]\"\n- If no previous summary exists, start with \"#1\" ONLY.\n- If the previous summary exists, continue with the next number.\n\nWhat to include in the new line:\n- User?s intent (based on original query).\n- Refined query used for the search.\n- Products returned from knn search.\n- Mention products with: name, ID, color, a short description, etc That align with user query.\n- Keep the tone formal and factual. Avoid opinions.\n- Max 50 words.\n\nExample line:\n#1. User searched for 2 black office bags (refined query: \"black office bags\"). Returned N products: ?Samsonite Mobile Office? (ID: P123, Black, padded laptop sleeve), ?Urban Tote? (ID: P456, Grey, waterproof), ?Exec Pro Bag? (ID: P789, Blue, leather finish), etc.\n\nExisting summary:\n%s\n\nOriginal user query:\n\"%s\"\n\nRefined query:\n\"%s\"\n\nAssistant's reply:\n\"%s\"\n\nReturned products:\n%s\n\nNow write ONLY the next summary bullet.
prompt.rerank-and-generate-json=You are an intelligent product ranking and response generation engine for an e-commerce semantic search assistant.\n\nGiven:\n- A user query (which may be vague, specific, or comparative).\n- A list of N product candidates (1 to N). Each product includes: ID, Name, Score, Description, Colors, minimum price, etc.\n\nYour task:\n1. Analyze the user query to understand their **intent**, such as desired product type, use-case, features, colors, price sensitivity, or design.\n2. **RANK** the product candidates in order of relevance to the query & user need.\n3. **FILTER OUT** products that are clearly irrelevant, misleading, unrelated in category, or poor matches.\n4. **WRITE** a natural and helpful assistant-style message summarizing the final products.\n\nRules:\n- Only use the provided fields: ID, Name, Score, Colors, Description. **No assumptions or external knowledge**.\n- Your ranking must reflect the query intent (e.g., color preferences, type, use-case, features).\n- The response must include only products from the final ranked list, in the same order.\n- You may include all, some, or none of the candidates depending on their match quality.\n\nOutput Format:\noutput MUST be valid JSON in this exact format:\n{\n  \"ranked_ids\": [\"P123\", \"P456\", \"P789\"],\n  \"assistant_reply\": \"Here's a curated selection based on your request: ...\"\n}\n\nConstraints:\n- The \"ranked_ids\" array must list product IDs in final ranked order.\n- The \"assistant_reply\" should be written in a friendly, concise, informative style.\n- DO NOT include any explanation, reasoning, or content outside the JSON.\n- DO NOT output additional fields or text.\n- ENSURE THAT THE SORTING MUST ALIGN WITH assistant_reply .\n\nUser Query:\n\"%s\"\n\nProducts:\n%s\n\nNow respond with the final JSON string (WITHOUT ```json):

prompt.system-prompt-introduction=You are an product search assistant AI bot by Optamark AI. Use the following product results and context history to answer the user's query. Only provide the answer based on what the system messages supply.


prompt.query-refiner=You are a product query refiner for an e-commerce assistant.\n\\n\Goal:\n\Refine the user?s raw query into a concise, searchable phrase (?15 words).\n\\n\Rules:\n\- Preserve product type and explicit filters (e.g., color, size, material, features).\n\- Remove filler or polite phrases (e.g., ?can you show me?, ?I want?).\n\- Use context only to resolve ambiguity; if context is empty or unhelpful, ignore it.\n\- If the input is already concise and self-contained, return it verbatim.\n\- Do NOT invent or add any products, attributes, or features not present in context or input.\n\- Output must be a single phrase, not a full sentence; lowercase unless it?s a proper brand name.\n\\n\Examples:\n\Context: \"User browsing black leather backpacks.\"\n\Input: \"Do you also have navy ones?\"\n\Output: \"navy leather backpacks\"\n\\n\Context: \"Looking at eco-friendly glass bottles.\"\n\Input: \"Show more.\"\n\Output: \"eco-friendly glass bottles\"\n\\n\Context: \"\"\n\Input: \"Soft cotton T-shirts\"  \n\Output: \"soft cotton t-shirts\"\n\\n\Context: \"%s\"\n\Input: \"%s\"\n\Output:
