elastic.namespace = prod
#elastic.host = *************
#elastic.host = **************
elastic.host = *************
sage.auth.id = 231324
sage.login.id = tarang
sage.auth.key = 7f6822ea32321592d88fb6fca8fb790e
sage.connect.url = https://www.promoplace.com/ws/ws.dll/ConnectAPI
cros.origin.url = https://app.optamarkgraphics.com

# Auth login
auth.login.url = https://app-api.optamarkgraphics.com/login

# sheet folder and name
excel.sheet.folderPath = apparel-printing-price/
screen.printing = screen-printing-price.xlsx
dtf.printing = dtf-printing-price.xlsx
embriodery.printing = embriodery-printing-price.xlsx

# S3 bucket
aws.accessKeyId = ********************
aws.secretKey = mGVabo4CX6qag2mli7O4QiojK9V1h4QZ2JacA+v/
aws.region = us-east-2
aws.s3.bucket = awi-esb

# ODP franchise sage supplier filter and add markup 43%
franchise.id=6
markup.percentage=0.43

#Max sage record
sage.max.record = 50000
sage.max.request.attempt = 3

logging.level.root = error

# optamark product excel api
optamark.product.excel.api = https://www.optamark.com/services/api.php?method=getProductSheet&token=cqlk6y7F9FUW4VXp6uVL

# Upload file limit
spring.servlet.multipart.max-file-size=20MB
spring.servlet.multipart.max-request-size=20MB

# Swagger configuration
springdoc.api-docs.enabled=false
springdoc.swagger-ui.enabled=false
