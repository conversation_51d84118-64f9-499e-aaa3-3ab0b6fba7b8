<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:tns="http://www.promostandards.org/WSDL/Inventory/2.0.0/" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:ns="http://www.promostandards.org/WSDL/Inventory/2.0.0/SharedObjects/" xmlns:ns1="http://www.codesynthesis.com/xmlns/xsstl" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" name="Inventory_v2_0_0" targetNamespace="http://www.promostandards.org/WSDL/Inventory/2.0.0/">
	<wsdl:types>
		<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema">
			<xsd:import namespace="http://www.promostandards.org/WSDL/Inventory/2.0.0/"  schemaLocation="GetFilterValuesRequest.xsd"/>
		</xsd:schema>
		<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema">
			<xsd:import namespace="http://www.promostandards.org/WSDL/Inventory/2.0.0/" schemaLocation="GetFilterValuesResponse.xsd"/>
		</xsd:schema>
		<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema">
			<xsd:import namespace="http://www.promostandards.org/WSDL/Inventory/2.0.0/" schemaLocation="GetInventoryLevelsRequest.xsd"/>
		</xsd:schema>
		<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema">
			<xsd:import namespace="http://www.promostandards.org/WSDL/Inventory/2.0.0/" schemaLocation="GetInventoryLevelsResponse.xsd"/>
		</xsd:schema>
	</wsdl:types>
	<wsdl:message name="GetFilterValuesRequestMessage">
		<wsdl:part name="GetFilterValuesRequest" element="tns:GetFilterValuesRequest"/>
	</wsdl:message>
	<wsdl:message name="GetFilterValuesResponseMessage">
		<wsdl:part name="GetFilterValuesResponse" element="tns:GetFilterValuesResponse"/>
	</wsdl:message>
	<wsdl:message name="getInventoryLevelsRequest">
		<wsdl:part name="GetInventoryLevelsRequest" element="tns:GetInventoryLevelsRequest"/>
	</wsdl:message>
	<wsdl:message name="getInventoryLevelsResponse">
		<wsdl:part name="GetInventoryLevelsResponse" element="tns:GetInventoryLevelsResponse"/>
	</wsdl:message>
	<wsdl:portType name="InventoryService">
		<wsdl:operation name="getFilterValues">
			<wsdl:input message="tns:GetFilterValuesRequestMessage"/>
			<wsdl:output message="tns:GetFilterValuesResponseMessage"/>
		</wsdl:operation>
		<wsdl:operation name="getInventoryLevels">
			<wsdl:input message="tns:getInventoryLevelsRequest"/>
			<wsdl:output message="tns:getInventoryLevelsResponse"/>
		</wsdl:operation>
	</wsdl:portType>
	<wsdl:binding name="InventoryServiceBindingV2" type="tns:InventoryService">
		<soap:binding transport="http://schemas.xmlsoap.org/soap/http"/>
		<wsdl:operation name="getFilterValues">
			<soap:operation soapAction="getFilterValues" style="document"/>
			<wsdl:input>
				<soap:body use="literal"/>
			</wsdl:input>
			<wsdl:output>
				<soap:body use="literal"/>
			</wsdl:output>
		</wsdl:operation>
		<wsdl:operation name="getInventoryLevels">
			<soap:operation soapAction="getInventoryLevels" style="document"/>
			<wsdl:input>
				<soap:body use="literal"/>
			</wsdl:input>
			<wsdl:output>
				<soap:body use="literal"/>
			</wsdl:output>
		</wsdl:operation>
	</wsdl:binding>
	<wsdl:service name="InventoryServiceV2">
		<wsdl:port name="InventoryServiceBinding" binding="tns:InventoryServiceBindingV2">
			<soap:address location="[Endpoint URL]"/>
		</wsdl:port>
	</wsdl:service>
</wsdl:definitions>
