<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ns1="http://www.promostandards.org/WSDL/ProductDataService/1.0.0/" xmlns:ns2="http://www.promostandards.org/WSDL/ProductDataService/1.0.0/" xmlns:ns3="http://www.promostandards.org/WSDL/ProductDataService/1.0.0/SharedObjects/" targetNamespace="http://www.promostandards.org/WSDL/ProductDataService/1.0.0/" elementFormDefault="qualified">
	<xsd:import namespace="http://www.promostandards.org/WSDL/ProductDataService/1.0.0/SharedObjects/" schemaLocation="SharedProductObjects.xsd"/>
	<xsd:element name="GetProductRequest">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element ref="ns3:wsVersion"/>
				<xsd:element ref="ns3:id"/>
				<xsd:element ref="ns3:password" minOccurs="0"/>
				<xsd:element ref="ns3:localizationCountry"/>
				<xsd:element ref="ns3:localizationLanguage"/>
				<xsd:element ref="ns3:productId"/>
				<xsd:element ref="ns3:partId" minOccurs="0"/>
				<xsd:element name="colorName" minOccurs="0">
					<xsd:annotation>
						<xsd:documentation>A specific part color for this product</xsd:documentation>
					</xsd:annotation>
					<xsd:simpleType>
						<xsd:restriction base="xsd:token">
							<xsd:minLength value="1"/>
							<xsd:maxLength value="64"/>
						</xsd:restriction>
					</xsd:simpleType>
				</xsd:element>
				<xsd:element name="ApparelSizeArray" minOccurs="0">
					<xsd:annotation>
						<xsd:documentation>Used for returning specific apparel sizes.  Array of ApparelSize objects.</xsd:documentation>
					</xsd:annotation>
					<xsd:complexType>
						<xsd:sequence>
							<xsd:element ref="ns3:ApparelSize" maxOccurs="unbounded"/>
						</xsd:sequence>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>
</xsd:schema>
