<?xml version="1.0" encoding="UTF-8" ?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema"            
            targetNamespace="http://www.promostandards.org/WSDL/InventoryService/1.0.0/"
            elementFormDefault="qualified">
  <xsd:element name="GetFilterValuesRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="wsVersion">
          <xsd:annotation>
            <xsd:documentation>
              The Standard Version of the Web Service being referenced
            </xsd:documentation>
          </xsd:annotation>
          <xsd:simpleType>
            <xsd:restriction base="xsd:token">
              <xsd:minLength value="1"/>
              <xsd:maxLength value="64"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="id">
          <xsd:annotation>
            <xsd:documentation>
              The customerID or any other agreed upon ID
            </xsd:documentation>
          </xsd:annotation>
          <xsd:simpleType>
            <xsd:restriction base="xsd:token">
              <xsd:minLength value="1"/>
              <xsd:maxLength value="64"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="password" minOccurs="0">
          <xsd:annotation>
            <xsd:documentation>
              The password associated with the customerID
            </xsd:documentation>
          </xsd:annotation>
          <xsd:simpleType>
            <xsd:restriction base="xsd:token">
              <xsd:minLength value="1"/>
              <xsd:maxLength value="64"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="productID">
          <xsd:annotation>
            <xsd:documentation>
              Item’s ID
            </xsd:documentation>
          </xsd:annotation>
          <xsd:simpleType>
            <xsd:restriction base="xsd:token">
              <xsd:minLength value="1"/>
              <xsd:maxLength value="64"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
        <xsd:element name="productIDtype">
          <xsd:annotation>
            <xsd:documentation>
              productID type (Distributor, Supplier)
            </xsd:documentation>
          </xsd:annotation>
          <xsd:simpleType>
            <xsd:restriction base="xsd:token">
              <xsd:minLength value="1"/>
              <xsd:maxLength value="64"/>
            </xsd:restriction>
          </xsd:simpleType>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
</xsd:schema>
