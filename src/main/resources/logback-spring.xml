<configuration>
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- File Appender -->
    <appender name="FILE" class="ch.qos.logback.core.FileAppender">
        <file>logs/app.log</file> <!-- Log file location -->
        <append>false</append> <!-- Cleanup on restart -->
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <logger name="com.optahub.awi" level="INFO"/>
    <!-- Reduce Log Levels -->
    <logger name="org.hibernate.validator" level="ERROR"/>
    <logger name="org.hibernate" level="ERROR"/>
    <logger name="org.springframework.boot" level="ERROR"/>
    <logger name="org.springframework.data" level="ERROR"/>
    <logger name="org.springframework.web" level="ERROR"/>
    <logger name="org.springframework.security" level="ERROR"/>
    <logger name="org.apache.coyote.http11" level="ERROR"/>
    <logger name="org.apache.catalina.core" level="ERROR"/>
    <logger name="org.springframework.context.support.PostProcessorRegistrationDelegate" level="ERROR"/>
    <logger name="org.springframework.ws" level="ERROR"/>
    <logger name="org.springframework.ws.config.annotation" level="ERROR"/>
    <logger name="org.springframework.ws.soap.addressing" level="ERROR"/>

    <root level="INFO">
        <appender-ref ref="STDOUT"/>
        <appender-ref ref="FILE"/>
    </root>
</configuration>