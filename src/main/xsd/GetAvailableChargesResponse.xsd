<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ns1="http://www.promostandards.org/WSDL/PricingAndConfiguration/1.0.0/" xmlns:ns2="http://www.promostandards.org/WSDL/PricingAndConfiguration/1.0.0/" xmlns:ns3="http://www.promostandards.org/WSDL/PricingAndConfiguration/1.0.0/SharedObjects/" targetNamespace="http://www.promostandards.org/WSDL/PricingAndConfiguration/1.0.0/" elementFormDefault="qualified">
	<xsd:import namespace="http://www.promostandards.org/WSDL/PricingAndConfiguration/1.0.0/SharedObjects/" schemaLocation="SharedObjectsPricingAndConfiguration.xsd"/>
	<xsd:element name="AvailableCharge">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element ref="ns3:chargeId"/>
				<xsd:element ref="ns3:chargeName"/>
				<xsd:element ref="ns3:chargeDescription"/>
				<xsd:element ref="ns3:chargeType"/>
			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="GetAvailableChargesResponse">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="AvailableChargeArray" minOccurs="0">
					<xsd:annotation>
						<xsd:documentation>
                            An Array of charges
                        </xsd:documentation>
					</xsd:annotation>
					<xsd:complexType>
						<xsd:sequence>
							<xsd:element ref="ns1:AvailableCharge" maxOccurs="unbounded"/>
						</xsd:sequence>
					</xsd:complexType>
				</xsd:element>
				<xsd:element ref="ns3:ErrorMessage" minOccurs="0"/>
			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>
</xsd:schema>
