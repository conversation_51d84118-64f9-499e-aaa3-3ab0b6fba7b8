<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ns1="http://www.promostandards.org/WSDL/PricingAndConfiguration/1.0.0/" xmlns:ns2="http://www.promostandards.org/WSDL/PricingAndConfiguration/1.0.0/" xmlns:ns3="http://www.promostandards.org/WSDL/PricingAndConfiguration/1.0.0/SharedObjects/" targetNamespace="http://www.promostandards.org/WSDL/PricingAndConfiguration/1.0.0/" elementFormDefault="qualified">
	<xsd:import namespace="http://www.promostandards.org/WSDL/PricingAndConfiguration/1.0.0/SharedObjects/" schemaLocation="SharedObjectsPricingAndConfiguration.xsd"/>
	<xsd:element name="Color">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element ref="ns3:colorId"/>
				<xsd:element ref="ns3:colorName"/>
			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="DecorationMethod">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element ref="ns3:decorationId"/>
				<xsd:element ref="ns3:decorationName"/>
			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="GetDecorationColorsResponse">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="DecorationColors" minOccurs="0">
					<xsd:complexType>
						<xsd:sequence>
							<xsd:element name="ColorArray" minOccurs="0">
								<xsd:annotation>
									<xsd:documentation>
                                        An Array of colors
                                    </xsd:documentation>
								</xsd:annotation>
								<xsd:complexType>
									<xsd:sequence>
										<xsd:element ref="ns1:Color" maxOccurs="unbounded"/>
									</xsd:sequence>
								</xsd:complexType>
							</xsd:element>
							<xsd:element ref="ns3:productId"/>
							<xsd:element ref="ns3:locationId"/>
							<xsd:element name="DecorationMethodArray" minOccurs="0">
								<xsd:annotation>
									<xsd:documentation>
                                        An Array of decoration method
                                    </xsd:documentation>
								</xsd:annotation>
								<xsd:complexType>
									<xsd:sequence>
										<xsd:element ref="ns1:DecorationMethod" maxOccurs="unbounded"/>
									</xsd:sequence>
								</xsd:complexType>
							</xsd:element>
							<xsd:element name="pmsMatch" type="xsd:boolean" nillable="true" minOccurs="1">
								<xsd:annotation>
									<xsd:documentation>
                                        PMS match or nto
                                    </xsd:documentation>
								</xsd:annotation>
							</xsd:element>
							<xsd:element name="fullColor" type="xsd:boolean" nillable="true" minOccurs="1">
								<xsd:annotation>
									<xsd:documentation>
                                        Full Color or not
                                    </xsd:documentation>
								</xsd:annotation>
							</xsd:element>
						</xsd:sequence>
					</xsd:complexType>
				</xsd:element>
				<xsd:element ref="ns3:ErrorMessage" minOccurs="0"/>
			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>
</xsd:schema>
