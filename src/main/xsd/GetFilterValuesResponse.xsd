<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ns1="http://www.promostandards.org/WSDL/Inventory/2.0.0/"
            xmlns:ns2="http://www.promostandards.org/WSDL/Inventory/2.0.0/SharedObjects/"
            targetNamespace="http://www.promostandards.org/WSDL/Inventory/2.0.0/" elementFormDefault="qualified">
    <xsd:import namespace="http://www.promostandards.org/WSDL/Inventory/2.0.0/SharedObjects/"
                schemaLocation="SharedObjectsInventory.xsd"/>
    <xsd:element name="GetFilterValuesResponse">
        <xsd:complexType>
            <xsd:sequence>
                <xsd:element name="FilterValues" minOccurs="0">
                    <xsd:complexType>
                        <xsd:sequence>
                            <xsd:element ref="ns2:productId"/>
                            <xsd:element ref="ns2:Filter"/>
                        </xsd:sequence>
                    </xsd:complexType>
                </xsd:element>
                <xsd:element ref="ns2:ServiceMessageArray" minOccurs="0"/>
            </xsd:sequence>
        </xsd:complexType>
    </xsd:element>
</xsd:schema>