package com.optahub.awi.service.elastic.data;

import java.util.List;

public class ProductPart {
    private String partId;
    private List<String> colorList;
    private String countryOfOrigin;
    private String primaryMaterial;
    private String shape;
    private ProductPartDimension dimension;
    private List<ProductPartShipping> shippingList;

    private List<ProductPartColor> partColors;

    private ProductPartApparelSize apparelSize;

    public String getPartId() {
        return partId;
    }

    public void setPartId(String partId) {
        this.partId = partId;
    }

    public List<String> getColorList() {
        return colorList;
    }

    public void setColorList(List<String> colorList) {
        this.colorList = colorList;
    }

    public String getCountryOfOrigin() {
        return countryOfOrigin;
    }

    public void setCountryOfOrigin(String countryOfOrigin) {
        this.countryOfOrigin = countryOfOrigin;
    }

    public String getPrimaryMaterial() {
        return primaryMaterial;
    }

    public void setPrimaryMaterial(String primaryMaterial) {
        this.primaryMaterial = primaryMaterial;
    }

    public String getShape() {
        return shape;
    }

    public void setShape(String shape) {
        this.shape = shape;
    }

    public ProductPartDimension getDimension() {
        return dimension;
    }

    public void setDimension(ProductPartDimension dimension) {
        this.dimension = dimension;
    }

    public List<ProductPartShipping> getShippingList() {
        return shippingList;
    }

    public void setShippingList(List<ProductPartShipping> shippingList) {
        this.shippingList = shippingList;
    }

    public List<ProductPartColor> getPartColors() {
        return partColors;
    }

    public void setPartColors(List<ProductPartColor> partColors) {
        this.partColors = partColors;
    }

    public ProductPartApparelSize getApparelSize() {
        return apparelSize;
    }

    public void setApparelSize(ProductPartApparelSize apparelSize) {
        this.apparelSize = apparelSize;
    }
}
