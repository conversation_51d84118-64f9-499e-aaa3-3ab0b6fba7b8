package com.optahub.awi.service.elastic.service;

import static com.optahub.awi.service.elastic.service.ElasticSearchService.*;

import co.elastic.clients.elasticsearch._types.FieldValue;
import co.elastic.clients.elasticsearch._types.aggregations.Aggregation;
import co.elastic.clients.elasticsearch._types.aggregations.TermsAggregation;
import co.elastic.clients.elasticsearch._types.query_dsl.*;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import co.elastic.clients.elasticsearch.core.DeleteByQueryRequest;
import co.elastic.clients.elasticsearch.core.SearchRequest;
import co.elastic.clients.json.JsonData;

@Service
public class ElasticQueryBuilderService {

    @Value("${elastic.namespace:uat}")
    String namespace;

    public DeleteByQueryRequest buildSupplierCodeQuery(final String supplierCode) {
        Query query = MatchQuery.of(m -> m
            .field("supplierCode")
            .query(supplierCode)
        )._toQuery();

        return DeleteByQueryRequest.of(s -> s
            .index(namespace + "-" + SUPPLIER_INDEX)
            .query(q -> q
                .term(t -> t
                    .field("supplierCode")
                    .value(supplierCode)
                )
            )
        );
    }

    public DeleteByQueryRequest buildSupplierDataCodeQuery(final String supplierCode) {
        /*return MatchQuery.of(m -> m
            .field("code")
            .query(supplierCode)
        )._toQuery();*/
        /*return new NativeSearchQueryBuilder()
                .withQuery(matchQuery("code", supplierCode))
                .build();*/
        return DeleteByQueryRequest.of(s -> s
            .index(namespace + "-" + SUPPLIER_INDEX)
            .query(q -> q
                .term(t -> t
                    .field("code") // or "supplierCode" depending on your mapping
                    .value(supplierCode)
                )
            )
        );
    }

    public SearchRequest buildSupplierQuery(final String supplierCode) {
        return SearchRequest.of(s -> s
            .index(namespace + "-" + SUPPLIER_INDEX)
            .query(Query.of(q -> q
                .match(m -> m
                    .field("code")
                    .query(supplierCode)
                )
            ))
        );
       /* return new NativeSearchQueryBuilder()
                .withQuery(matchQuery("code", supplierCode))
                .build();*/
    }

    public Query buildKeywordQuery(final String keyword) {
        return MatchQuery.of(m -> m
            .query(keyword)
        )._toQuery();
        /*return new NativeSearchQueryBuilder()
                .withQuery(QueryBuilders.queryStringQuery(keyword))
                .build();*/
    }

    public SearchRequest buildCategoryAggregationQuery(final List<String> supplierList,
                                               final List<String> brandList,
                                               final Boolean apparel) {
        /*BoolQueryBuilder conditions = boolQuery();
        if (supplierList.size() > 0) {
            conditions.filter(termsQuery("supplierParent.keyword", supplierList));
        }
        if (brandrList.size() > 0) {
            conditions.filter(termsQuery("supplierName.keyword", brandrList));
        }
        if (apparel) {
            conditions.must(termsQuery("apparel", true));
        } else {
            conditions.mustNot(termsQuery("apparel", true));
        }
        *//*if(apparel != null && apparel) {
            conditions.filter(termsQuery("supplierName.keyword", brandrList));
        }*//*
        return new NativeSearchQueryBuilder()
                .addAggregation(terms("Category").field("categoryList.category.keyword").size(Integer.MAX_VALUE))
                .withQuery(conditions)
                .build();*/
        // --- Build Bool Query ---
        BoolQuery.Builder boolQuery = new BoolQuery.Builder();

        if (supplierList != null && !supplierList.isEmpty()) {
            boolQuery.filter(q -> q.terms(t -> t.field("supplierParent.keyword")
                    .terms(terms -> terms.value(
                            supplierList.stream().map(FieldValue::of).toList()
            ))));
        }

        if (brandList != null && !brandList.isEmpty()) {
            boolQuery.filter(q -> q.terms(t -> t.field("supplierName.keyword")
                    .terms(terms -> terms.value(
                            brandList.stream().map(FieldValue::of).toList()
            ))));
        }

        if (Boolean.TRUE.equals(apparel)) {
            boolQuery.must(q -> q.term(t -> t.field("apparel").value(true)));
        } else {
            boolQuery.mustNot(q -> q.term(t -> t.field("apparel").value(true)));
        }

        // Build aggregation
        Aggregation categoryAggregation = Aggregation.of(a -> a
                .terms(TermsAggregation.of(t -> t
                        .field("categoryList.category.keyword")
                        .size(10_000) // Integer.MAX_VALUE is too large for Elasticsearch, use a practical high limit
                ))
        );

        return SearchRequest.of(s -> s
                .index(namespace + "-" + PRODUCT_INDEX)
                .query(q -> q.bool(boolQuery.build()))
                .aggregations("Category", categoryAggregation)
                .size(0)
        );
    }

    public SearchRequest buildCategoryAggregationQuery() {
        /*return new NativeSearchQueryBuilder()// we build a Elasticsearch native query
                .addAggregation(terms("Category").field("categoryList.category.keyword"))
                .withQuery(boolQuery()
                        .filter(termsQuery("supplierParent.keyword", supplierList))
                        .filter(termsQuery("supplierName.keyword", brandrList)))
                .build();*/

       return SearchRequest.of(s -> s
            .index(namespace + "-" + PRODUCT_INDEX)
            .size(0)
            .aggregations("Category", a -> a
                .terms(t -> t
                    .field("categoryList.category.keyword")
                    .size(10_000)
                )
            )
        );
    }

    public SearchRequest buildSubCategoryAggregationQuery(final List<String> supplierList,
                                                  final List<String> brandList,
                                                  final List<String> categoryList,
                                                  final Boolean apparel) {
        /*BoolQueryBuilder conditions = boolQuery();
        if(supplierList.size() > 0){
            conditions.filter(termsQuery("supplierParent.keyword", supplierList));
        }
        if(brandList.size() > 0){
            conditions.filter(termsQuery("supplierName.keyword", brandList));
        }
        if(categoryList.size() > 0){
            conditions.filter(termsQuery("categoryList.category.keyword", categoryList));
        }

        if (apparel) {
            conditions.must(termsQuery("apparel", true));
        } else {
            conditions.mustNot(termsQuery("apparel", true));
        }

        return new NativeSearchQueryBuilder()// we build a Elasticsearch native query
                .addAggregation(terms("SubCategory").field("categoryList.subCategory.keyword").size(Integer.MAX_VALUE))
                .withQuery(conditions)
                .build();*/


        BoolQuery.Builder boolQueryBuilder = new BoolQuery.Builder();

        // supplierParent filter
        if (supplierList != null && !supplierList.isEmpty()) {
            boolQueryBuilder.filter(f -> f.terms(t -> t
                    .field("supplierParent.keyword")
                    .terms(tv -> tv.value(supplierList.stream().map(FieldValue::of).toList()))
            ));
        }

        // supplierName filter
        if (brandList != null && !brandList.isEmpty()) {
            boolQueryBuilder.filter(f -> f.terms(t -> t
                    .field("supplierName.keyword")
                    .terms(tv -> tv.value(brandList.stream().map(FieldValue::of).toList()))
            ));
        }

        // categoryList.category filter
        if (categoryList != null && !categoryList.isEmpty()) {
            boolQueryBuilder.filter(f -> f.terms(t -> t
                    .field("categoryList.category.keyword")
                    .terms(tv -> tv.value(categoryList.stream().map(FieldValue::of).toList()))
            ));
        }

        if (Boolean.TRUE.equals(apparel)) {
            boolQueryBuilder.must(m -> m.terms(t -> t
                    .field("apparel")
                    .terms(ts -> ts
                            .value(List.of(FieldValue.of(true))))
            ));
        } else {
            boolQueryBuilder.mustNot(m -> m.terms(t -> t
                    .field("apparel").terms(ts -> ts
                            .value(List.of( FieldValue.of(true))))
            ));
        }

        // Build the search request
        return new SearchRequest.Builder()
                .index(namespace + "-" + PRODUCT_INDEX)
                .size(0)
                .query(q -> q.bool(boolQueryBuilder.build()))
                .aggregations("SubCategory", a -> a
                        .terms(t -> t
                                .field("categoryList.subCategory.keyword")
                                .size(10000)
                        )
                )
                .build();

    }

    public SearchRequest buildSubCategoryAggregationQuery(final String category) {
        /*return new NativeSearchQueryBuilder()// we build a Elasticsearch native query
                .addAggregation(terms("SubCategory").field("categoryList.subCategory.keyword"))
                .withQuery(boolQuery()
                        *//*.filter(termsQuery("supplierParent.keyword", supplierList))
                        .filter(termsQuery("supplierName.keyword", brandList))*//*
                        .filter(termsQuery("categoryList.category.keyword", category)))
                .build();*/

        TermsQuery categoryTermsQuery = TermsQuery.of(t -> t
                .field("categoryList.category.keyword")
                .terms(tf -> tf.value(List.of(FieldValue.of(category))))
        );

        Query boolQuery = Query.of(q -> q.bool(b -> b
                .filter(Query.of(q1 -> q1.terms(categoryTermsQuery)))
        ));

        Aggregation subCategoryAggregation = Aggregation.of(a -> a
                .terms(TermsAggregation.of(t -> t
                        .field("categoryList.subCategory.keyword")
                        .size(10_000)
                ))
        );

        return SearchRequest.of(s -> s
                .index(namespace + "-" + PRODUCT_INDEX)
                .query(boolQuery)
                .aggregations("SubCategory", subCategoryAggregation)
                .size(0)
        );
    }

    public SearchRequest buildSupplierAggregation(final Boolean apparel) {

        /*BoolQueryBuilder conditions = boolQuery();

        if (apparel) {
            conditions.must(termsQuery("apparel", true));
        } else {
            conditions.mustNot(termsQuery("apparel", true));
        }

        return new NativeSearchQueryBuilder()// we build a Elasticsearch native query
                .addAggregation(terms("Supplier").field("supplierParent.keyword").size(Integer.MAX_VALUE))
                .withQuery(conditions)
                .build();*/

        // Create apparel query condition
        BoolQuery.Builder boolQueryBuilder = new BoolQuery.Builder();

        if (Boolean.TRUE.equals(apparel)) {
            boolQueryBuilder.must(m -> m.terms(t -> t
                    .field("apparel")
                    .terms(ts -> ts.value(List.of(FieldValue.of(true))))
            ));
        } else {
            boolQueryBuilder.mustNot(m -> m.terms(t -> t
                    .field("apparel")
                    .terms(ts -> ts.value(List.of(FieldValue.of(true))))
            ));
        }

        return new SearchRequest.Builder()
                .index(namespace + "-" + PRODUCT_INDEX )
                .size(0)
                .query(q -> q.bool(boolQueryBuilder.build()))
                .aggregations("Supplier", a -> a
                        .terms(t -> t
                                .field("supplierParent.keyword")
                                .size(10000)
                        )
                )
                .build();
    }

    public SearchRequest buildBrandAggregation(final List<String> supplierList, final Boolean apparel) {

        /*BoolQueryBuilder conditions = boolQuery();

        conditions.filter(termsQuery("supplierParent.keyword", supplierList));

        if (apparel) {
            conditions.must(termsQuery("apparel", true));
        } else {
            conditions.mustNot(termsQuery("apparel", true));
        }

        return new NativeSearchQueryBuilder()// we build a Elasticsearch native query
                .addAggregation(terms("Brands").field("supplierName.keyword").size(Integer.MAX_VALUE))
                .withQuery(conditions)
                .build();*/

        BoolQuery.Builder boolBuilder = new BoolQuery.Builder();

        if (supplierList != null && !supplierList.isEmpty()) {
            List<FieldValue> supplierValues = supplierList.stream()
                    .map(FieldValue::of)
                    .collect(Collectors.toList());

            TermsQuery supplierQuery = TermsQuery.of(t -> t
                    .field("supplierParent.keyword")
                    .terms(s -> s.value(supplierValues))
            );

            boolBuilder.filter(supplierQuery._toQuery());
        }

        Query apparelQuery = TermsQuery.of(t -> t
                .field("apparel")
                .terms(s -> s.value(List.of(FieldValue.of(true))))
        )._toQuery();

        if (Boolean.TRUE.equals(apparel)) {
            boolBuilder.must(apparelQuery);
        } else {
            boolBuilder.mustNot(apparelQuery);
        }

        Aggregation brandAgg = Aggregation.of(a -> a
                .terms(TermsAggregation.of(t -> t
                        .field("supplierName.keyword")
                        .size(10000)
                ))
        );

        return SearchRequest.of(s -> s
                .index(namespace + "-" + PRODUCT_INDEX)
                .query(q -> q.bool(boolBuilder.build()))
                .aggregations("Brands", brandAgg)
                .size(0)
        );
    }

    public SearchRequest buildSearchQuery(final com.optahub.awi.service.rest.data.search.SearchRequest request) {

        /*final int pageSize = Integer.valueOf(request.getMaxRecs());
        final int startFrom = Integer.valueOf(request.getStartNum());
        final int pageNum = startFrom > 0 ? startFrom/pageSize : 0;
        final NativeSearchQueryBuilder searchQueryBuilder = new NativeSearchQueryBuilder()
               .withPageable(PageRequest.of(pageNum, pageSize));

        BoolQueryBuilder booleanQueryBuild = QueryBuilders.boolQuery();
        if(StringUtils.isNotEmpty(request.getKeywords())) {
            booleanQueryBuild.must(QueryBuilders.queryStringQuery(request.getKeywords()));
        }

        if(StringUtils.isNotEmpty(request.getPriceLow())
                || StringUtils.isNotEmpty(request.getPriceHigh())
                || StringUtils.isNotEmpty(request.getQuantity()))  {

            final Double minPrice = StringUtils.isNotEmpty(request.getPriceLow()) ? Double.valueOf(request.getPriceLow()) : Double.valueOf(0);
            final Double maxPrice = StringUtils.isNotEmpty(request.getPriceHigh()) ? Double.valueOf(request.getPriceHigh()) : Double.MAX_VALUE;
            final Integer quantity = StringUtils.isNotEmpty(request.getQuantity()) ? Integer.valueOf(request.getQuantity()) : 1;
            booleanQueryBuild.must(QueryBuilders.rangeQuery("priceList.price").gte(minPrice).lte(maxPrice));
            booleanQueryBuild.must(QueryBuilders.rangeQuery("priceList.minQuantity").lte(quantity));
            booleanQueryBuild.must(QueryBuilders.rangeQuery("priceList.maxQuantity").gte(quantity));
        }

        if(request.getCategories() != null && request.getCategories().length > 0)  {
            booleanQueryBuild.must(termsQuery("categoryList.category.keyword", Arrays.asList(request.getCategories())));
        }
        if(request.getSubCategories() != null && request.getSubCategories().length > 0)  {
            booleanQueryBuild.must(termsQuery("categoryList.subCategory.keyword", Arrays.asList(request.getSubCategories())));
        }
        if(request.getSuppliers() != null && request.getSuppliers().length > 0)  {
            booleanQueryBuild.must(termsQuery("supplierParent.keyword", Arrays.asList(request.getSuppliers())));
        }
        if(request.getBrands() != null && request.getBrands().length > 0)  {
            booleanQueryBuild.must(termsQuery("supplierName.keyword", Arrays.asList(request.getBrands())));
        }

        if(StringUtils.isNotEmpty(request.getPrefGroups()))  {
            String [] subCategoryArray = request.getPrefGroups().split("\\s*,\\s*");
            booleanQueryBuild.must(termsQuery("categoryList.SubCategory.keyword", Arrays.asList(subCategoryArray)));
        }

        if (request.isApparel()) {
            booleanQueryBuild.must(termsQuery("apparel", true));
        } else {
            booleanQueryBuild.mustNot(termsQuery("apparel", true));
        }

        return searchQueryBuilder.withQuery(booleanQueryBuild).build();*/
        int pageSize = Integer.parseInt(request.getMaxRecs());
        int startFrom = Integer.parseInt(request.getStartNum());

        List<Query> mustQueries = new ArrayList<>();
        List<Query> mustNotQueries = new ArrayList<>();

        if (StringUtils.isNotEmpty(request.getKeywords())) {
            mustQueries.add(Query.of(q -> q
                .queryString(qs -> qs.query(request.getKeywords()))
            ));
        }

        if (StringUtils.isNotEmpty(request.getPriceLow()) || StringUtils.isNotEmpty(request.getPriceHigh()) || StringUtils.isNotEmpty(request.getQuantity())) {
            double minPrice = StringUtils.isNotEmpty(request.getPriceLow()) ? Double.parseDouble(request.getPriceLow()) : 0.0;
            double maxPrice = StringUtils.isNotEmpty(request.getPriceHigh()) ? Double.parseDouble(request.getPriceHigh()) : 10_000;
            int quantity = StringUtils.isNotEmpty(request.getQuantity()) ? Integer.parseInt(request.getQuantity()) : 1;

            mustQueries.add(Query.of(q -> q.range(r -> r.field("priceList.price").gte(JsonData.of(minPrice)).lte(JsonData.of(maxPrice)))));
            mustQueries.add(Query.of(q -> q.range(r -> r.field("priceList.minQuantity").lte(JsonData.of(quantity)))));
            mustQueries.add(Query.of(q -> q.range(r -> r.field("priceList.maxQuantity").gte(JsonData.of(quantity)))));
        }

        if (request.getCategories() != null && request.getCategories().length > 0) {
            mustQueries.add(Query.of(q -> q.terms(t -> t.field("categoryList.category.keyword").terms(terms -> terms.value(Arrays.stream(request.getCategories())
                .map(FieldValue::of)
                .toList())))));
        }

        if (request.getSubCategories() != null && request.getSubCategories().length > 0) {
            mustQueries.add(Query.of(q -> q.terms(t -> t.field("categoryList.subCategory.keyword").terms(terms -> terms.value(Arrays.stream(request.getSubCategories())
                .map(FieldValue::of)
                .toList())))));
        }

        if (request.getSuppliers() != null && request.getSuppliers().length > 0) {
            mustQueries.add(Query.of(q -> q.terms(t -> t.field("supplierParent.keyword").terms(terms -> terms.value(Arrays.stream(request.getSuppliers())
                .map(FieldValue::of)
                .toList())))));
        }

        if (request.getBrands() != null && request.getBrands().length > 0) {
            mustQueries.add(Query.of(q -> q.terms(t -> t.field("supplierName.keyword").terms(terms -> terms.value(Arrays.stream(request.getBrands())
                .map(FieldValue::of)
                .toList())))));
        }

        if (StringUtils.isNotEmpty(request.getPrefGroups())) {
            String[] groups = request.getPrefGroups().split("\\s*,\\s*");
            mustQueries.add(Query.of(q -> q.terms(t -> t.field("categoryList.SubCategory.keyword").terms(terms -> terms.value(Arrays.stream(groups)
                .map(FieldValue::of)
                .toList())))));
        }

        if (request.isApparel()) {
            mustQueries.add(Query.of(q -> q.term(t -> t.field("apparel").value(true))));
        } else {
            mustNotQueries.add(Query.of(q -> q.term(t -> t.field("apparel").value(true))));
        }

        Query boolQuery = Query.of(q -> q.bool(b -> b
            .must(mustQueries)
            .mustNot(mustNotQueries)
        ));

        return SearchRequest.of(s -> s
            .index(namespace + "-" + PRODUCT_INDEX)
            .query(boolQuery)
            .from(startFrom)
            .size(pageSize)
        );
    }
}
