package com.optahub.awi.service.elastic.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.optahub.awi.service.elastic.data.PromoStandardsProduct;
import com.optahub.awi.service.elastic.data.supplier.SupplierData;
import com.optahub.awi.service.rest.data.detail.ProductDetail;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.elasticsearch.core.ElasticsearchOperations;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.document.Document;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
import org.springframework.data.elasticsearch.core.query.IndexQuery;
import org.springframework.data.elasticsearch.core.query.IndexQueryBuilder;
import org.springframework.data.elasticsearch.core.query.Query;
import org.springframework.data.elasticsearch.core.query.UpdateQuery;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch.core.DeleteByQueryRequest;

@Service
public class ElasticSearchService {

    public static final String PRODUCT_INDEX = "product";
    public static final String SUPPLIER_INDEX = "supplier";

    private final String namespace;
    private final ElasticsearchOperations elastic;
    private final ElasticQueryBuilderService queryBuilder;
    private final ElasticsearchClient elasticsearchClient;

    @Autowired
    public ElasticSearchService(final ElasticsearchOperations elastic,
                                final ElasticQueryBuilderService queryBuilder,
                                @Value("${elastic.namespace:uat}") final String namespace, ElasticsearchClient elasticsearchClient) {

        this.elastic = elastic;
        this.queryBuilder = queryBuilder;
        this.namespace = namespace;
        this.elasticsearchClient = elasticsearchClient;
    }

    public void indexProducts(final List<PromoStandardsProduct> productList) {
        try {
            List<IndexQuery> queries = productList.stream()
                    .map(product ->
                            new IndexQueryBuilder()
                                    .withId(product.getId().toString())
                                    .withObject(product).build())
                    .collect(Collectors.toList());

            elastic.bulkIndex(queries, IndexCoordinates.of(this.namespace + "-" +PRODUCT_INDEX));
        } catch (final Exception ex) {
            System.out.println("Unable to load to elastic due to "+ ex.getMessage());
            ex.printStackTrace();
        }
    }

    public void indexSupplier(final List<SupplierData> supplierList) {
        List<IndexQuery> queries = supplierList.stream()
                .map(supplier->
                        new IndexQueryBuilder()
                                .withId(supplier.getId().toString())
                                .withObject(supplier).build())
                .collect(Collectors.toList());

        elastic.bulkIndex(queries, IndexCoordinates.of(this.namespace + "-" +SUPPLIER_INDEX));
    }

    public void removeSupplier(final String supplierCode) throws IOException {
        final DeleteByQueryRequest deleteByQueryRequest = queryBuilder.buildSupplierDataCodeQuery(supplierCode);
        elasticsearchClient.deleteByQuery(deleteByQueryRequest);
    }

    public void removeSupplierProducts(final String supplierCode) throws IOException {
        final DeleteByQueryRequest deleteByQueryRequest = queryBuilder.buildSupplierCodeQuery(supplierCode);
        elasticsearchClient.deleteByQuery(deleteByQueryRequest);
    }

    public SearchHits<PromoStandardsProduct> searchProducts(final Query query) {
        return elastic.search(query, PromoStandardsProduct.class, IndexCoordinates.of(this.namespace + "-" +PRODUCT_INDEX));
    }

    public SearchHits<SupplierData> searchSuppliers(final Query query) {
        return elastic.search(query, SupplierData.class, IndexCoordinates.of(this.namespace + "-" +SUPPLIER_INDEX));
    }

    public PromoStandardsProduct getById(final String productId) {
        return elastic.get(productId, PromoStandardsProduct.class, IndexCoordinates.of(this.namespace + "-" +PRODUCT_INDEX));
    }

    public void updateProduct(final PromoStandardsProduct product) {
        final Document updatedDocument = this.elastic.getElasticsearchConverter().mapObject(product);
        final UpdateQuery query = UpdateQuery.builder(product.getId()).withDocument(updatedDocument).build();
        this.elastic.update(query, IndexCoordinates.of(this.namespace + "-" +PRODUCT_INDEX));
    }
}
