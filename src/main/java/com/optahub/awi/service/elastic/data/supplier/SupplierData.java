package com.optahub.awi.service.elastic.data.supplier;

import java.time.LocalDateTime;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.elasticsearch.annotations.Document;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

    @JsonSerialize
    @JsonIgnoreProperties(ignoreUnknown = true)
    public class SupplierData {

        @JsonProperty("id")
        private String id;
        @JsonProperty("code")
        private String code;
        @JsonProperty("key")
        private String key;
        @JsonProperty("secret")
        private String secret;
        @JsonProperty("name")
        private String name;
        @JsonProperty("supplierParent")
        private String supplierParent;

        @JsonProperty("lastUpdate")
        private LocalDateTime lastUpdate;

        @JsonProperty("disableInventory")
        private boolean disableInventory;

        @JsonProperty("disablePricing")
        private boolean disablePricing;

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getKey() {
            return key;
        }

        public void setKey(String key) {
            this.key = key;
        }

        public String getSecret() {
            return secret;
        }

        public void setSecret(String secret) {
            this.secret = secret;
        }

        public LocalDateTime getLastUpdate() {
            return lastUpdate;
        }

        public void setLastUpdate(LocalDateTime lastUpdate) {
            this.lastUpdate = lastUpdate;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public boolean isDisableInventory() {
            return disableInventory;
        }

        public boolean isDisablePricing() {
            return disablePricing;
        }

        public void setDisablePricing(boolean disablePricing) {
            this.disablePricing = disablePricing;
        }

        public void setDisableInventory(boolean disableInventory) {
            this.disableInventory = disableInventory;
        }

        public String getSupplierParent() {
            return supplierParent;
        }

        public void setSupplierParent(String supplierParent) {
            this.supplierParent = supplierParent;
        }
    }
