package com.optahub.awi.service.elastic.data.constant;

import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

public enum ImagePrioritizationType {
    SANMAR("2001,501,502,500,1007,1008,1006,503,1004,504", "SanMar"),
    ALPHA_BROADER("1006,1002,1003,1001", "alphabroder"),
    SS("1007,1008,1010,1006,1004", "SS"),
    UNKNOWN(StringUtils.EMPTY, StringUtils.EMPTY);

    private List<String> classList;
    private String supplierCode;

    ImagePrioritizationType(final String classId, final String supplierCode) {

        if (StringUtils.isEmpty(classId)) {
            this.classList = Collections.emptyList();
        } else {
            this.classList = Arrays.stream(classId.split(","))
                    .collect(Collectors.toList());
        }

        this.supplierCode = supplierCode;
    }

    public List<String> getClassList() {
        return classList;
    }

    public String getSupplierCode() {
        return supplierCode;
    }

    public static List<String> getPriorityImages(final String supplierCode) {
        final ImagePrioritizationType imagePriority = Arrays.stream(ImagePrioritizationType.values())
                .filter(val -> val.getSupplierCode().equalsIgnoreCase(supplierCode))
                .findFirst().orElse(ImagePrioritizationType.UNKNOWN);

       return imagePriority.classList;
    }
}
