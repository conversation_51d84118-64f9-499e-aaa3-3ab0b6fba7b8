package com.optahub.awi.service.elastic.data.constant;

import java.util.Arrays;
import java.util.Optional;

public enum ApparelSizeType {
    EXTRA_SMALL("XS", 1),
    SMALL("S", 2),
    MEDIUM("M", 3),
    LARGE("L", 4),
    EXTRA_LARGE("XL", 5),
    DOUBLE_XL ("2XL", 6),
    DOUBLE_XL_CUSTOM("XXL", 7),
    TRIPLE_XL("3XL", 8),
    TRIPLE_XL_CUSTOM("XXXL", 9),
    QUAD_XL("4XL", 10),
    QUAD_XL_CUSTOM("XXXXL", 11),

    CUSTOM("", 9999);

    private String labelSize;
    private int position;

    ApparelSizeType(final String labelSize, final int position) {
        this.labelSize = labelSize;
        this.position = position;
    }

    public String getLabelSize() {
        return labelSize;
    }

    public int getPosition() {
        return position;
    }

    public static ApparelSizeType getApparelSize(final String lableSize) {
        final Optional<ApparelSizeType> labelSize = Arrays.stream(ApparelSizeType.values())
                .filter(val -> val.getLabelSize().equalsIgnoreCase(lableSize))
                .findFirst();

       return labelSize.orElse(ApparelSizeType.CUSTOM);
    }
}
