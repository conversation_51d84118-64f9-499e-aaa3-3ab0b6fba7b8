package com.optahub.awi.service.elastic.service;

import com.optahub.awi.service.elastic.data.*;
import com.optahub.awi.service.elastic.data.ProductPrice;
import com.optahub.awi.service.elastic.data.supplier.SupplierData;
import com.optahub.awi.service.promostandards.data.constant.PriceType;
import com.optahub.awi.service.promostandards.data.constant.PriceUOMType;
import com.optahub.awi.service.promostandards.data.inventory.LocationFutureInventory;
import com.optahub.awi.service.promostandards.data.inventory.LocationInventory;
import com.optahub.awi.service.promostandards.data.inventory.ProductInventory;
import com.optahub.awi.service.promostandards.data.inventory.ProductPartInventory;
import com.optahub.awi.service.promostandards.data.price.Decoration;
import com.optahub.awi.service.promostandards.data.price.DecorationCharge;
import com.optahub.awi.service.promostandards.data.price.Location;
import com.optahub.awi.service.promostandards.data.price.PartPriceWrapper;
import com.optahub.awi.service.promostandards.service.MediaContentService;
import com.optahub.awi.service.rest.data.search.SearchResponseItem;

import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.WordUtils;
import org.optahub.promostandards.wsdl.inventory.Inventory;
import org.optahub.promostandards.wsdl.pricing.Configuration;
import org.optahub.promostandards.wsdl.inventory.FutureAvailability;
import org.optahub.promostandards.wsdl.inventory.InventoryLocation;
import org.optahub.promostandards.wsdl.inventory.PartInventoryArray;
import org.optahub.promostandards.wsdl.inventory.Quantity;
import org.optahub.promostandards.wsdl.pricing.Charge;
import org.optahub.promostandards.wsdl.pricing.ChargePrice;
import org.optahub.promostandards.wsdl.pricing.Part;
import org.optahub.promostandards.wsdl.pricing.PartPrice;
import org.optahub.promostandards.wsdl.productservice.Product;
import org.optahub.promostandards.wsdl.productservice.Color;
import org.optahub.promostandards.wsdl.productservice.FobPoint;
import org.optahub.promostandards.wsdl.productservice.ProductMarketingPoint;
import org.optahub.promostandards.wsdl.productservice.ProductPriceGroup;
import org.optahub.promostandards.wsdl.productservice.ShippingPackage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class ElasticSearchObjectMapper {

    private final NumberFormat numberFormat;

    private final NumberFormat currencyFormat;
    private final MediaContentService mediaContentService;

    private static final DecimalFormat TWO_PLACES = new DecimalFormat("0.00");

    @Autowired
    public ElasticSearchObjectMapper(final MediaContentService mediaContentService) {
        numberFormat = NumberFormat.getNumberInstance(Locale.US);
        currencyFormat = NumberFormat.getCurrencyInstance(Locale.US);
        numberFormat.setGroupingUsed(true);



        this.mediaContentService = mediaContentService;
    }

    public PromoStandardsProduct toElasticSearchProduct(final Product promostandardProduct, final SupplierData supplierData) {
        final PromoStandardsProduct product = new PromoStandardsProduct();
        product.setId(supplierData.getCode()+ "-" + promostandardProduct.getProductId());
        product.setUniqueId(UUID.randomUUID().toString());
        product.setProductId(promostandardProduct.getProductId());
        product.setBrandName(promostandardProduct.getProductBrand());
        product.setLineName(promostandardProduct.getLineName());
        product.setDescription(!CollectionUtils.isEmpty(promostandardProduct.getDescription()) ? promostandardProduct.getDescription().get(0) : promostandardProduct.getProductName());
        product.setDescriptionList(!CollectionUtils.isEmpty(promostandardProduct.getDescription()) ? promostandardProduct.getDescription() : Arrays.asList(promostandardProduct.getProductName()));
        product.setDefaultRunCharge(promostandardProduct.getDefaultRunCharge());
        product.setDefaultSetupCharge(promostandardProduct.getDefaultSetupCharge());
        product.setSupplierCode(supplierData.getCode());
        product.setSupplierName(StringUtils.isNotEmpty(promostandardProduct.getProductBrand()) ? promostandardProduct.getProductBrand() : supplierData.getName());
        product.setSupplierParent(StringUtils.isNotEmpty(supplierData.getSupplierParent()) ? supplierData.getSupplierParent() : supplierData.getName());
        product.setImprintSize(promostandardProduct.getImprintSize());
        product.setTitle(promostandardProduct.getProductName());
        product.setImageUrl(promostandardProduct.getPrimaryImageUrl());

        if(promostandardProduct.getProductPriceGroupArray() != null
                && !CollectionUtils.isEmpty(promostandardProduct.getProductPriceGroupArray().getProductPriceGroup())) {
            final List<com.optahub.awi.service.elastic.data.ProductPrice> priceList = new ArrayList<>();
            for(ProductPriceGroup priceGroup : promostandardProduct.getProductPriceGroupArray().getProductPriceGroup()) {
                if(priceGroup.getProductPriceArray() != null &&
                        !CollectionUtils.isEmpty(priceGroup.getProductPriceArray().getProductPrice())) {
                    for (final org.optahub.promostandards.wsdl.productservice.ProductPrice price : priceGroup.getProductPriceArray().getProductPrice()) {
                        final com.optahub.awi.service.elastic.data.ProductPrice productPrice = new com.optahub.awi.service.elastic.data.ProductPrice();
                        productPrice.setPrice(price.getPrice() != null ? price.getPrice().doubleValue() : Double.valueOf(0));
                        productPrice.setDiscountCode(price.getDiscountCode());
                        productPrice.setMinQuantity(price.getQuantityMin());
                        productPrice.setMaxQuantity(price.getQuantityMax());

                        priceList.add(productPrice);
                    }
                }
            }

            product.setPriceList(priceList);
        }

        if(promostandardProduct.getProductCategoryArray() != null
                && !CollectionUtils.isEmpty(promostandardProduct.getProductCategoryArray().getProductCategory())) {
            final List<com.optahub.awi.service.elastic.data.ProductCategory> categoryList = new ArrayList<>();
            for(org.optahub.promostandards.wsdl.productservice.ProductCategory category : promostandardProduct.getProductCategoryArray().getProductCategory()) {
                if(StringUtils.isNotBlank(category.getCategory())) {
                    final com.optahub.awi.service.elastic.data.ProductCategory productCategory = new com.optahub.awi.service.elastic.data.ProductCategory();
                    productCategory.setCategory(WordUtils.capitalizeFully(category.getCategory()));
                    productCategory.setSubCategory(WordUtils.capitalizeFully(category.getSubCategory()));

                    categoryList.add(productCategory);
                }
            }

            product.setCategoryList(categoryList);
        }

        if(promostandardProduct.getProductMarketingPointArray() != null
                && !CollectionUtils.isEmpty(promostandardProduct.getProductMarketingPointArray().getProductMarketingPoint())) {
            final List<com.optahub.awi.service.elastic.data.ProductMarketPoint> markettingList = new ArrayList<>();
            for(ProductMarketingPoint category : promostandardProduct.getProductMarketingPointArray().getProductMarketingPoint()) {
                final com.optahub.awi.service.elastic.data.ProductMarketPoint marketingPoint = new com.optahub.awi.service.elastic.data.ProductMarketPoint();
                marketingPoint.setDescription(category.getPointCopy());
                marketingPoint.setType(category.getPointType());

                markettingList.add(marketingPoint);
            }

            product.setMarketingPoint(markettingList);
        }

        if(promostandardProduct.getFobPointArray() != null
                && !CollectionUtils.isEmpty(promostandardProduct.getFobPointArray().getFobPoint())) {
            final List<FobDetails> fobList = new ArrayList<>();
            for(FobPoint fob : promostandardProduct.getFobPointArray().getFobPoint()) {
                final FobDetails details = new FobDetails();
                details.setCity(fob.getFobCity());
                details.setCountry(fob.getFobCountry());
                details.setId(fob.getFobId());
                details.setState(fob.getFobState());
                details.setPostalCode(fob.getFobPostalCode());

                fobList.add(details);
            }

            product.setFobDetailsList(fobList);
        }

        if(promostandardProduct.getProductPartArray() != null
                && !CollectionUtils.isEmpty(promostandardProduct.getProductPartArray().getProductPart())) {

            this.buildProductParts(promostandardProduct.getProductPartArray().getProductPart(), product);
        }

        return product;
    }

    public SearchResponseItem toSearchResponseItem(final PromoStandardsProduct product, final int count) {
        final SearchResponseItem item = new SearchResponseItem();

        item.setItemNum(product.getProductId());
        if(!CollectionUtils.isEmpty(product.getCategoryList())) {
            item.setCategory(product.getCategoryList().stream()
                    .map(com.optahub.awi.service.elastic.data.ProductCategory::getCategory)
                    .collect(Collectors.joining(", ")));
        }
        item.setCount(String.valueOf(count));
        item.setNet("0.0 - 0.0");
        item.setCompanyName(product.getSupplierParent());
        item.setLineName(product.getSupplierName());
        item.setPrName(product.getTitle());
        item.setProdTime("Please enquire"); //To ask swami.
        item.setProductId(product.getId());
        item.setThumbPicLink(mediaContentService.getPrimaryImage(product).getLeft());
        item.setDescription(product.getDescription());
        item.setSuppId(product.getSupplierCode());

        final Set<String> colorList = new HashSet<>();
        for(final ProductPart part : product.getPartList()) {
            if(part != null && !CollectionUtils.isEmpty(part.getColorList())) {
                colorList.addAll(new HashSet<>(part.getColorList()));
            }
        }



        if(!CollectionUtils.isEmpty(product.getPriceList())) {
            Double minPrice = Double.valueOf(Double.MAX_VALUE);
            Double maxPrice = Double.valueOf(Double.MIN_VALUE);
            for (com.optahub.awi.service.elastic.data.ProductPrice priceData : product.getPriceList()) {
                if (priceData.getPrice() < minPrice) {
                    minPrice = priceData.getPrice();
                }

                if (priceData.getPrice() > maxPrice) {
                    maxPrice = priceData.getPrice();
                }
            }

            item.setPrc(minPrice + " - " + maxPrice);
        } else {
            item.setPrc("unavailable");
        }

        item.setColors(colorList.stream().sorted()
                .map(value -> StringUtils.capitalize(value.toLowerCase()))
                .collect(Collectors.joining(", ")));
        return item;
    }

    public com.optahub.awi.service.promostandards.data.price.ProductPrice buildProductPrice(final Map<PriceType, Configuration> configurationMap, final Boolean isApparel) {
        final com.optahub.awi.service.promostandards.data.price.ProductPrice priceObj = new com.optahub.awi.service.promostandards.data.price.ProductPrice();
        final Set<PriceType> priceTypeSet = configurationMap.keySet();
        final PartPriceWrapper priceWrapper = new PartPriceWrapper();
        for(final PriceType priceType : priceTypeSet) {
            final Configuration configuration = configurationMap.get(priceType);
            if(configuration == null) {
                continue;
            }
            final Map<String, List<com.optahub.awi.service.promostandards.data.price.PartPrice>> priceMap = this.buildPartPrice(configuration.getPartArray());

            if (priceType == PriceType.LIST_DECORATED) {
                priceWrapper.setListPrice(priceMap);
                final List<Location> locationList = buildLocationCharges(configuration.getLocationArray());
                priceObj.setLocationList(locationList);
            } else if (priceType == PriceType.LIST_BLANK) {
                priceWrapper.setListBlankPrice(priceMap);
                final List<Location> locationList = buildLocationCharges(configuration.getLocationArray());
                priceObj.setBlankLocationList(locationList);
            } else if (priceType == PriceType.NET_DECORATED) {
                priceWrapper.setNetPrice(priceMap);
            } else if (priceType == PriceType.NET_BLANK) {
                priceWrapper.setNetBlankPrice(priceMap);
            } else if (priceType == PriceType.CUSTOMER_DECORATED) {
                if(BooleanUtils.isTrue(isApparel)) {
                    priceWrapper.setCustomerPrice(priceMap);
                } else {
                    priceWrapper.setNetPrice(priceMap);
                }
            } else if (priceType == PriceType.CUSTOMER_BLANK) {
                if(BooleanUtils.isTrue(isApparel)) {
                    priceWrapper.setCustomerBlankPrice(priceMap);
                } else {
                    priceWrapper.setNetBlankPrice(priceMap);
                }
            }
        }

        priceObj.setPartPrices(priceWrapper);
        return priceObj;
    }

    public ProductInventory buildInventory(final Inventory inventory) {
        final ProductInventory inventoryObj = new ProductInventory();
        if(inventory != null && inventory.getPartInventoryArray() != null
                && !CollectionUtils.isEmpty(inventory.getPartInventoryArray().getPartInventory())) {
            for(final PartInventoryArray.PartInventory partInventoryObj : inventory.getPartInventoryArray().getPartInventory()) {
                final ProductPartInventory productPartInventory = new ProductPartInventory();
                productPartInventory.setPartId(partInventoryObj.getPartId());
                productPartInventory.setDescription(partInventoryObj.getPartDescription());
                productPartInventory.setPartColor(partInventoryObj.getPartColor());
                productPartInventory.setOptional(!partInventoryObj.isMainPart());
                if(partInventoryObj.getInventoryLocationArray() != null
                        && !CollectionUtils.isEmpty(partInventoryObj.getInventoryLocationArray().getInventoryLocation())) {
                    final List<LocationInventory> locationInventoryList = new ArrayList<>();
                    for(final InventoryLocation locationObj : partInventoryObj.getInventoryLocationArray().getInventoryLocation()) {
                        final LocationInventory locationInventory = new LocationInventory();
                        locationInventory.setLocationCountry(locationObj.getCountry() == null ? "US" : locationObj.getCountry().value());
                        locationInventory.setLocationName(locationObj.getInventoryLocationName());
                        locationInventory.setLocationZip(locationObj.getPostalCode());
                        if(locationObj.getInventoryLocationQuantity() != null
                                && locationObj.getInventoryLocationQuantity().getQuantity() != null) {
                            final Quantity quantity = locationObj.getInventoryLocationQuantity().getQuantity();
                            locationInventory.setUom(quantity.getUom().value());
                            locationInventory.setAvailableQuantity(quantity.getValue() != null ? numberFormat.format(quantity.getValue().doubleValue()) : "0");
                            locationInventory.setAvailableQuantityNumeric(quantity.getValue() != null ? quantity.getValue().doubleValue() : 0d);
                        }

                        if(locationObj.getFutureAvailabilityArray() != null
                                && !CollectionUtils.isEmpty(locationObj.getFutureAvailabilityArray().getFutureAvailability())) {
                            final List<LocationFutureInventory> futureInventoryList = new ArrayList<>();
                            for(final FutureAvailability futureAvailabilityObj : locationObj.getFutureAvailabilityArray().getFutureAvailability()) {
                                final LocationFutureInventory futureInventory = new LocationFutureInventory();
                                final Quantity futureQuantity = futureAvailabilityObj.getQuantity();
                                futureInventory.setUom(futureQuantity.getUom().value());
                                futureInventory.setQuantity(futureQuantity.getValue() != null ? numberFormat.format(futureQuantity.getValue().doubleValue()) : "0");
                                futureInventory.setNumericQuantity(futureQuantity.getValue() != null ? futureQuantity.getValue().doubleValue() : 0d);

                                if(futureAvailabilityObj.getAvailableOn() != null) {
                                    final GregorianCalendar gc = futureAvailabilityObj.getAvailableOn().toGregorianCalendar();
                                    final ZonedDateTime dateTime = gc.toZonedDateTime();
                                    futureInventory.setAvailableFrom(dateTime.format(DateTimeFormatter.ISO_LOCAL_DATE));
                                }

                                futureInventoryList.add(futureInventory);
                            }

                            locationInventory.setFutureList(futureInventoryList);
                        }

                        locationInventoryList.add(locationInventory);
                    }

                    productPartInventory.setLocationInventoryList(locationInventoryList);
                }

                if(partInventoryObj.isMainPart()) {
                    inventoryObj.getMainInventory().put(partInventoryObj.getPartId(), productPartInventory);
                } else {
                    inventoryObj.getOptionalInventory().put(partInventoryObj.getPartId(), productPartInventory);
                }
            }
        }

        return inventoryObj;
    }

    private Map<String, List<com.optahub.awi.service.promostandards.data.price.PartPrice>> buildPartPrice(final Configuration.PartArray partArray) {
        final Map<String, List<com.optahub.awi.service.promostandards.data.price.PartPrice>> returnMap = new LinkedHashMap<>();
        if(partArray != null && !CollectionUtils.isEmpty(partArray.getPart())) {
            for(final Part part : partArray.getPart()) {
                final Part.PartPriceArray partPriceArray = part.getPartPriceArray();
                final List<com.optahub.awi.service.promostandards.data.price.PartPrice> priceList = new ArrayList<>();
                if(partPriceArray != null && !CollectionUtils.isEmpty(partPriceArray.getPartPrice())){
                    for(final PartPrice priceObj : partPriceArray.getPartPrice()) {
                        com.optahub.awi.service.promostandards.data.price.PartPrice price = new com.optahub.awi.service.promostandards.data.price.PartPrice();
                        price.setPrice(priceObj.getPrice() != null ? Double.valueOf(TWO_PLACES.format(priceObj.getPrice().doubleValue())) : Double.valueOf(0));
                        price.setMinQuantity(priceObj.getMinQuantity());
                        priceList.add(price);
                    }
                }

                returnMap.put(part.getPartId(), priceList);
            }
        }

        return returnMap;
    }

    private List<Location> buildLocationCharges(final Configuration.LocationArray locationArray) {
        final List<Location> locationList = new ArrayList<>();
        if(locationArray != null && !CollectionUtils.isEmpty(locationArray.getLocation())) {
            for(final org.optahub.promostandards.wsdl.pricing.Location locationObj : locationArray.getLocation()) {
                final Location location = new Location();
                location.setLocationId(String.valueOf(locationObj.getLocationId()));
                location.setDefaultLocation(locationObj.isDefaultLocation());
                location.setName(locationObj.getLocationName());
                location.setMaxDecoration(locationObj.getMaxDecoration());
                location.setMinDecoration(locationObj.getMinDecoration());
                location.setIncluded(locationObj.getDecorationsIncluded());
                final List<Decoration> decorationList = new ArrayList<>();
                final List<com.optahub.awi.service.promostandards.data.price.PartPrice> priceList = new ArrayList<>();
                if(locationObj.getDecorationArray() != null
                        && !CollectionUtils.isEmpty(locationObj.getDecorationArray().getDecoration())){
                    for(final org.optahub.promostandards.wsdl.pricing.Decoration decorationObj : locationObj.getDecorationArray().getDecoration()) {
                        Decoration decoration = new Decoration();
                        decoration.setDecorationId(String.valueOf(decorationObj.getDecorationId()));
                        decoration.setDiameter(decorationObj.getDecorationDiameter() != null ? numberFormat.format(decorationObj.getDecorationDiameter().doubleValue()) : "0");
                        decoration.setHeight(decorationObj.getDecorationHeight() != null ? numberFormat.format(decorationObj.getDecorationHeight().doubleValue()) : "0");
                        decoration.setWidth(decorationObj.getDecorationWidth() != null ? numberFormat.format(decorationObj.getDecorationWidth().doubleValue()) : "0");
                        decoration.setGeometry(decorationObj.getDecorationGeometry());
                        decoration.setName(decorationObj.getDecorationName());
                        decoration.setUom(PriceUOMType.getLabelForUom(decorationObj.getDecorationUom().value()));
                        decoration.setDefault(decorationObj.isDefaultDecoration());
                        decoration.setLeadTime(decorationObj.getLeadTime() != null && decorationObj.getLeadTime().getValue() != null ? decorationObj.getLeadTime().getValue() : 0);
                        decoration.setRushLeadTime(decorationObj.getRushLeadTime() != null && decorationObj.getRushLeadTime().getValue() != null ? decorationObj.getRushLeadTime().getValue() : 0);
                        decoration.setMaxUnits(decorationObj.getDecorationUnitsMax());
                        decoration.setDefaultLocationSubstitutionAllowed(decorationObj.isAllowSubForDefaultLocation());
                        decoration.setDefaultMethodSubstitutionAllowed(decorationObj.isAllowSubForDefaultMethod());
                        decoration.setUnitsIncluded(decorationObj.getDecorationUnitsIncluded() != null ? decorationObj.getDecorationUnitsIncluded().getValue() : 0);
                        decoration.setUnitsIncludedUom(decorationObj.getDecorationUnitsIncludedUom() != null ? PriceUOMType.getLabelForUom(decorationObj.getDecorationUnitsIncludedUom()) : StringUtils.EMPTY);

                        if(decorationObj.getChargeArray() != null
                                && !CollectionUtils.isEmpty(decorationObj.getChargeArray().getCharge())) {
                            final List<DecorationCharge> decorationCharges = new ArrayList<>();
                            for (final Charge chargeObj : decorationObj.getChargeArray().getCharge()) {
                                final DecorationCharge decorationCharge = new DecorationCharge();
                                decorationCharge.setChargeId(String.valueOf(chargeObj.getChargeId()));
                                decorationCharge.setChargeName(chargeObj.getChargeName());
                                decorationCharge.setChargeType(chargeObj.getChargeType().value());
                                decorationCharge.setChargeDescription(chargeObj.getChargeDescription());

                                if(chargeObj.getChargePriceArray() != null
                                        && !CollectionUtils.isEmpty(chargeObj.getChargePriceArray().getChargePrice())) {
                                    final List<com.optahub.awi.service.promostandards.data.price.ChargePrice> chargePriceList = new ArrayList<>();
                                    for(final ChargePrice chargePriceObj : chargeObj.getChargePriceArray().getChargePrice()) {
                                        final com.optahub.awi.service.promostandards.data.price.ChargePrice chargePrice = new com.optahub.awi.service.promostandards.data.price.ChargePrice();
                                        chargePrice.setPrice(chargePriceObj.getPrice() != null ? currencyFormat.format(chargePriceObj.getPrice().doubleValue()) : "0");
                                        chargePrice.setRepeatPrice(chargePriceObj.getRepeatPrice() != null ? currencyFormat.format(chargePriceObj.getRepeatPrice().doubleValue()) : "0");
                                        chargePrice.setMinXQuantity(chargePriceObj.getXMinQty());
                                        chargePrice.setMinYQuantity(chargePriceObj.getYMinQty());
                                        chargePrice.setxUom(PriceUOMType.getLabelForUom(chargePriceObj.getXUom().value()));
                                        chargePrice.setyUom(PriceUOMType.getLabelForUom(chargePriceObj.getYUom().value()));
                                        chargePrice.setDiscountCode(chargePriceObj.getDiscountCode());
                                        chargePrice.setRepeatDiscountCode(chargePriceObj.getRepeatDiscountCode());

                                        chargePriceList.add(chargePrice);
                                    }

                                    Collections.sort(chargePriceList,
                                            Comparator.comparing(com.optahub.awi.service.promostandards.data.price.ChargePrice::getMinXQuantity));
                                    decorationCharge.setPriceList(chargePriceList);
                                }

                                decorationCharges.add(decorationCharge);
                            }

                            Collections.sort(decorationCharges, Comparator.comparing(DecorationCharge::getChargeType).reversed());
                            decoration.setChargeList(decorationCharges);
                        }
                        decorationList.add(decoration);
                    }

                    Collections.sort(decorationList, (obj1, obj2) ->
                            Boolean.compare(obj2.getDefault(), obj1.getDefault()));
                    location.setDecorationList(decorationList);
                }

                locationList.add(location);
            }
        }

        Collections.sort(locationList, (obj1, obj2) ->
                Boolean.compare(obj2.isDefaultLocation(), obj1.isDefaultLocation()));
        return locationList;
    }

    private void buildProductParts(final List<Product.ProductPartArray.ProductPart> productParts, final PromoStandardsProduct product) {
        final List<ProductPart> partList = new ArrayList<>();
        for(final Product.ProductPartArray.ProductPart part : productParts) {
           final ProductPart productPart  = new ProductPart();
           productPart.setPartId(part.getPartId());
           productPart.setShape(part.getShape());
           productPart.setPrimaryMaterial(part.getPrimaryMaterial());
           productPart.setCountryOfOrigin(part.getCountryOfOrigin() != null ? part.getCountryOfOrigin().value() : null);

           if(part.getDimension() != null) {
               final ProductPartDimension partDimension = new ProductPartDimension();
               partDimension.setDimensionUnit(part.getDimension().getDimensionUom() != null ? part.getDimension().getDimensionUom().value() : null);
               partDimension.setWeightUnit(part.getDimension().getWeightUom() != null ? part.getDimension().getWeightUom().value() : null);
               partDimension.setDepth(part.getDimension().getDepth() != null ? part.getDimension().getDepth().doubleValue() : Double.valueOf(0));
               partDimension.setHeight(part.getDimension().getHeight() != null ? part.getDimension().getHeight().doubleValue() : Double.valueOf(0));
               partDimension.setWidth(part.getDimension().getWidth() != null ? part.getDimension().getWidth().doubleValue() : Double.valueOf(0));
               partDimension.setWeight(part.getDimension().getWeight() != null ? part.getDimension().getWeight().doubleValue() : Double.valueOf(0));

               productPart.setDimension(partDimension);
           }

           if(part.getColorArray() != null && !CollectionUtils.isEmpty(part.getColorArray().getColor())) {

               final String partColor = part.getColorArray().getColor().get(0).getColorName();
               final String standardColor = part.getColorArray().getColor().get(0).getStandardColorName();
               productPart.setColorList(Arrays.asList(StringUtils.isNotEmpty(standardColor) ? standardColor.toUpperCase() : partColor.toUpperCase()));

               List<ProductPartColor> partColors = new ArrayList<>();
               for(final Color color : part.getColorArray().getColor()) {
                   final ProductPartColor partColorItem = new ProductPartColor();
                   partColorItem.setColorName(color.getColorName());
                   partColorItem.setStandardColorName(color.getStandardColorName());
                   partColorItem.setApproximatePpms(color.getApproximatePms());
                   partColorItem.setHexValue(color.getHex());

                   partColors.add(partColorItem);
               }

               productPart.setPartColors(partColors);
           }

           if(part.getApparelSize() != null) {
               //product is apparel
               product.setApparel(true);

               ProductPartApparelSize apparelSize = new ProductPartApparelSize();
               apparelSize.setStyle(part.getApparelSize().getApparelStyle().value());
               final String labelSize = part.getApparelSize().getLabelSize();
               if(labelSize != null && labelSize.toUpperCase().equals("CUSTOM")) {
                   apparelSize.setSize(part.getApparelSize().getCustomSize());
               } else {
                   apparelSize.setSize(labelSize);
               }

               productPart.setApparelSize(apparelSize);
           } else {
               product.setApparel(false);
           }



           if(part.getShippingPackageArray() != null
                   && !CollectionUtils.isEmpty(part.getShippingPackageArray().getShippingPackage())) {
               final List<ProductPartShipping> partShippingList = new ArrayList<>();
               for(final ShippingPackage shippingPackage : part.getShippingPackageArray().getShippingPackage()) {
                   final ProductPartShipping shipping = new ProductPartShipping();
                   shipping.setDepth(shippingPackage.getDepth() != null ? shippingPackage.getDepth().doubleValue() : Double.valueOf(0));
                   shipping.setWidth(shippingPackage.getWidth() != null ? shippingPackage.getWidth().doubleValue() : Double.valueOf(0));
                   shipping.setHeight(shippingPackage.getHeight() != null ? shippingPackage.getHeight().doubleValue() : Double.valueOf(0));
                   shipping.setWeight(shippingPackage.getWeight() != null ? shippingPackage.getWeight().doubleValue() : Double.valueOf(0));
                   shipping.setDimensionUnit(shippingPackage.getDimensionUom() != null ? shippingPackage.getDimensionUom().value() : null);
                   shipping.setWeightUnit(shippingPackage.getWeightUom() != null ? shippingPackage.getWeightUom().value() : null);
                   shipping.setDescription(shippingPackage.getDescription());
                   shipping.setPackageType(shippingPackage.getPackageType());
                   shipping.setQuantity(shippingPackage.getQuantity() != null ? shippingPackage.getQuantity().doubleValue() : Double.valueOf(0));

                   partShippingList.add(shipping);
               }
               productPart.setShippingList(partShippingList);
           }


           partList.add(productPart);
        }

        product.setPartList(partList);
    }
}
