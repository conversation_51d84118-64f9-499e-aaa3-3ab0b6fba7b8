package com.optahub.awi.service.elastic.data;

import java.util.List;

public class ProductPartColor {
    private String colorName;

    private String approximatePpms;

    private String standardColorName;

    private String hexValue;

    public String getColorName() {
        return colorName;
    }

    public void setColorName(String colorName) {
        this.colorName = colorName;
    }

    public String getApproximatePpms() {
        return approximatePpms;
    }

    public void setApproximatePpms(String approximatePpms) {
        this.approximatePpms = approximatePpms;
    }

    public String getStandardColorName() {
        return standardColorName;
    }

    public void setStandardColorName(String standardColorName) {
        this.standardColorName = standardColorName;
    }

    public String getHexValue() {
        return hexValue;
    }

    public void setHexValue(String hexValue) {
        this.hexValue = hexValue;
    }
}
