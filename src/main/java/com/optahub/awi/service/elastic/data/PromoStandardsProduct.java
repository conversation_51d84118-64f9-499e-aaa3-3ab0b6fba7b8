package com.optahub.awi.service.elastic.data;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.optahub.awi.service.promostandards.data.media.ImageReference;
import com.optahub.awi.service.rest.data.detail.ProductDetail;
import org.springframework.data.elasticsearch.annotations.DateFormat;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.time.Instant;
import java.util.List;
@JsonIgnoreProperties(ignoreUnknown = true)
public class PromoStandardsProduct {
    private String id;

    private String uniqueId;
    private String productId;
    private String supplierCode;

    private String supplierParent;
    private String supplierName;
    private String brandName;
    private String lineName;
    private String title;
    private String description;
    private String defaultSetupCharge;
    private String defaultRunCharge;
    private String imprintSize;
    private String imageUrl;

    private Boolean apparel;
    private List<ProductMarketPoint> marketingPoint;
    private List<ProductCategory> categoryList;
    private List<ProductPart> partList;
    private List<ProductPrice> priceList;
    private List<FobDetails> fobDetailsList;

    private List<ImageReference> imageList;

    private List<String> descriptionList;

    private ProductDetail cacheObject;

    @Field(type = FieldType.Date, format = DateFormat.date_time)
    private Instant cacheTime;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }

    public String getSupplierCode() {
        return supplierCode;
    }

    public void setSupplierCode(String supplierCode) {
        this.supplierCode = supplierCode;
    }

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    public String getLineName() {
        return lineName;
    }

    public void setLineName(String lineName) {
        this.lineName = lineName;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDefaultSetupCharge() {
        return defaultSetupCharge;
    }

    public void setDefaultSetupCharge(String defaultSetupCharge) {
        this.defaultSetupCharge = defaultSetupCharge;
    }

    public String getDefaultRunCharge() {
        return defaultRunCharge;
    }

    public void setDefaultRunCharge(String defaultRunCharge) {
        this.defaultRunCharge = defaultRunCharge;
    }

    public String getImprintSize() {
        return imprintSize;
    }

    public void setImprintSize(String imprintSize) {
        this.imprintSize = imprintSize;
    }

    public List<ProductMarketPoint> getMarketingPoint() {
        return marketingPoint;
    }

    public void setMarketingPoint(List<ProductMarketPoint> marketingPoint) {
        this.marketingPoint = marketingPoint;
    }

    public List<ProductCategory> getCategoryList() {
        return categoryList;
    }

    public void setCategoryList(List<ProductCategory> categoryList) {
        this.categoryList = categoryList;
    }

    public List<ProductPart> getPartList() {
        return partList;
    }

    public void setPartList(List<ProductPart> partList) {
        this.partList = partList;
    }

    public List<ProductPrice> getPriceList() {
        return priceList;
    }

    public void setPriceList(List<ProductPrice> priceList) {
        this.priceList = priceList;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public List<FobDetails> getFobDetailsList() {
        return fobDetailsList;
    }

    public void setFobDetailsList(List<FobDetails> fobDetailsList) {
        this.fobDetailsList = fobDetailsList;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public List<ImageReference> getImageList() {
        return imageList;
    }

    public void setImageList(List<ImageReference> imageList) {
        this.imageList = imageList;
    }

    public String getSupplierParent() {
        return supplierParent;
    }

    public void setSupplierParent(String supplierParent) {
        this.supplierParent = supplierParent;
    }

    public String getUniqueId() {
        return uniqueId;
    }

    public void setUniqueId(String uniqueId) {
        this.uniqueId = uniqueId;
    }

    public Boolean getApparel() {
        return apparel;
    }

    public void setApparel(Boolean apparel) {
        this.apparel = apparel;
    }

    public List<String> getDescriptionList() {
        return descriptionList;
    }

    public void setDescriptionList(List<String> descriptionList) {
        this.descriptionList = descriptionList;
    }

    public ProductDetail getCacheObject() {
        return cacheObject;
    }

    public void setCacheObject(ProductDetail cacheObject) {
        this.cacheObject = cacheObject;
    }

    public Instant getCacheTime() {
        return cacheTime;
    }

    public void setCacheTime(Instant cacheTime) {
        this.cacheTime = cacheTime;
    }
}
