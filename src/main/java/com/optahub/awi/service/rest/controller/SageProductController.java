package com.optahub.awi.service.rest.controller;

import com.optahub.awi.service.rest.data.detail.ProductDataResponseWrapper;
import com.optahub.awi.service.rest.data.detail.ProductDetail;
import com.optahub.awi.service.sage.service.SageProductService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
public class SageProductController {

    private final SageProductService service;

    @Autowired
    public SageProductController(final SageProductService service) {
        this.service = service;
    }

    @GetMapping("/sage/product/{id}")
    public ResponseEntity<ProductDataResponseWrapper> getProduct(@PathVariable final String id) {
        final ProductDataResponseWrapper response = service.getProduct(id);

        return ResponseEntity.ok(response);
    }
}
