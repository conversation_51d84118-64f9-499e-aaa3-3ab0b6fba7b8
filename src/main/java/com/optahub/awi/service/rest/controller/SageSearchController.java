package com.optahub.awi.service.rest.controller;

import com.optahub.awi.service.rest.data.sage.search.SageSearchRequest;
import com.optahub.awi.service.rest.data.search.SearchResponse;
import com.optahub.awi.service.sage.service.SageSearchService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class SageSearchController {

    private final SageSearchService service;

    @Autowired
    public SageSearchController(final SageSearchService service) {
        this.service = service;
    }

    @PostMapping("/sage/search")
    public ResponseEntity<SearchResponse> getCategories(@RequestBody final SageSearchRequest request) {
        final SearchResponse response = service.searchSage(request);

        return ResponseEntity.ok(response);
    }
}
