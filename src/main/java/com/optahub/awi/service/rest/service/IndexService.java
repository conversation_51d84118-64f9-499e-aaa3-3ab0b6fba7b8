package com.optahub.awi.service.rest.service;

import com.optahub.awi.service.elastic.data.PromoStandardsProduct;
import com.optahub.awi.service.elastic.data.supplier.SupplierData;
import com.optahub.awi.service.elastic.service.ElasticSearchObjectMapper;
import com.optahub.awi.service.elastic.service.ElasticSearchService;
import com.optahub.awi.service.promostandards.client.ProductServiceClient;
import com.optahub.awi.service.promostandards.factory.SupplierConfigFactory;
import com.optahub.awi.service.promostandards.service.MediaContentService;
import com.optahub.awi.service.promostandards.service.SupplierService;

import org.optahub.promostandards.wsdl.productservice.Product;
import org.optahub.promostandards.wsdl.productservice.GetProductResponse;
import org.optahub.promostandards.wsdl.productservice.GetProductSellableResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

import static java.util.stream.Collectors.groupingBy;

@Service
public class IndexService {

    private final SupplierConfigFactory supplierConfigFactory;
    private final SupplierService supplierService;
    private final ElasticSearchObjectMapper elasticObjectMapper;
    private final ElasticSearchService elasticSearchService;

    private final MediaContentService mediaContentService;

    @Autowired
    public IndexService(final SupplierConfigFactory supplierConfigFactory,
                        final SupplierService supplierService,
                        final ElasticSearchObjectMapper elasticObjectMapper,
                        final ElasticSearchService elasticSearchService,
                        final MediaContentService mediaContentService) {

        this.supplierConfigFactory = supplierConfigFactory;
        this.supplierService = supplierService;
        this.elasticObjectMapper = elasticObjectMapper;
        this.elasticSearchService = elasticSearchService;
        this.mediaContentService = mediaContentService;
    }

    public void indexSupplierAsync(final String supplierCode) {
          CompletableFuture<List<String>> future
                    = CompletableFuture.supplyAsync(() -> this.indexSupplier(supplierCode));

    }

    public List<String> indexSupplier(final String supplierCode) {

        final SupplierData supplierData = supplierService.getSupplier(supplierCode);
        List<PromoStandardsProduct> productList = new ArrayList<>();
        int callCount = 0;
        if(supplierData != null) {
            final ProductServiceClient productClient = supplierConfigFactory.configureProductClient(supplierData);
            timeout(callCount, supplierCode);
            callCount++;
            final Optional<GetProductSellableResponse> sellableResponseOpt = productClient.getProductSellable();
            if(sellableResponseOpt.isPresent()) {
                final GetProductSellableResponse sellableResponse = sellableResponseOpt.get();
                final GetProductSellableResponse.ProductSellableArray sellableArray = sellableResponse
                        .getProductSellableArray();

                if (sellableArray != null && !CollectionUtils.isEmpty(sellableArray.getProductSellable())) {
                    final List<GetProductSellableResponse.ProductSellableArray.ProductSellable> sellableList = sellableArray.getProductSellable();
                    final Map<String, List<GetProductSellableResponse.ProductSellableArray.ProductSellable>> groupMap = sellableList.stream()
                            .collect(groupingBy(GetProductSellableResponse.ProductSellableArray.ProductSellable::getProductId));
                    System.out.println("Trying to upload "+groupMap.keySet().size()+" sellable products from promo standards.");
                    int count = 0;
                    for(final String productId : groupMap.keySet()) {
                        System.out.println("Fetching product id : "+productId);
                        timeout(callCount, supplierCode);
                        callCount++;
                        final Optional<GetProductResponse> productResponseOpt = productClient.getProductDetails(productId);
                        if(productResponseOpt.isPresent()) {
                            System.out.println("Mapping product "+productId);
                            final GetProductResponse productResponse = productResponseOpt.get();
                            if(productResponse.getProduct() != null) {
                                try {
                                    final Product product = productResponse.getProduct();
                                    final PromoStandardsProduct converted = elasticObjectMapper.toElasticSearchProduct(product, supplierData);
                                    timeout(callCount, supplierCode);
                                    callCount++;
                                    converted.setImageList(mediaContentService.getImages(supplierCode, productId));
                                    productList.add(converted);
                                }catch (final Exception ex) {
                                    System.out.println("Unable to map product "+productId);
                                }
                            } else {
                                System.out.println("Unable to load product id : "+productId+" due to "+productResponse.getServiceMessageArray().getServiceMessage());
                            }
                        }

                        count++;

                        if (count % 200 == 0) {
                            System.out.println("Loading "+productList.size()+" to elastic.");
                            elasticSearchService.indexProducts(productList);

                            productList = new ArrayList<>();
                        }

                    }

                    System.out.println("Loading "+productList.size()+" to elastic.");
                    elasticSearchService.indexProducts(productList);
                }
            }
        }

        System.out.println("Indexing ended.");
        return Collections.EMPTY_LIST;
    }

    public void removeSupplierProducts(final String supplierCode) throws IOException {
        elasticSearchService.removeSupplierProducts(supplierCode);
    }

    private void timeout(int callcount, String supplierCode) {
        try {
            if(supplierCode.equals("alphabroder") || supplierCode.equals("SS")) {
                TimeUnit.SECONDS.sleep(1);
            } else if(supplierCode.equals("SS") && (callcount % 50 == 0)) {
                TimeUnit.SECONDS.sleep(60);
            } else if(supplierCode.equals("alphabroder") && (callcount % 100 == 0)) {
                TimeUnit.SECONDS.sleep(60);
            }
        } catch (final InterruptedException ex) {
            System.out.println("Timeout interrupted ");
            ex.printStackTrace();
        }
    }
}
