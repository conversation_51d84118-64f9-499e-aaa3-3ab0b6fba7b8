package com.optahub.awi.service.rest.data.detail;

import com.optahub.awi.service.elastic.data.ProductPartDimension;
import com.optahub.awi.service.elastic.data.ProductPartShipping;
import com.optahub.awi.service.promostandards.data.inventory.LocationInventory;
import com.optahub.awi.service.promostandards.data.media.ImageReference;
import com.optahub.awi.service.promostandards.data.price.PartPrice;

import java.util.List;

public class ApparelInventory {
    private String size;
    private List<LocationInventory> inventory;

    public String getSize() {
        return size;
    }

    public void setSize(String size) {
        this.size = size;
    }

    public List<LocationInventory> getInventory() {
        return inventory;
    }

    public void setInventory(List<LocationInventory> inventory) {
        this.inventory = inventory;
    }
}
