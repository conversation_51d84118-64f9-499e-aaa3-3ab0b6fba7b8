package com.optahub.awi.service.rest.service;

import com.optahub.awi.service.rest.data.aggregate.AggregateResponse;
import com.optahub.awi.service.sage.data.constants.SageListType;
import com.optahub.awi.service.sage.service.SageListService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class SageAggregateService {

    private final SageListService sageListService;

    @Autowired
    public SageAggregateService(final SageListService sageListService) {
        this.sageListService = sageListService;
    }


    public AggregateResponse getAggregate(final SageListType listType) {

        final AggregateResponse sageResponse = sageListService.getListData(listType);
        return sageResponse;
    }
}
