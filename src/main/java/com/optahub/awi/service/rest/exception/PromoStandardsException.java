package com.optahub.awi.service.rest.exception;

import org.springframework.http.HttpStatus;

public class PromoStandardsException extends RuntimeException {
    private final Integer statusCode;

    public PromoStandardsException() {
        super("Something went wrong");
        this.statusCode = HttpStatus.INTERNAL_SERVER_ERROR.value();
    }

    public PromoStandardsException(final String message) {
        super(message);
        this.statusCode = HttpStatus.INTERNAL_SERVER_ERROR.value();
    }

    public PromoStandardsException(final HttpStatus status, final String message) {
        super(message);
        this.statusCode = status.value();
    }

    public Integer getStatusCode() {
        return this.statusCode;
    }
}
