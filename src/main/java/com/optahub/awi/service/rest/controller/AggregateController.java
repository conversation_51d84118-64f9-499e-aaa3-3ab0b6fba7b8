package com.optahub.awi.service.rest.controller;

import com.optahub.awi.service.rest.data.aggregate.AggregateResponse;
import com.optahub.awi.service.rest.data.aggregate.NameObject;
import com.optahub.awi.service.rest.data.aggregate.TextObject;
import com.optahub.awi.service.rest.exception.PromoStandardsException;
import com.optahub.awi.service.rest.service.AggregateService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

@RestController
public class AggregateController {

    private final AggregateService service;

    @Autowired
    public AggregateController(final AggregateService service) {
        this.service = service;
    }

    @GetMapping("/aggregate/categories")
    ResponseEntity<AggregateResponse> getCategories() {
        try {
            //final AggregateResponse response = service.getIndexedCategories(suppliers, brands);
            final AggregateResponse response = service.getIndexedCategories();
            /*if (!responseList.isEmpty()) {
                return ResponseEntity.ok(responseList);
            }*/

            return ResponseEntity.ok(response);
        } catch (final PromoStandardsException | IOException pse) {
            pse.printStackTrace();
         //   return ResponseEntity.status(pse.getStatusCode()).body(Arrays.asList(pse.getMessage()));
        }

        return ResponseEntity.notFound().build();
    }

    @GetMapping("/aggregate/v2/categories")
    ResponseEntity<AggregateResponse> getCategories(@RequestParam(value = "suppliers", required = false) final String suppliers,
                                                    @RequestParam(value = "brands", required = false) final String brands,
                                                    @RequestParam(value = "apparel", required = false) final Optional<Boolean> apparel) throws IOException {
        try {
            final AggregateResponse response = service.getIndexedCategories(suppliers, brands, apparel.orElse(Boolean.FALSE));
            //final AggregateResponse response = service.getIndexedCategories();
            /*if (!responseList.isEmpty()) {
                return ResponseEntity.ok(responseList);
            }*/

            return ResponseEntity.ok(response);
        } catch (final PromoStandardsException pse) {
            pse.printStackTrace();
            //   return ResponseEntity.status(pse.getStatusCode()).body(Arrays.asList(pse.getMessage()));
        }

        return ResponseEntity.notFound().build();
    }

    @GetMapping("/aggregate/subCategories")
    ResponseEntity<List<TextObject>> getCategories(@RequestParam("categories") final String categories) {
        try {
            //final AggregateResponse response = service.getSubCategory(suppliers, brands, categories);
            final List<TextObject> response = service.getSubCategory(categories);
            if (!response.isEmpty()) {
                return ResponseEntity.ok(response);
            }

            return ResponseEntity.ok(response);
        } catch (final PromoStandardsException | IOException pse) {
            //   return ResponseEntity.status(pse.getStatusCode()).body(Arrays.asList(pse.getMessage()));
        }

        return ResponseEntity.notFound().build();
    }

    @GetMapping("/aggregate/v2/subCategories")
    ResponseEntity<AggregateResponse> getSubCategories(@RequestParam(value = "suppliers", required = false) final String suppliers,
                                                       @RequestParam(value = "brands", required = false) final String brands,
                                                       @RequestParam(value = "categories", required = false) final String categories,
                                                       @RequestParam(value = "apparel", required = false) final Optional<Boolean> apparel) throws IOException {
        try {
            final AggregateResponse response = service.getSubCategory(
                    StringUtils.defaultIfEmpty(suppliers, null),
                    StringUtils.defaultIfEmpty(brands, null),
                    StringUtils.defaultIfEmpty(categories, null),
                    apparel.orElse(Boolean.FALSE));
            //final List<NameObject> response = service.getSubCategory(categories);
            /*if (!responseList.isEmpty()) {
                return ResponseEntity.ok(responseList);
            }*/

            return ResponseEntity.ok(response);
        } catch (final PromoStandardsException pse) {
            //   return ResponseEntity.status(pse.getStatusCode()).body(Arrays.asList(pse.getMessage()));
        }

        return ResponseEntity.notFound().build();
    }

    @GetMapping("/aggregate/suppliers")
    ResponseEntity<AggregateResponse> getSuppliers(@RequestParam(value = "apparel", required = false) final Optional<Boolean> apparel) {
        try {
            final AggregateResponse response = service.getSuppliers(apparel.orElse(Boolean.FALSE));
            /*if (!response.getMessage().isEmpty()) {
                return ResponseEntity.ok(response);
            }*/

            return ResponseEntity.ok(response);
        } catch (final PromoStandardsException | IOException pse) {
            //   return ResponseEntity.status(pse.getStatusCode()).body(Arrays.asList(pse.getMessage()));
            pse.printStackTrace();
        }

        return ResponseEntity.notFound().build();
    }

    @GetMapping("/aggregate/brands")
    ResponseEntity<AggregateResponse> getBrands(@RequestParam("suppliers") final String suppliers,
                                                @RequestParam(value = "apparel", required = false) final Optional<Boolean> apparel) {
        try {
            final AggregateResponse response = service.getBrands(suppliers, apparel.orElse(Boolean.FALSE));
            return ResponseEntity.ok(response);
        } catch (final IOException | PromoStandardsException pse) {
            //   return ResponseEntity.status(pse.getStatusCode()).body(Arrays.asList(pse.getMessage()));
            pse.printStackTrace();
        }

        return ResponseEntity.notFound().build();
    }

}
