package com.optahub.awi.service.rest.data.detail;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.optahub.awi.service.promostandards.data.media.ImageReference;
import com.optahub.awi.service.promostandards.data.price.Location;

import java.util.ArrayList;
import java.util.List;

public class SupplierDetail {


    @JsonProperty("SuppID")
    private String suppID;
    @JsonProperty("CoName")
    private String coName;
    @JsonProperty("LineName")
    private String lineName;
    @JsonProperty("MAddr")
    private String mAddr;
    @JsonProperty("MCity")
    private String mCity;
    @JsonProperty("MState")
    private String mState;
    @JsonProperty("MZip")
    private String mZip;
    @JsonProperty("MCountry")
    private String mCountry;
    @JsonProperty("Tel")
    private String tel;
    @JsonProperty("TollFreeTel")
    private String tollFreeTel;
    @JsonProperty("Fax")
    private String fax;
    @JsonProperty("Email")
    private String email;
    @JsonProperty("Web")
    private String web;
    @JsonProperty("ArtContactEmail")
    private String artContactEmail;

    @JsonProperty("CatYear")
    private String catYear;

    @JsonProperty("CatExpOn")
    private String catExpOn;

    @JsonProperty("CatCurrency")
    private String catCurrency;

    @JsonProperty("Comment")
    private String comment;

    @JsonProperty("PrefGroupIDs")
    private String prefGroupIds;

    @JsonProperty("PrefGroup")
    private String prefGroup;

    @JsonProperty("SAddr")
    private String sAddr;

    @JsonProperty("SCity")
    private String sCity;

    @JsonProperty("SState")
    private String sState;

    @JsonProperty("SZip")
    private String sZip;

    @JsonProperty("SCountry")
    private String sCountry;

    @JsonProperty("PersCustNum")
    private String persCustNum;

    @JsonProperty("ContactName")
    private String contactName;

    @JsonProperty("ArtContactName")
    private String artContactName;

    public String getSuppID() {
        return suppID;
    }

    public void setSuppID(String suppID) {
        this.suppID = suppID;
    }

    public String getCoName() {
        return coName;
    }

    public void setCoName(String coName) {
        this.coName = coName;
    }

    public String getLineName() {
        return lineName;
    }

    public void setLineName(String lineName) {
        this.lineName = lineName;
    }

    public String getmAddr() {
        return mAddr;
    }

    public void setmAddr(String mAddr) {
        this.mAddr = mAddr;
    }

    public String getmCity() {
        return mCity;
    }

    public void setmCity(String mCity) {
        this.mCity = mCity;
    }

    public String getmState() {
        return mState;
    }

    public void setmState(String mState) {
        this.mState = mState;
    }

    public String getmZip() {
        return mZip;
    }

    public void setmZip(String mZip) {
        this.mZip = mZip;
    }

    public String getmCountry() {
        return mCountry;
    }

    public void setmCountry(String mCountry) {
        this.mCountry = mCountry;
    }

    public String getTel() {
        return tel;
    }

    public void setTel(String tel) {
        this.tel = tel;
    }

    public String getTollFreeTel() {
        return tollFreeTel;
    }

    public void setTollFreeTel(String tollFreeTel) {
        this.tollFreeTel = tollFreeTel;
    }

    public String getFax() {
        return fax;
    }

    public void setFax(String fax) {
        this.fax = fax;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getWeb() {
        return web;
    }

    public void setWeb(String web) {
        this.web = web;
    }

    public String getArtContactEmail() {
        return artContactEmail;
    }

    public void setArtContactEmail(String artContactEmail) {
        this.artContactEmail = artContactEmail;
    }

    public String getCatYear() {
        return catYear;
    }

    public void setCatYear(String catYear) {
        this.catYear = catYear;
    }

    public String getCatExpOn() {
        return catExpOn;
    }

    public void setCatExpOn(String catExpOn) {
        this.catExpOn = catExpOn;
    }

    public String getCatCurrency() {
        return catCurrency;
    }

    public void setCatCurrency(String catCurrency) {
        this.catCurrency = catCurrency;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public String getPrefGroupIds() {
        return prefGroupIds;
    }

    public void setPrefGroupIds(String prefGroupIds) {
        this.prefGroupIds = prefGroupIds;
    }

    public String getPrefGroup() {
        return prefGroup;
    }

    public void setPrefGroup(String prefGroup) {
        this.prefGroup = prefGroup;
    }

    public String getsAddr() {
        return sAddr;
    }

    public void setsAddr(String sAddr) {
        this.sAddr = sAddr;
    }

    public String getsCity() {
        return sCity;
    }

    public void setsCity(String sCity) {
        this.sCity = sCity;
    }

    public String getsState() {
        return sState;
    }

    public void setsState(String sState) {
        this.sState = sState;
    }

    public String getsZip() {
        return sZip;
    }

    public void setsZip(String sZip) {
        this.sZip = sZip;
    }

    public String getsCountry() {
        return sCountry;
    }

    public void setsCountry(String sCountry) {
        this.sCountry = sCountry;
    }

    public String getPersCustNum() {
        return persCustNum;
    }

    public void setPersCustNum(String persCustNum) {
        this.persCustNum = persCustNum;
    }

    public String getContactName() {
        return contactName;
    }

    public void setContactName(String contactName) {
        this.contactName = contactName;
    }

    public String getArtContactName() {
        return artContactName;
    }

    public void setArtContactName(String artContactName) {
        this.artContactName = artContactName;
    }
}
