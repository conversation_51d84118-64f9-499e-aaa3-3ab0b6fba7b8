package com.optahub.awi.service.rest.controller;

import com.optahub.awi.service.rest.data.aggregate.AggregateResponse;
import com.optahub.awi.service.rest.service.SageAggregateService;
import com.optahub.awi.service.sage.data.constants.SageListType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class SageAggregateController {

    private final SageAggregateService service;

    @Autowired
    public SageAggregateController(final SageAggregateService service) {
        this.service = service;
    }

    @GetMapping("/sage/categories")
    public ResponseEntity<AggregateResponse> getCategories() {
        final AggregateResponse sageAggregateResponse = service.getAggregate(SageListType.CATEGORIES);

        return ResponseEntity.ok(sageAggregateResponse);
    }

    @GetMapping("/sage/themes")
    public ResponseEntity<AggregateResponse> getThemes() {
        final AggregateResponse sageAggregateResponse = service.getAggregate(SageListType.THEMES);

        return ResponseEntity.ok(sageAggregateResponse);
    }

    @GetMapping("/sage/esg")
    public ResponseEntity<AggregateResponse> getEsg() {
        final AggregateResponse sageAggregateResponse = service.getAggregate(SageListType.ESG);

        return ResponseEntity.ok(sageAggregateResponse);
    }

}
