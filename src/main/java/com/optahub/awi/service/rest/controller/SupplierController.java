package com.optahub.awi.service.rest.controller;

import java.io.IOException;

import com.optahub.awi.service.rest.data.supplier.SupplierRequest;
import com.optahub.awi.service.rest.service.SupplierIndexService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
public class SupplierController {

    private final SupplierIndexService service;

    @Autowired
    public SupplierController(final SupplierIndexService service) {
        this.service = service;
    }

    @PostMapping("/supplier")
    ResponseEntity<String> addSupplier(@RequestBody final SupplierRequest request) {
            service.addSupplier(request);
            return ResponseEntity.ok("SUCCESS");
    }

    @PostMapping("/supplier/remove/{supplierCode}")
    ResponseEntity<String> removeSupplierProducts(@PathVariable("supplierCode") final String supplierCode) throws IOException {
        service.removeSupplier(supplierCode);
        return ResponseEntity.ok("Success");
    }
}
