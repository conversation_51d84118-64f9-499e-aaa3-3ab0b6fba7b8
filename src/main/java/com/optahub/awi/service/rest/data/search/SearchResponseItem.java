package com.optahub.awi.service.rest.data.search;

import com.fasterxml.jackson.annotation.JsonProperty;

public class SearchResponseItem {

    @JsonProperty("Count")
    private String count;

    @JsonProperty("ProductID")
    private String productId;

    @JsonProperty("SPC")
    private String spc;

    @JsonProperty("PrName")
    private String prName;

    @JsonProperty("Category")
    private String category;

    @JsonProperty("ItemNum")
    private String itemNum;

    @JsonProperty("Description")
    private String description;

    @JsonProperty("Colors")
    private String colors;

    @JsonProperty("Themes")
    private String themes;

    @JsonProperty("ProdTime")
    private String prodTime;

    @JsonProperty("SuppID")
    private String suppId;

    @JsonProperty("LineName")
    private String lineName;

    @JsonProperty("CompanyName")
    private String companyName;

    @JsonProperty("Prc")
    private String prc;

    @JsonProperty("Net")
    private String net;

    @JsonProperty("ThumbPicLink")
    private String thumbPicLink;

    public String getCount() {
        return count;
    }

    public void setCount(String count) {
        this.count = count;
    }

    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }

    public String getSpc() {
        return spc;
    }

    public void setSpc(String spc) {
        this.spc = spc;
    }

    public String getPrName() {
        return prName;
    }

    public void setPrName(String prName) {
        this.prName = prName;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getItemNum() {
        return itemNum;
    }

    public void setItemNum(String itemNum) {
        this.itemNum = itemNum;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getColors() {
        return colors;
    }

    public void setColors(String colors) {
        this.colors = colors;
    }

    public String getThemes() {
        return themes;
    }

    public void setThemes(String themes) {
        this.themes = themes;
    }

    public String getProdTime() {
        return prodTime;
    }

    public void setProdTime(String prodTime) {
        this.prodTime = prodTime;
    }

    public String getSuppId() {
        return suppId;
    }

    public void setSuppId(String suppId) {
        this.suppId = suppId;
    }

    public String getLineName() {
        return lineName;
    }

    public void setLineName(String lineName) {
        this.lineName = lineName;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getPrc() {
        return prc;
    }

    public void setPrc(String prc) {
        this.prc = prc;
    }

    public String getNet() {
        return net;
    }

    public void setNet(String net) {
        this.net = net;
    }

    public String getThumbPicLink() {
        return thumbPicLink;
    }

    public void setThumbPicLink(String thumbPicLink) {
        this.thumbPicLink = thumbPicLink;
    }
}
