package com.optahub.awi.service.rest.data.search;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

public class SearchResponseData {

    @JsonProperty("Success")
    private String success;

    @JsonProperty("ErrMsg")
    private List<String> errMsg;

    @JsonProperty("TotalFound")
    private String totalFound;

    @JsonProperty("Items")
    private SearchResponseItemContainer items;

    public String getSuccess() {
        return success;
    }

    public void setSuccess(String success) {
        this.success = success;
    }

    public List<String> getErrMsg() {
        return errMsg;
    }

    public void setErrMsg(List<String> errMsg) {
        this.errMsg = errMsg;
    }

    public String getTotalFound() {
        return totalFound;
    }

    public void setTotalFound(String totalFound) {
        this.totalFound = totalFound;
    }

    public SearchResponseItemContainer getItems() {
        return items;
    }

    public void setItems(SearchResponseItemContainer items) {
        this.items = items;
    }
}
