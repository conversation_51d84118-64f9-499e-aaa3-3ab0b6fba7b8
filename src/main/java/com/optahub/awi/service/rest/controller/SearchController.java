package com.optahub.awi.service.rest.controller;

import com.optahub.awi.service.rest.data.search.SearchRequest;
import com.optahub.awi.service.rest.data.search.SearchResponse;
import com.optahub.awi.service.rest.service.SearchService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
public class SearchController {

    private final SearchService service;

    @Autowired
    public SearchController(final SearchService service) {
        this.service = service;
    }

    @PostMapping("/search")
    ResponseEntity<SearchResponse> search(@RequestBody final SearchRequest searchRequest) {
        final SearchResponse response = service.doSearch(searchRequest);
        return ResponseEntity.ok(response);
    }
}
