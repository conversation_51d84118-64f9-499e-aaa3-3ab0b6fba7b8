package com.optahub.awi.service.rest.data.sage.search;

import com.fasterxml.jackson.annotation.JsonProperty;

public class SageSearchRequest {

    @JsonProperty("Category")
    private String category;

    @JsonProperty("contact_shipping_details")
    private String contactShippingDetails;

    @JsonProperty("Keywords")
    private String keywords;

    @JsonProperty("Colors")
    private String colors;

    @JsonProperty("Themes")
    private String themes;

    @JsonProperty("ItemName")
    private String itemName;

    @JsonProperty("SPC")
    private String spc;

    @JsonProperty("PriceLow")
    private String priceLow;

    @JsonProperty("PriceHigh")
    private String priceHigh;

    @JsonProperty("Qty")
    private String quantity;

    @JsonProperty("MinPriceCode")
    private String minPriceCode;

    @JsonProperty("Dimension1Units")
    private String dimension1Units;

    @JsonProperty("Dimension1Type")
    private String dimension1Type;

    @JsonProperty("Dimension2Units")
    private String dimension2Units;

    @JsonProperty("Dimension2Type")
    private String dimension2Type;

    @JsonProperty("Dimension3Units")
    private String dimension3Units;

    @JsonProperty("Dimension3Type")
    private String dimension3Type;

    @JsonProperty("DimensionApprox")
    private String dimensionApprox;

    @JsonProperty("ImprintArea1Units")
    private String imprintArea1Units;

    @JsonProperty("ImprintArea1Type")
    private String imprintArea1Type;

    @JsonProperty("ImprintArea2Type")
    private String imprintArea2Type;

    @JsonProperty("ImprintArea2Units")
    private String imprintArea2Units;

    @JsonProperty("ImprintAreaApprox")
    private String imprintAreaApprox;

    @JsonProperty("ImprintAreaMin")
    private String imprintAreaMin;

    @JsonProperty("LineName")
    private String lineName;

    @JsonProperty("PrefGroups")
    private String prefGroups;

    private String create;

    @JsonProperty("StartNum")
    private String startNum;

    @JsonProperty("MaxRecs")
    private String maxRecs;

    private String quoteId;

    @JsonProperty("ExtraReturnFields")
    private String extraReturnFields;

    @JsonProperty("MaxTotalItems")
    private Integer maxTotalItems;

    @JsonProperty("Sort")
    private String sort;

    @JsonProperty("ItemNumExact")
    private String itemNumExact;

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getContactShippingDetails() {
        return contactShippingDetails;
    }

    public void setContactShippingDetails(String contactShippingDetails) {
        this.contactShippingDetails = contactShippingDetails;
    }

    public String getKeywords() {
        return keywords;
    }

    public void setKeywords(String keywords) {
        this.keywords = keywords;
    }

    public String getColors() {
        return colors;
    }

    public void setColors(String colors) {
        this.colors = colors;
    }

    public String getThemes() {
        return themes;
    }

    public void setThemes(String themes) {
        this.themes = themes;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getSpc() {
        return spc;
    }

    public void setSpc(String spc) {
        this.spc = spc;
    }

    public String getPriceLow() {
        return priceLow;
    }

    public void setPriceLow(String priceLow) {
        this.priceLow = priceLow;
    }

    public String getPriceHigh() {
        return priceHigh;
    }

    public void setPriceHigh(String priceHigh) {
        this.priceHigh = priceHigh;
    }

    public String getQuantity() {
        return quantity;
    }

    public void setQuantity(String quantity) {
        this.quantity = quantity;
    }

    public String getMinPriceCode() {
        return minPriceCode;
    }

    public void setMinPriceCode(String minPriceCode) {
        this.minPriceCode = minPriceCode;
    }

    public String getDimension1Units() {
        return dimension1Units;
    }

    public void setDimension1Units(String dimension1Units) {
        this.dimension1Units = dimension1Units;
    }

    public String getDimension1Type() {
        return dimension1Type;
    }

    public void setDimension1Type(String dimension1Type) {
        this.dimension1Type = dimension1Type;
    }

    public String getDimension2Units() {
        return dimension2Units;
    }

    public void setDimension2Units(String dimension2Units) {
        this.dimension2Units = dimension2Units;
    }

    public String getDimension2Type() {
        return dimension2Type;
    }

    public void setDimension2Type(String dimension2Type) {
        this.dimension2Type = dimension2Type;
    }

    public String getDimension3Units() {
        return dimension3Units;
    }

    public void setDimension3Units(String dimension3Units) {
        this.dimension3Units = dimension3Units;
    }

    public String getDimension3Type() {
        return dimension3Type;
    }

    public void setDimension3Type(String dimension3Type) {
        this.dimension3Type = dimension3Type;
    }

    public String getDimensionApprox() {
        return dimensionApprox;
    }

    public void setDimensionApprox(String dimensionApprox) {
        this.dimensionApprox = dimensionApprox;
    }

    public String getImprintArea1Units() {
        return imprintArea1Units;
    }

    public void setImprintArea1Units(String imprintArea1Units) {
        this.imprintArea1Units = imprintArea1Units;
    }

    public String getImprintArea1Type() {
        return imprintArea1Type;
    }

    public void setImprintArea1Type(String imprintArea1Type) {
        this.imprintArea1Type = imprintArea1Type;
    }

    public String getImprintArea2Type() {
        return imprintArea2Type;
    }

    public void setImprintArea2Type(String imprintArea2Type) {
        this.imprintArea2Type = imprintArea2Type;
    }

    public String getImprintArea2Units() {
        return imprintArea2Units;
    }

    public void setImprintArea2Units(String imprintArea2Units) {
        this.imprintArea2Units = imprintArea2Units;
    }

    public String getImprintAreaApprox() {
        return imprintAreaApprox;
    }

    public void setImprintAreaApprox(String imprintAreaApprox) {
        this.imprintAreaApprox = imprintAreaApprox;
    }

    public String getImprintAreaMin() {
        return imprintAreaMin;
    }

    public void setImprintAreaMin(String imprintAreaMin) {
        this.imprintAreaMin = imprintAreaMin;
    }

    public String getLineName() {
        return lineName;
    }

    public void setLineName(String lineName) {
        this.lineName = lineName;
    }

    public String getPrefGroups() {
        return prefGroups;
    }

    public void setPrefGroups(String prefGroups) {
        this.prefGroups = prefGroups;
    }

    public String getCreate() {
        return create;
    }

    public void setCreate(String create) {
        this.create = create;
    }

    public String getStartNum() {
        return startNum;
    }

    public void setStartNum(String startNum) {
        this.startNum = startNum;
    }

    public String getMaxRecs() {
        return maxRecs;
    }

    public void setMaxRecs(String maxRecs) {
        this.maxRecs = maxRecs;
    }

    public String getQuoteId() {
        return quoteId;
    }

    public void setQuoteId(String quoteId) {
        this.quoteId = quoteId;
    }

    public String getExtraReturnFields() {
        return extraReturnFields;
    }

    public void setExtraReturnFields(String extraReturnFields) {
        this.extraReturnFields = extraReturnFields;
    }

    public Integer getMaxTotalItems() {
        return maxTotalItems;
    }

    public void setMaxTotalItems(Integer maxTotalItems) {
        this.maxTotalItems = maxTotalItems;
    }

    public String getSort() {
        return sort;
    }

    public void setSort(String sort) {
        this.sort = sort;
    }

    public String getItemNumExact() {
        return itemNumExact;
    }

    public void setItemNumExact(String itemNumExact) {
        this.itemNumExact = itemNumExact;
    }
}
