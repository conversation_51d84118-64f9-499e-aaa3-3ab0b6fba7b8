package com.optahub.awi.service.rest.data.detail;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.optahub.awi.service.promostandards.data.media.ImageReference;
import com.optahub.awi.service.promostandards.data.price.Location;
import org.yaml.snakeyaml.error.Mark;

import java.util.ArrayList;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class ProductDetail {

    @JsonProperty("ProductID")
    private String productId;

    @JsonProperty("Category")
    private String category;

    @JsonProperty("SuppID")
    private String suppId;

    @JsonProperty("LineName")
    private String lineName;

    @JsonProperty("CatPage")
    private List<String> catPage = new ArrayList();

    @JsonProperty("CatYear")
    private String catYear;

    @JsonProperty("ItemNum")
    private String itemNum;

    @JsonProperty("SPC")
    private String spc;

    @JsonProperty("PrName")
    private String prName;

    @JsonProperty("Description")
    private String description;

    @JsonProperty("DescriptionList")
    private List<String> descriptionList;

    @JsonProperty("ApparelDescription")
    private String ApparelDescription;

    @JsonProperty("Dimensions")
    private String dimensions;

    @JsonProperty("Keywords")
    private String keywords;

    @JsonProperty("Colors")
    private String colors;

    @JsonProperty("Themes")
    private String themes;

    @JsonProperty("Qty1")
    private String qty1;

    @JsonProperty("Qty2")
    private String qty2;

    @JsonProperty("Qty3")
    private String qty3;

    @JsonProperty("Qty4")
    private String qty4;

    @JsonProperty("Qty5")
    private String qty5;

    @JsonProperty("Qty6")
    private String qty6;

    @JsonProperty("Qty7")
    private String qty7;

    @JsonProperty("Qty8")
    private String qty8;

    @JsonProperty("Qty9")
    private String qty9;

    @JsonProperty("Prc1")
    private String prc1;

    @JsonProperty("Prc2")
    private String prc2;

    @JsonProperty("Prc3")
    private String prc3;

    @JsonProperty("Prc4")
    private String prc4;

    @JsonProperty("Prc5")
    @JsonAlias("prc5")
    private String prc5;

    @JsonProperty("Prc7")
    private String prc7;

    @JsonProperty("Prc8")
    private String prc8;

    @JsonProperty("Prc9")
    private String prc9;


    @JsonProperty("Prc6")
    private List <String> prc6 = new ArrayList ();

    @JsonProperty("PrCode")
    private String prCode;

    @JsonProperty("CatPrc1")
    private String catPrc1;

    @JsonProperty("CatPrc2")
    private String catPrc2;

    @JsonProperty("CatPrc3")
    private String catPrc3;

    @JsonProperty("CatPrc4")
    private String catPrc4;

    @JsonProperty("CatPrc5")
    private String catPrc5;

    @JsonProperty("CatPrc7")
    private String catPrc7;

    @JsonProperty("CatPrc8")
    private String catPrc8;

    @JsonProperty("CatPrc9")
    private String catPrc9;

    @JsonProperty("CatPrc6")
    private List <String> catPrc6 = new ArrayList();

    @JsonProperty("CatPrCode")
    private String catPrCode;

    @JsonProperty("Net1")
    private String net1;

    @JsonProperty("Net2")
    private String net2;

    @JsonProperty("Net3")
    private String net3;

    @JsonProperty("Net4")
    private String net4;
    @JsonProperty("Net5")
    private String net5;

    @JsonProperty("Net7")
    private String net7;

    @JsonProperty("Net8")
    private String net8;

    @JsonProperty("Net9")
    private String net9;

    @JsonProperty("Net6")
    private List<String> net6 = new ArrayList();

    @JsonProperty("PriceAdjustMsg")
    private List<String> priceAdjustMsg = new ArrayList ();

    @JsonProperty("Currency")
    private String currency;

    @JsonProperty("PiecesPerUnit1")
    private String piecesPerUnit1;

    @JsonProperty("PiecesPerUnit2")
    private String piecesPerUnit2;

    @JsonProperty("PiecesPerUnit3")
    private String piecesPerUnit3;

    @JsonProperty("PiecesPerUnit4")
    private String piecesPerUnit4;

    @JsonProperty("PiecesPerUnit5")
    private String piecesPerUnit5;

    @JsonProperty("PiecesPerUnit6")
    private String piecesPerUnit6;

    @JsonProperty("OptionsObject")
    private Object optionsObject;

    @JsonProperty("MadeUSA")
    private String madeUSA;

    @JsonProperty("MadeInCountry")
    private String madeInCountry;

    @JsonProperty("AssembledInCountry")
    private String assembledInCountry;

    @JsonProperty("DecoratedInCountry")
    private String decoratedInCountry;

    @JsonProperty("Recycle")
    private String recycle;

    @JsonProperty("Recyclable")
    private String recyclable;

    @JsonProperty("NewProduct")
    private String newProduct;

    @JsonProperty("EnvFriendly")
    private String envFriendly;

    @JsonProperty("Food")
    private String food;

    @JsonProperty("Clothing")
    private String clothing;

    @JsonProperty("ProductCompliance")
    private String productCompliance;

    @JsonProperty("WarningLbl")
    private String warningLbl;

    @JsonProperty("ProductComplianceMemo")
    private String productComplianceMemo;

    @JsonProperty("Verified")
    private String verified;

    @JsonProperty("ImprintArea")
    private String imprintArea;

    @JsonProperty("SecondImprintArea")
    private String secondImprintArea;

    @JsonProperty("DecorationMethod")
    private String decorationMethod;

    @JsonProperty("DecorationNotOffered")
    private String decorationNotOffered;

    @JsonProperty("SetupChg")
    private String setupChg;

    @JsonProperty("SetupChgCode")
    private String setupChgCode;

    @JsonProperty("RepeatSetupChg")
    private String repeatSetupChg;

    @JsonProperty("RepeatSetupChgCode")
    private String repeatSetupChgCode;

    @JsonProperty("ScreenChg")
    private String screenChg;

    @JsonProperty("ScreenChgCode")
    private String screenChgCode;

    @JsonProperty("PlateChg")
    private String plateChg;

    @JsonProperty("PlateChgCode")
    ArrayList <String> plateChgCode = new ArrayList();

    @JsonProperty("DieChg")
    private String dieChg;

    @JsonProperty("DieChgCode")
    ArrayList <String> dieChgCode = new ArrayList();

    @JsonProperty("toolingChg")
    private String ToolingChg;

    @JsonProperty("ToolingChgCode")
    private List <String> toolingChgCode = new ArrayList();


    @JsonProperty("AddClrChg")
    private String addClrChg;


    @JsonProperty("AddClrChgCode")
    private String addClrChgCode;


    @JsonProperty("AddClrRunChg1")
    private String addClrRunChg1;


    @JsonProperty("AddClrRunChg2")
    private String addClrRunChg2;


    @JsonProperty("AddClrRunChg3")
    private String addClrRunChg3;


    @JsonProperty("AddClrRunChg4")
    private String addClrRunChg4;


    @JsonProperty("AddClrRunChg5")
    private String addClrRunChg5;


    @JsonProperty("AddClrRunChg6")
    private String addClrRunChg6;


    @JsonProperty("AddClrRunChgCode")
    private String addClrRunChgCode;


    @JsonProperty("PriceIncludes")
    private String priceIncludes;


    @JsonProperty("Package")
    private String packageStr;


    @JsonProperty("WeightPerCarton")
    private String weightPerCarton;


    @JsonProperty("UnitsPerCarton")
    private String unitsPerCarton;


    @JsonProperty("CartonL")
    private String cartonL;


    @JsonProperty("CartonW")
    private String cartonW;


    @JsonProperty("CartonH")
    private String cartonH;


    @JsonProperty("ShipPointCountry")
    private String shipPointCountry;


    @JsonProperty("ShipPointZip")
    private String shipPointZip;


    @JsonProperty("ProdTime")
    private String prodTime;


    @JsonProperty("Comment")
    private String comment;


    @JsonProperty("PicLink")
    private String picLink;

    @JsonProperty("PicName")
    private String picName;

    @JsonProperty("SpecialAvailable")
    private String specialAvailable;

    @JsonProperty("ExpDate")
    private String expDate;

    @JsonProperty("DefaultPart")
    private String defaultPart;

    @JsonProperty("PartList")
    private List<PartDetail> partList;

    @JsonProperty("LocationList")
    private List<Location> locationList;

    @JsonProperty("SupplierName")
    private String supplierName;

    @JsonProperty("Images")
    private List<ImageReference> images;

    @JsonProperty("SupplierDetail")
    private SupplierDetail supplierDetail;

    @JsonProperty("ApparelStyle")
    private String apparelStyle;

    @JsonProperty("Apparel")
    private boolean apparel;

    @JsonProperty("ApparelSizes")
    private String apparelSizes;
    @JsonProperty("MarkupDetail")
    private MarkupDetail markupDetail;

    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getSuppId() {
        return suppId;
    }

    public void setSuppId(String suppId) {
        this.suppId = suppId;
    }

    public String getLineName() {
        return lineName;
    }

    public void setLineName(String lineName) {
        this.lineName = lineName;
    }

    public List<String> getCatPage() {
        return catPage;
    }

    public void setCatPage(List<String> catPage) {
        this.catPage = catPage;
    }

    public String getCatYear() {
        return catYear;
    }

    public void setCatYear(String catYear) {
        this.catYear = catYear;
    }

    public String getItemNum() {
        return itemNum;
    }

    public void setItemNum(String itemNum) {
        this.itemNum = itemNum;
    }

    public String getSpc() {
        return spc;
    }

    public void setSpc(String spc) {
        this.spc = spc;
    }

    public String getPrName() {
        return prName;
    }

    public void setPrName(String prName) {
        this.prName = prName;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDimensions() {
        return dimensions;
    }

    public void setDimensions(String dimensions) {
        this.dimensions = dimensions;
    }

    public String getKeywords() {
        return keywords;
    }

    public void setKeywords(String keywords) {
        this.keywords = keywords;
    }

    public String getColors() {
        return colors;
    }

    public void setColors(String colors) {
        this.colors = colors;
    }

    public String getThemes() {
        return themes;
    }

    public void setThemes(String themes) {
        this.themes = themes;
    }

    public String getQty1() {
        return qty1;
    }

    public void setQty1(String qty1) {
        this.qty1 = qty1;
    }

    public String getQty2() {
        return qty2;
    }

    public void setQty2(String qty2) {
        this.qty2 = qty2;
    }

    public String getQty3() {
        return qty3;
    }

    public void setQty3(String qty3) {
        this.qty3 = qty3;
    }

    public String getQty4() {
        return qty4;
    }

    public void setQty4(String qty4) {
        this.qty4 = qty4;
    }

    public String getQty5() {
        return qty5;
    }

    public void setQty5(String qty5) {
        this.qty5 = qty5;
    }

    public String getQty6() {
        return qty6;
    }

    public void setQty6(String qty6) {
        this.qty6 = qty6;
    }

    public String getPrc1() {
        return prc1;
    }

    public void setPrc1(String prc1) {
        this.prc1 = prc1;
    }

    public String getPrc2() {
        return prc2;
    }

    public void setPrc2(String prc2) {
        this.prc2 = prc2;
    }

    public String getPrc3() {
        return prc3;
    }

    public void setPrc3(String prc3) {
        this.prc3 = prc3;
    }

    public String getPrc4() {
        return prc4;
    }

    public void setPrc4(String prc4) {
        this.prc4 = prc4;
    }

    public String getPrc5() {
        return prc5;
    }

    public void setPrc5(String prc5) {
        this.prc5 = prc5;
    }

    public List<String> getPrc6() {
        return prc6;
    }

    public void setPrc6(List<String> prc6) {
        this.prc6 = prc6;
    }

    public String getPrCode() {
        return prCode;
    }

    public void setPrCode(String prCode) {
        this.prCode = prCode;
    }

    public String getCatPrc1() {
        return catPrc1;
    }

    public void setCatPrc1(String catPrc1) {
        this.catPrc1 = catPrc1;
    }

    public String getCatPrc2() {
        return catPrc2;
    }

    public void setCatPrc2(String catPrc2) {
        this.catPrc2 = catPrc2;
    }

    public String getCatPrc3() {
        return catPrc3;
    }

    public void setCatPrc3(String catPrc3) {
        this.catPrc3 = catPrc3;
    }

    public String getCatPrc4() {
        return catPrc4;
    }

    public void setCatPrc4(String catPrc4) {
        this.catPrc4 = catPrc4;
    }

    public String getCatPrc5() {
        return catPrc5;
    }

    public void setCatPrc5(String catPrc5) {
        this.catPrc5 = catPrc5;
    }

    public List<String> getCatPrc6() {
        return catPrc6;
    }

    public void setCatPrc6(List<String> catPrc6) {
        this.catPrc6 = catPrc6;
    }

    public String getCatPrCode() {
        return catPrCode;
    }

    public void setCatPrCode(String catPrCode) {
        this.catPrCode = catPrCode;
    }

    public String getNet1() {
        return net1;
    }

    public void setNet1(String net1) {
        this.net1 = net1;
    }

    public String getNet2() {
        return net2;
    }

    public void setNet2(String net2) {
        this.net2 = net2;
    }

    public String getNet3() {
        return net3;
    }

    public void setNet3(String net3) {
        this.net3 = net3;
    }

    public String getNet4() {
        return net4;
    }

    public void setNet4(String net4) {
        this.net4 = net4;
    }

    public String getNet5() {
        return net5;
    }

    public void setNet5(String net5) {
        this.net5 = net5;
    }

    public List<String> getNet6() {
        return net6;
    }

    public void setNet6(List<String> net6) {
        this.net6 = net6;
    }

    public List<String> getPriceAdjustMsg() {
        return priceAdjustMsg;
    }

    public void setPriceAdjustMsg(List<String> priceAdjustMsg) {
        this.priceAdjustMsg = priceAdjustMsg;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getPiecesPerUnit1() {
        return piecesPerUnit1;
    }

    public void setPiecesPerUnit1(String piecesPerUnit1) {
        this.piecesPerUnit1 = piecesPerUnit1;
    }

    public String getPiecesPerUnit2() {
        return piecesPerUnit2;
    }

    public void setPiecesPerUnit2(String piecesPerUnit2) {
        this.piecesPerUnit2 = piecesPerUnit2;
    }

    public String getPiecesPerUnit3() {
        return piecesPerUnit3;
    }

    public void setPiecesPerUnit3(String piecesPerUnit3) {
        this.piecesPerUnit3 = piecesPerUnit3;
    }

    public String getPiecesPerUnit4() {
        return piecesPerUnit4;
    }

    public void setPiecesPerUnit4(String piecesPerUnit4) {
        this.piecesPerUnit4 = piecesPerUnit4;
    }

    public String getPiecesPerUnit5() {
        return piecesPerUnit5;
    }

    public void setPiecesPerUnit5(String piecesPerUnit5) {
        this.piecesPerUnit5 = piecesPerUnit5;
    }

    public String getPiecesPerUnit6() {
        return piecesPerUnit6;
    }

    public void setPiecesPerUnit6(String piecesPerUnit6) {
        this.piecesPerUnit6 = piecesPerUnit6;
    }

    public Object getOptionsObject() {
        return optionsObject;
    }

    public void setOptionsObject(Object optionsObject) {
        this.optionsObject = optionsObject;
    }

    public String getMadeUSA() {
        return madeUSA;
    }

    public void setMadeUSA(String madeUSA) {
        this.madeUSA = madeUSA;
    }

    public String getMadeInCountry() {
        return madeInCountry;
    }

    public void setMadeInCountry(String madeInCountry) {
        this.madeInCountry = madeInCountry;
    }

    public String getAssembledInCountry() {
        return assembledInCountry;
    }

    public void setAssembledInCountry(String assembledInCountry) {
        this.assembledInCountry = assembledInCountry;
    }

    public String getDecoratedInCountry() {
        return decoratedInCountry;
    }

    public void setDecoratedInCountry(String decoratedInCountry) {
        this.decoratedInCountry = decoratedInCountry;
    }

    public String getRecycle() {
        return recycle;
    }

    public void setRecycle(String recycle) {
        this.recycle = recycle;
    }

    public String getRecyclable() {
        return recyclable;
    }

    public void setRecyclable(String recyclable) {
        this.recyclable = recyclable;
    }

    public String getNewProduct() {
        return newProduct;
    }

    public void setNewProduct(String newProduct) {
        this.newProduct = newProduct;
    }

    public String getEnvFriendly() {
        return envFriendly;
    }

    public void setEnvFriendly(String envFriendly) {
        this.envFriendly = envFriendly;
    }

    public String getFood() {
        return food;
    }

    public void setFood(String food) {
        this.food = food;
    }

    public String getClothing() {
        return clothing;
    }

    public void setClothing(String clothing) {
        this.clothing = clothing;
    }

    public String getProductCompliance() {
        return productCompliance;
    }

    public void setProductCompliance(String productCompliance) {
        this.productCompliance = productCompliance;
    }

    public String getWarningLbl() {
        return warningLbl;
    }

    public void setWarningLbl(String warningLbl) {
        this.warningLbl = warningLbl;
    }

    public String getProductComplianceMemo() {
        return productComplianceMemo;
    }

    public void setProductComplianceMemo(String productComplianceMemo) {
        this.productComplianceMemo = productComplianceMemo;
    }

    public String getVerified() {
        return verified;
    }

    public void setVerified(String verified) {
        this.verified = verified;
    }

    public String getImprintArea() {
        return imprintArea;
    }

    public void setImprintArea(String imprintArea) {
        this.imprintArea = imprintArea;
    }

    public String getSecondImprintArea() {
        return secondImprintArea;
    }

    public void setSecondImprintArea(String secondImprintArea) {
        this.secondImprintArea = secondImprintArea;
    }

    public String getDecorationMethod() {
        return decorationMethod;
    }

    public void setDecorationMethod(String decorationMethod) {
        this.decorationMethod = decorationMethod;
    }

    public String getDecorationNotOffered() {
        return decorationNotOffered;
    }

    public void setDecorationNotOffered(String decorationNotOffered) {
        this.decorationNotOffered = decorationNotOffered;
    }

    public String getSetupChg() {
        return setupChg;
    }

    public void setSetupChg(String setupChg) {
        this.setupChg = setupChg;
    }

    public String getSetupChgCode() {
        return setupChgCode;
    }

    public void setSetupChgCode(String setupChgCode) {
        this.setupChgCode = setupChgCode;
    }

    public String getRepeatSetupChg() {
        return repeatSetupChg;
    }

    public void setRepeatSetupChg(String repeatSetupChg) {
        this.repeatSetupChg = repeatSetupChg;
    }

    public String getRepeatSetupChgCode() {
        return repeatSetupChgCode;
    }

    public void setRepeatSetupChgCode(String repeatSetupChgCode) {
        this.repeatSetupChgCode = repeatSetupChgCode;
    }

    public String getScreenChg() {
        return screenChg;
    }

    public void setScreenChg(String screenChg) {
        this.screenChg = screenChg;
    }

    public String getScreenChgCode() {
        return screenChgCode;
    }

    public void setScreenChgCode(String screenChgCode) {
        this.screenChgCode = screenChgCode;
    }

    public String getPlateChg() {
        return plateChg;
    }

    public void setPlateChg(String plateChg) {
        this.plateChg = plateChg;
    }

    public ArrayList<String> getPlateChgCode() {
        return plateChgCode;
    }

    public void setPlateChgCode(ArrayList<String> plateChgCode) {
        plateChgCode = plateChgCode;
    }

    public String getDieChg() {
        return dieChg;
    }

    public void setDieChg(String dieChg) {
        this.dieChg = dieChg;
    }

    public ArrayList<String> getDieChgCode() {
        return dieChgCode;
    }

    public void setDieChgCode(ArrayList<String> dieChgCode) {
        dieChgCode = dieChgCode;
    }

    public String getToolingChg() {
        return ToolingChg;
    }

    public void setToolingChg(String toolingChg) {
        ToolingChg = toolingChg;
    }

    public List<String> getToolingChgCode() {
        return toolingChgCode;
    }

    public void setToolingChgCode(List<String> toolingChgCode) {
        toolingChgCode = toolingChgCode;
    }

    public String getAddClrChg() {
        return addClrChg;
    }

    public void setAddClrChg(String addClrChg) {
        this.addClrChg = addClrChg;
    }

    public String getAddClrChgCode() {
        return addClrChgCode;
    }

    public void setAddClrChgCode(String addClrChgCode) {
        this.addClrChgCode = addClrChgCode;
    }

    public String getAddClrRunChg1() {
        return addClrRunChg1;
    }

    public void setAddClrRunChg1(String addClrRunChg1) {
        this.addClrRunChg1 = addClrRunChg1;
    }

    public String getAddClrRunChg2() {
        return addClrRunChg2;
    }

    public void setAddClrRunChg2(String addClrRunChg2) {
        this.addClrRunChg2 = addClrRunChg2;
    }

    public String getAddClrRunChg3() {
        return addClrRunChg3;
    }

    public void setAddClrRunChg3(String addClrRunChg3) {
        this.addClrRunChg3 = addClrRunChg3;
    }

    public String getAddClrRunChg4() {
        return addClrRunChg4;
    }

    public void setAddClrRunChg4(String addClrRunChg4) {
        this.addClrRunChg4 = addClrRunChg4;
    }

    public String getAddClrRunChg5() {
        return addClrRunChg5;
    }

    public void setAddClrRunChg5(String addClrRunChg5) {
        this.addClrRunChg5 = addClrRunChg5;
    }

    public String getAddClrRunChg6() {
        return addClrRunChg6;
    }

    public void setAddClrRunChg6(String addClrRunChg6) {
        this.addClrRunChg6 = addClrRunChg6;
    }

    public String getAddClrRunChgCode() {
        return addClrRunChgCode;
    }

    public void setAddClrRunChgCode(String addClrRunChgCode) {
        this.addClrRunChgCode = addClrRunChgCode;
    }

    public String getPriceIncludes() {
        return priceIncludes;
    }

    public void setPriceIncludes(String priceIncludes) {
        this.priceIncludes = priceIncludes;
    }

    public String getPackageStr() {
        return packageStr;
    }

    public void setPackageStr(String packageStr) {
        this.packageStr = packageStr;
    }

    public String getWeightPerCarton() {
        return weightPerCarton;
    }

    public void setWeightPerCarton(String weightPerCarton) {
        this.weightPerCarton = weightPerCarton;
    }

    public String getUnitsPerCarton() {
        return unitsPerCarton;
    }

    public void setUnitsPerCarton(String unitsPerCarton) {
        this.unitsPerCarton = unitsPerCarton;
    }

    public String getCartonL() {
        return cartonL;
    }

    public void setCartonL(String cartonL) {
        this.cartonL = cartonL;
    }

    public String getCartonW() {
        return cartonW;
    }

    public void setCartonW(String cartonW) {
        this.cartonW = cartonW;
    }

    public String getCartonH() {
        return cartonH;
    }

    public void setCartonH(String cartonH) {
        this.cartonH = cartonH;
    }

    public String getShipPointCountry() {
        return shipPointCountry;
    }

    public void setShipPointCountry(String shipPointCountry) {
        this.shipPointCountry = shipPointCountry;
    }

    public String getShipPointZip() {
        return shipPointZip;
    }

    public void setShipPointZip(String shipPointZip) {
        this.shipPointZip = shipPointZip;
    }

    public String getProdTime() {
        return prodTime;
    }

    public void setProdTime(String prodTime) {
        this.prodTime = prodTime;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public String getPicLink() {
        return picLink;
    }

    public void setPicLink(String picLink) {
        this.picLink = picLink;
    }

    public String getSpecialAvailable() {
        return specialAvailable;
    }

    public void setSpecialAvailable(String specialAvailable) {
        this.specialAvailable = specialAvailable;
    }

    public String getExpDate() {
        return expDate;
    }

    public void setExpDate(String expDate) {
        this.expDate = expDate;
    }

    public String getDefaultPart() {
        return defaultPart;
    }

    public void setDefaultPart(String defaultPart) {
        this.defaultPart = defaultPart;
    }

    public List<PartDetail> getPartList() {
        return partList;
    }

    public void setPartList(List<PartDetail> partList) {
        this.partList = partList;
    }

    public List<Location> getLocationList() {
        return locationList;
    }

    public void setLocationList(List<Location> locationList) {
        this.locationList = locationList;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public List<ImageReference> getImages() {
        return images;
    }

    public void setImages(List<ImageReference> images) {
        this.images = images;
    }

    public String getPicName() {
        return picName;
    }

    public void setPicName(String picName) {
        this.picName = picName;
    }

    public SupplierDetail getSupplierDetail() {
        return supplierDetail;
    }

    public void setSupplierDetail(SupplierDetail supplierDetail) {
        this.supplierDetail = supplierDetail;
    }

    public String getQty7() {
        return qty7;
    }

    public void setQty7(String qty7) {
        this.qty7 = qty7;
    }

    public String getQty8() {
        return qty8;
    }

    public void setQty8(String qty8) {
        this.qty8 = qty8;
    }

    public String getQty9() {
        return qty9;
    }

    public void setQty9(String qty9) {
        this.qty9 = qty9;
    }

    public String getPrc7() {
        return prc7;
    }

    public void setPrc7(String prc7) {
        this.prc7 = prc7;
    }

    public String getPrc8() {
        return prc8;
    }

    public void setPrc8(String prc8) {
        this.prc8 = prc8;
    }

    public String getPrc9() {
        return prc9;
    }

    public void setPrc9(String prc9) {
        this.prc9 = prc9;
    }

    public String getCatPrc7() {
        return catPrc7;
    }

    public void setCatPrc7(String catPrc7) {
        this.catPrc7 = catPrc7;
    }

    public String getCatPrc8() {
        return catPrc8;
    }

    public void setCatPrc8(String catPrc8) {
        this.catPrc8 = catPrc8;
    }

    public String getCatPrc9() {
        return catPrc9;
    }

    public void setCatPrc9(String catPrc9) {
        this.catPrc9 = catPrc9;
    }

    public String getNet7() {
        return net7;
    }

    public void setNet7(String net7) {
        this.net7 = net7;
    }

    public String getNet8() {
        return net8;
    }

    public void setNet8(String net8) {
        this.net8 = net8;
    }

    public String getNet9() {
        return net9;
    }

    public void setNet9(String net9) {
        this.net9 = net9;
    }

    public List<String> getDescriptionList() {
        return descriptionList;
    }

    public void setDescriptionList(List<String> descriptionList) {
        this.descriptionList = descriptionList;
    }

    public String getApparelStyle() {
        return apparelStyle;
    }

    public void setApparelStyle(String apparelStyle) {
        this.apparelStyle = apparelStyle;
    }

    public boolean isApparel() {
        return apparel;
    }

    public void setApparel(boolean apparel) {
        this.apparel = apparel;
    }

    public String getApparelSizes() {
        return apparelSizes;
    }

    public void setApparelSizes(String apparelSizes) {
        this.apparelSizes = apparelSizes;
    }

    public String getApparelDescription() {
        return ApparelDescription;
    }

    public void setApparelDescription(String apparelDescription) {
        ApparelDescription = apparelDescription;
    }

    public MarkupDetail getMarkupDetail() {
        return markupDetail;
    }

    public void setMarkupDetail(MarkupDetail markupDetail) {
        this.markupDetail = markupDetail;
    }
}
