package com.optahub.awi.service.rest.service;

import com.optahub.awi.service.elastic.data.supplier.SupplierData;
import com.optahub.awi.service.elastic.service.ElasticQueryBuilderService;
import com.optahub.awi.service.elastic.service.ElasticSearchObjectMapper;
import com.optahub.awi.service.elastic.service.ElasticSearchService;
import com.optahub.awi.service.promostandards.data.service.Company;
import com.optahub.awi.service.promostandards.factory.SupplierConfigFactory;
import com.optahub.awi.service.rest.data.supplier.SupplierRequest;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.*;

import static java.util.stream.Collectors.groupingBy;

@Service
public class SupplierIndexService {

    private final com.optahub.awi.service.promostandards.service.SupplierService supplierService;
    private final ElasticSearchService elasticSearchService;

    @Autowired
    public SupplierIndexService(final com.optahub.awi.service.promostandards.service.SupplierService supplierService,
                                final ElasticSearchService elasticSearchService) {

        this.supplierService = supplierService;
        this.elasticSearchService = elasticSearchService;
    }

    public void removeSupplier(final String supplierCode) throws IOException {
        elasticSearchService.removeSupplier(supplierCode);
    }

    public void addSupplier(final SupplierRequest supplierRequest) {
        final SupplierData config = new SupplierData();
        final Company company = supplierService.getCompanyDetails(supplierRequest.getCode());
        config.setKey(supplierRequest.getKey());
        config.setSecret(supplierRequest.getSecret());
        config.setCode(supplierRequest.getCode());
        config.setName(company.getName());
        config.setId(UUID.randomUUID().toString());
        config.setDisableInventory(supplierRequest.isDisableInventory());
        config.setDisablePricing(supplierRequest.isDisablePricing());
        config.setSupplierParent(StringUtils.isNotEmpty(supplierRequest.getParent()) ? supplierRequest.getParent() : company.getName());

        elasticSearchService.indexSupplier(Arrays.asList(config));
    }
}
