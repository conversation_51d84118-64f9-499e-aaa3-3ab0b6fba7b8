package com.optahub.awi.service.rest.controller;

import com.optahub.awi.service.promostandards.data.price.Location;
import com.optahub.awi.service.rest.data.detail.ProductDataResponseWrapper;
import com.optahub.awi.service.rest.data.detail.ProductDetail;
import com.optahub.awi.service.rest.data.detail.ProductLocationResponseWrapper;
import com.optahub.awi.service.rest.data.search.SearchResponse;
import com.optahub.awi.service.rest.service.DetailService;
import com.optahub.awi.service.rest.service.SearchService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

@RestController
public class DetailController {

    private final DetailService service;

    @Autowired
    public DetailController(final DetailService service) {
        this.service = service;
    }

    @GetMapping("/details")
    ResponseEntity<ProductDataResponseWrapper> search(@RequestHeader(value = "awi-franchise-id", required = false) String franchiseId,@RequestParam("productId") final String productId, @RequestParam("fresh") final Optional<Boolean> fresh) {
        final ProductDetail response = service.getProduct(franchiseId, productId, fresh.orElse(Boolean.FALSE));
        final ProductDataResponseWrapper wrapper = new ProductDataResponseWrapper();
        wrapper.setMessage("");
        wrapper.setData(response);
        return ResponseEntity.ok(wrapper);
    }

    @GetMapping("/details/locations/{supplierId}/{productId}")
    ResponseEntity<ProductLocationResponseWrapper> getLocations(@PathVariable("productId") final String productId,
                                                                @PathVariable("supplierId") final String supplierId) {
        final List<Location> response = service.getProductLocations(productId, supplierId);
        final ProductLocationResponseWrapper wrapper = new ProductLocationResponseWrapper();
        wrapper.setMessage("");
        wrapper.setData(response);
        return ResponseEntity.ok(wrapper);
    }
}
