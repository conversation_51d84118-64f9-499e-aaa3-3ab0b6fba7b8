package com.optahub.awi.service.rest.data.detail;

import com.optahub.awi.service.promostandards.data.price.Location;

import java.util.List;

public class ProductLocationResponseWrapper {
    private String message;
    private List<Location> data;

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public List<Location> getData() {
        return data;
    }

    public void setData(List<Location> data) {
        this.data = data;
    }
}
