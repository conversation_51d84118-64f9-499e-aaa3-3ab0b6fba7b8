package com.optahub.awi.service.rest.controller;

import com.optahub.awi.service.rest.exception.PromoStandardsException;
import com.optahub.awi.service.rest.service.IndexService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;

@RestController
public class IndexController {

    private final IndexService service;

    @Autowired
    public IndexController(final IndexService service) {
        this.service = service;
    }

    @PostMapping("/index/{supplierCode}")
    ResponseEntity<String> indexProducts(@PathVariable("supplierCode") final String supplierCode) {
            service.indexSupplierAsync(supplierCode);
            return ResponseEntity.ok("Initiated");
    }

    @PostMapping("/index/remove/{supplierCode}")
    ResponseEntity<String> removeSupplierProducts(@PathVariable("supplierCode") final String supplierCode) throws IOException {
        service.removeSupplierProducts(supplierCode);
        return ResponseEntity.ok("Success");
    }
}
