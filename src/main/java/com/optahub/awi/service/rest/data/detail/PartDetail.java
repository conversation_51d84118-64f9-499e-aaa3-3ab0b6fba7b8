package com.optahub.awi.service.rest.data.detail;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.optahub.awi.service.elastic.data.ProductPartDimension;
import com.optahub.awi.service.elastic.data.ProductPartShipping;
import com.optahub.awi.service.promostandards.data.inventory.LocationInventory;
import com.optahub.awi.service.promostandards.data.inventory.ProductPartInventory;
import com.optahub.awi.service.promostandards.data.media.ImageReference;
import com.optahub.awi.service.promostandards.data.price.PartPrice;

import java.util.ArrayList;
import java.util.List;

public class PartDetail {
    private String partId;
    private String partName;
    private String partDescription;

    private String swatchImage;

    private List<String> hexValue;

    private boolean optional;
    private List<PartPrice> netPrice;
    private List<PartPrice> listPrice;

    private List<PartPrice> listBlank;

    private List<PartPrice> netBlank;

    private List<PartPrice> custPrice;

    private List<PartPrice> custBlank;

    private List<LocationInventory> inventory;

    private List<ApparelInventory> apparelInventory;

    private ProductPartDimension dimension;

    private List<ProductPartShipping> shippingList;

    private List<ImageReference> partImages;

    private String apparelStyle;

    private ApparelPackaging apparelPackaging;

    private ApparelDimension apparelDimension;



    public String getPartId() {
        return partId;
    }

    public void setPartId(String partId) {
        this.partId = partId;
    }

    public String getPartName() {
        return partName;
    }

    public void setPartName(String partName) {
        this.partName = partName;
    }

    public String getPartDescription() {
        return partDescription;
    }

    public void setPartDescription(String partDescription) {
        this.partDescription = partDescription;
    }

    public List<PartPrice> getNetPrice() {
        return netPrice;
    }

    public void setNetPrice(List<PartPrice> netPrice) {
        this.netPrice = netPrice;
    }

    public List<PartPrice> getListPrice() {
        return listPrice;
    }

    public void setListPrice(List<PartPrice> listPrice) {
        this.listPrice = listPrice;
    }

    public List<LocationInventory> getInventory() {
        return inventory;
    }

    public void setInventory(List<LocationInventory> inventory) {
        this.inventory = inventory;
    }

    public ProductPartDimension getDimension() {
        return dimension;
    }

    public void setDimension(ProductPartDimension dimension) {
        this.dimension = dimension;
    }

    public List<ProductPartShipping> getShippingList() {
        return shippingList;
    }

    public void setShippingList(List<ProductPartShipping> shippingList) {
        this.shippingList = shippingList;
    }

    public boolean isOptional() {
        return optional;
    }

    public void setOptional(boolean optional) {
        this.optional = optional;
    }

    public List<ImageReference> getPartImages() {
        return partImages;
    }

    public void setPartImages(List<ImageReference> partImages) {
        this.partImages = partImages;
    }

    public List<PartPrice> getListBlank() {
        return listBlank;
    }

    public void setListBlank(List<PartPrice> listBlank) {
        this.listBlank = listBlank;
    }

    public List<PartPrice> getNetBlank() {
        return netBlank;
    }

    public void setNetBlank(List<PartPrice> netBlank) {
        this.netBlank = netBlank;
    }

    public List<ApparelInventory> getApparelInventory() {
        return apparelInventory;
    }

    public void setApparelInventory(List<ApparelInventory> apparelInventory) {
        this.apparelInventory = apparelInventory;
    }

    public List<PartPrice> getCustPrice() {
        return custPrice;
    }

    public void setCustPrice(List<PartPrice> custPrice) {
        this.custPrice = custPrice;
    }

    public List<PartPrice> getCustBlank() {
        return custBlank;
    }

    public void setCustBlank(List<PartPrice> custBlank) {
        this.custBlank = custBlank;
    }

    public String getApparelStyle() {
        return apparelStyle;
    }

    public void setApparelStyle(String apparelStyle) {
        this.apparelStyle = apparelStyle;
    }

    public ApparelPackaging getApparelPackaging() {
        return apparelPackaging;
    }

    public void setApparelPackaging(ApparelPackaging apparelPackaging) {
        this.apparelPackaging = apparelPackaging;
    }

    public ApparelDimension getApparelDimension() {
        return apparelDimension;
    }

    public void setApparelDimension(ApparelDimension apparelDimension) {
        this.apparelDimension = apparelDimension;
    }

    public String getSwatchImage() {
        return swatchImage;
    }

    public void setSwatchImage(String swatchImage) {
        this.swatchImage = swatchImage;
    }

    public List<String> getHexValue() {
        return hexValue;
    }

    public void setHexValue(List<String> hexValue) {
        this.hexValue = hexValue;
    }
}
