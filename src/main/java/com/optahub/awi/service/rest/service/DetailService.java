package com.optahub.awi.service.rest.service;

import com.optahub.awi.service.elastic.data.*;
import com.optahub.awi.service.elastic.data.constant.ApparelSizeType;
import com.optahub.awi.service.elastic.data.constant.ImagePrioritizationType;
import com.optahub.awi.service.elastic.service.ElasticSearchService;
import com.optahub.awi.service.promostandards.data.constant.PriceUOMType;
import com.optahub.awi.service.promostandards.data.inventory.ProductInventory;
import com.optahub.awi.service.promostandards.data.inventory.ProductPartInventory;
import com.optahub.awi.service.promostandards.data.media.ImageReference;
import com.optahub.awi.service.promostandards.data.price.Location;
import com.optahub.awi.service.promostandards.data.price.PartPrice;
import com.optahub.awi.service.promostandards.data.price.PartPriceWrapper;
import com.optahub.awi.service.promostandards.data.price.ProductPrice;
import com.optahub.awi.service.promostandards.service.MediaContentService;
import com.optahub.awi.service.promostandards.service.ProductInventoryService;
import com.optahub.awi.service.promostandards.service.ProductPricingService;
import com.optahub.awi.service.rest.data.detail.*;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;

@Service
public class DetailService {

    private final ElasticSearchService searchService;
    private final ProductPricingService pricingService;
    private final ProductInventoryService inventoryService;
    private final MediaContentService mediaContentService;
    @Value("${markup.percentage}")
    private double markupPercentage;
    @Value("${franchise.id}")
    private String odpFranchiseId;

    @Autowired
    public DetailService(final ElasticSearchService searchService,
                         final ProductPricingService pricingService,
                         final ProductInventoryService inventoryService,
                         final MediaContentService mediaContentService) {

        this.searchService = searchService;
        this.pricingService = pricingService;
        this.inventoryService = inventoryService;
        this.mediaContentService = mediaContentService;
    }

    public ProductDetail getProduct(String franchiseId, final String productId, final Boolean fresh) {

        final PromoStandardsProduct product = searchService.getById(productId);
        final ProductDetail detail = new ProductDetail();

        if(product != null) {

            if(!fresh) {
                final ProductDetail cache = product.getCacheObject();
                if (cache != null) {
                    final Instant cacheTime = product.getCacheTime();
                    final Instant current = Instant.now();
                    if (!cacheTime.isBefore(current.minus(24, ChronoUnit.HOURS))
                            && cacheTime.isBefore(current)) {
                        return cache;
                    }
                }
            }

            detail.setProductId(productId);
            if(!CollectionUtils.isEmpty(product.getCategoryList())) {
                detail.setCategory(product.getCategoryList().stream()
                        .map(category -> {
                            if(StringUtils.isNotEmpty(category.getCategory()) && StringUtils.isNotEmpty(category.getSubCategory())) {
                                return category.getCategory() + " | " + category.getSubCategory();
                            } else if (StringUtils.isEmpty(category.getCategory()) && StringUtils.isNotEmpty(category.getSubCategory())) {
                                return category.getSubCategory();
                            } else if (StringUtils.isNotEmpty(category.getCategory()) && StringUtils.isEmpty(category.getSubCategory())) {
                                return category.getCategory();
                            } else {
                                return "INVALID-CATEGORY";
                            }
                        }).filter(val -> !val.equals("INVALID-CATEGORY"))
                        .collect(Collectors.joining(", ")));
            }


            detail.setSuppId(product.getSupplierCode());
            detail.setItemNum(product.getProductId());
            detail.setDescription(product.getDescription());
            detail.setDescriptionList(product.getDescriptionList());
            Pair<String, String> picInfo = mediaContentService.getPrimaryImage(product);
            detail.setPicLink(picInfo.getLeft());
            detail.setPicName(picInfo.getRight());
            detail.setLineName(product.getLineName());
            detail.setPrName(product.getTitle());
            detail.setSupplierName(product.getSupplierName());

            final Set<String> colorList = new HashSet<>();
            final Set<String> partCountryList = new HashSet<>();
            for(final ProductPart part : product.getPartList()) {
                if(part != null && !CollectionUtils.isEmpty(part.getColorList())) {
                    colorList.addAll(new HashSet<>(part.getColorList()));
                }

                if(part != null && StringUtils.isNotEmpty(part.getCountryOfOrigin())) {
                    partCountryList.add(part.getCountryOfOrigin());
                }
            }

            detail.setColors(colorList.stream().sorted()
                    .map(value -> StringUtils.capitalize(value.toLowerCase()))
                    .collect(Collectors.joining(", ")));

            detail.setAssembledInCountry(partCountryList.stream().collect(Collectors.joining(", ")));
            detail.setMadeInCountry(partCountryList.stream().collect(Collectors.joining(", ")));
            detail.setDecoratedInCountry(partCountryList.stream().collect(Collectors.joining(", ")));
            detail.setMadeUSA(StringUtils.contains(detail.getMadeInCountry(), "US") ? "1" : "0");
            detail.setImprintArea(product.getImprintSize());
            if(!CollectionUtils.isEmpty(product.getCategoryList())) {
                detail.setKeywords(product.getCategoryList().stream().map(ProductCategory::getSubCategory)
                        .collect(Collectors.joining(", ")));
            }
            detail.setProdTime("Please Enquire");
            detail.setComment("Additional setup charges may apply, call for details");

            final ProductPrice price = pricingService.getPrice(product);
            final ProductInventory inventory = inventoryService.getInventory(product).orElse(null);
            detail.setLocationList(price.getLocationList());

            if(BooleanUtils.isTrue(product.getApparel())) {
                this.buildApparelPartList(detail, price, inventory, product);
                detail.setApparel(true);
                detail.setApparelDescription(CollectionUtils.isEmpty(detail.getDescriptionList()) ? detail.getDescription() : detail.getDescriptionList().stream().collect(Collectors.joining(", ")));
                detail.setApparelSizes(detail.getPartList().stream().flatMap(part -> part.getApparelInventory().stream()).map(ApparelInventory::getSize).collect(Collectors.toSet()).stream().collect(Collectors.joining(", ")));
            } else {
                this.buildPartList(detail, price, inventory, product);
            }

            product.setCacheObject(detail);
            product.setCacheTime(Instant.now());
            this.searchService.updateProduct(product);
        }
        if(franchiseId != null && franchiseId.equals(odpFranchiseId) && BooleanUtils.isTrue(product.getApparel())){
            for (PartDetail partDetail : detail.getPartList()) {
                if (partDetail.getNetPrice() != null) {
                    for (PartPrice partPrice : partDetail.getNetBlank()) {
                        double originalPrice = partPrice.getPrice();
                        double updatedPrice = applyMarkup(originalPrice);
                        partPrice.setPrice(updatedPrice);
                    }
                }
            }

            MarkupDetail markupDetail = new MarkupDetail();
            markupDetail.setMarkup(true);
            markupDetail.setMarkupPercentage("43%");
            detail.setMarkupDetail(markupDetail);
        }
        if(franchiseId != null && franchiseId.equals(odpFranchiseId)){
            detail.setNet1(applyMarkup(detail.getNet1()));
            detail.setNet2(applyMarkup(detail.getNet2()));
            detail.setNet3(applyMarkup(detail.getNet3()));
            detail.setNet4(applyMarkup(detail.getNet4()));
            detail.setNet5(applyMarkup(detail.getNet5()));
            detail.setNet7(applyMarkup(detail.getNet7()));
            detail.setNet8(applyMarkup(detail.getNet8()));
            detail.setNet9(applyMarkup(detail.getNet9()));

            MarkupDetail markupDetail = new MarkupDetail();
            markupDetail.setMarkup(true);
            markupDetail.setMarkupPercentage("43%");
            detail.setMarkupDetail(markupDetail);
        }

        if (franchiseId == null || !franchiseId.equals(odpFranchiseId)) {
            MarkupDetail markupDetail = new MarkupDetail();
            markupDetail.setMarkup(false);
            markupDetail.setMarkupPercentage("0%");
            detail.setMarkupDetail(markupDetail);
        }
        return detail;
    }

    private double applyMarkup(double originalPrice) {
        if (originalPrice >= 0) {
            double markupPrice = originalPrice + (originalPrice * markupPercentage);
            return Math.round(markupPrice * 100.0) / 100.0;
        }
        return originalPrice;
    }

    private String applyMarkup(String originalPrice) {
        if (originalPrice != null && !originalPrice.isEmpty()) {
            BigDecimal price = new BigDecimal(originalPrice);
            BigDecimal markupPrice = price.add(price.multiply(BigDecimal.valueOf(markupPercentage)));
            return markupPrice.setScale(2, BigDecimal.ROUND_HALF_UP).toString();
        }
        return originalPrice;
    }


    public List<Location> getProductLocations(final String productId,
                                              final String supplierCode) {

        final ProductPrice price = pricingService.getListPrice(supplierCode, productId);
        return price.getLocationList();
    }



    private List<PartDetail> buildPartList(final ProductDetail detail,
                                           final ProductPrice price,
                                           final ProductInventory inventory,
                                           final PromoStandardsProduct product) {
        final List<PartDetail> partList = new ArrayList<>();
        boolean foundDefault = false;
        PartDetail firstPartDetail = null;
        for( final ProductPart productPart : product.getPartList()) {
            final PartDetail partDetail = new PartDetail();
            partDetail.setPartId(productPart.getPartId());
            partDetail.setPartName(productPart.getPartId());
            partDetail.setDimension(productPart.getDimension());
            partDetail.setShippingList(productPart.getShippingList());

            final List<ImageReference> partImages = product.getImageList().stream()
                    .filter((ref) -> productPart.getPartId().equalsIgnoreCase(ref.getPartId()))
                    .collect(Collectors.toList());
            partDetail.setPartImages(partImages);

            final PartPriceWrapper partPriceWrapper = price.getPartPrices();
            final List<PartPrice> partNetPrice = partPriceWrapper.getNetPrice() != null ? partPriceWrapper.getNetPrice().get(productPart.getPartId()) : Collections.emptyList();
            final List<PartPrice> partListPrice = partPriceWrapper.getListPrice() != null ? partPriceWrapper.getListPrice().get(productPart.getPartId()) : Collections.emptyList();
            final List<PartPrice> partListBlankPrice = partPriceWrapper.getListBlankPrice() != null ? partPriceWrapper.getListBlankPrice().get(productPart.getPartId()) : Collections.emptyList();
            final List<PartPrice> partNetBlankPrice = partPriceWrapper.getNetBlankPrice() != null ? partPriceWrapper.getNetBlankPrice().get(productPart.getPartId()) : Collections.emptyList();

            partDetail.setListPrice(partListPrice);
            partDetail.setNetPrice(partNetPrice);
            partDetail.setListBlank(partListBlankPrice);
            partDetail.setNetBlank(partNetBlankPrice);

            if(inventory != null) {
                ProductPartInventory partInventory = inventory.getMainInventory().get(productPart.getPartId());

                if(partInventory != null && !foundDefault) {
                    this.populateDefaultDetails(partDetail, detail);
                    foundDefault = true;
                }

                if(partInventory == null) {
                    partInventory = inventory.getOptionalInventory().get(productPart.getPartId());
                }

                if(partInventory != null) {
                    partDetail.setInventory(partInventory.getLocationInventoryList());
                    partDetail.setPartName(StringUtils.isNotEmpty(partInventory.getPartColor()) ? partInventory.getPartColor() : partInventory.getPartId());
                    partDetail.setPartDescription(partInventory.getDescription());
                    partDetail.setOptional(partInventory.isOptional());
                }
            }else if(!foundDefault) {
                this.populateDefaultDetails(partDetail, detail);
                foundDefault = true;
            }
            if(firstPartDetail == null) {
                firstPartDetail = partDetail;
            }

            partList.add(partDetail);
        }

        if(!foundDefault) {
            this.populateDefaultDetails(firstPartDetail, detail);
        }
        final List<PartDetail> sortedParts = partList.stream()
                .sorted(Comparator.comparing(part -> StringUtils.isEmpty(part.getPartName()) ? part.getPartId() : part.getPartName()))
                .collect(Collectors.toList());
        detail.setPartList(sortedParts);
        return partList;
    }

    private List<PartDetail> buildApparelPartList(final ProductDetail detail,
                                                  final ProductPrice price,
                                                  final ProductInventory inventory,
                                                  final PromoStandardsProduct product) {

        final List<PartDetail> partList = new ArrayList<>();
        final Map<String,List<ProductPart>> partMap = new HashMap<>();
        if("SS".equals(product.getSupplierCode())) {
            partMap.putAll(product.getPartList().stream()
                    .collect(groupingBy(part -> StringUtils.isNotEmpty(part.getPartColors().get(0).getColorName()) ? part.getPartColors().get(0).getColorName() : part.getPartColors().get(0).getStandardColorName())));
        } else {
            partMap.putAll(product.getPartList().stream()
                    .collect(groupingBy(part -> StringUtils.isNotEmpty(part.getPartColors().get(0).getStandardColorName()) ? part.getPartColors().get(0).getStandardColorName() : part.getPartColors().get(0).getColorName())));
        }

        boolean foundDefault = false;
        for( final String colorPpms : partMap.keySet()) {
            final List<ProductPart> colorPartList = partMap.get(colorPpms);

            if(CollectionUtils.isEmpty(colorPartList)) {
                continue;
            }

            final List<ProductPart> sortedParts = colorPartList.stream()
                    .sorted(Comparator.comparing(item -> ApparelSizeType.getApparelSize(item.getApparelSize().getSize()).getPosition()))
                    .collect(Collectors.toList());

            final ProductPart productPart = sortedParts.get(0);

            final PartDetail partDetail = new PartDetail();
            partDetail.setPartId(productPart.getPartId());
            partDetail.setPartName(this.buildApparelPartName(productPart.getPartColors(), product.getSupplierCode()));
            partDetail.setHexValue(this.buildApparelPartHexValue(productPart.getPartColors()));

            partDetail.setDimension(productPart.getDimension());
            partDetail.setShippingList(productPart.getShippingList());
            partDetail.setApparelStyle(productPart.getApparelSize() != null && StringUtils.isNotEmpty(productPart.getApparelSize().getStyle()) ? productPart.getApparelSize().getStyle().toUpperCase() : "-");

            final List<ImageReference> partImages = new ArrayList<>();

            final PartPriceWrapper partPriceWrapper = price.getPartPrices();
            final List<PartPrice> partNetPrice = new ArrayList<>();
            final List<PartPrice> partListPrice = new ArrayList<>();
            final List<PartPrice> partListBlankPrice = new ArrayList<>();
            final List<PartPrice> partCustomerPrice = new ArrayList<>();
            final List<PartPrice> partCustomerBlankPrice = new ArrayList<>();
            final List<PartPrice> partNetBlankPrice = new ArrayList<>();
            final List<ApparelInventory> apparelInventories = new ArrayList<>();
            final List<PackagingDetails> packagingDetails = new ArrayList<>();
            final List<DimensionDetails> dimensionDetails = new ArrayList<>();

            for(final ProductPart part : sortedParts) {
                partImages.addAll(product.getImageList().stream()
                        .filter((ref) -> productPart.getPartId().equalsIgnoreCase(ref.getPartId()))
                        .collect(Collectors.toList()));

                if (partPriceWrapper.getNetPrice() != null) {
                    final List<PartPrice> netPriceList = partPriceWrapper.getNetPrice().get(part.getPartId());
                    if(!CollectionUtils.isEmpty(netPriceList)) {
                        PartPrice netPrice = netPriceList.get(0);
                        netPrice.setSize(part.getApparelSize().getSize());
                        partNetPrice.add(netPrice);
                    }
                }

                if (partPriceWrapper.getListPrice() != null) {
                    final List<PartPrice> listPriceList = partPriceWrapper.getListPrice().get(part.getPartId());
                    if(!CollectionUtils.isEmpty(listPriceList)) {
                        PartPrice listPrice = listPriceList.get(0);
                        listPrice.setSize(part.getApparelSize().getSize());
                        partListPrice.add(listPrice);
                    }
                }

                if (partPriceWrapper.getListBlankPrice() != null) {
                    final List<PartPrice> ListBlankPriceList = partPriceWrapper.getListBlankPrice().get(part.getPartId());
                    if(!CollectionUtils.isEmpty(ListBlankPriceList)) {
                        PartPrice listBlankPrice = ListBlankPriceList.get(0);
                        listBlankPrice.setSize(part.getApparelSize().getSize());
                        partListBlankPrice.add(listBlankPrice);
                    }
                }

                if (partPriceWrapper.getNetBlankPrice() != null) {
                    final List<PartPrice> netBlankPriceList = partPriceWrapper.getNetBlankPrice().get(part.getPartId());
                    if(!CollectionUtils.isEmpty(netBlankPriceList)) {
                        PartPrice netBlankPrice = netBlankPriceList.get(0);
                        netBlankPrice.setSize(part.getApparelSize().getSize());
                        partNetBlankPrice.add(netBlankPrice);
                    }
                }

                if (partPriceWrapper.getCustomerPrice() != null) {
                    final List<PartPrice> custPriceList = partPriceWrapper.getCustomerPrice().get(part.getPartId());
                    if(!CollectionUtils.isEmpty(custPriceList)) {
                        PartPrice custPrice = custPriceList.get(0);
                        custPrice.setSize(part.getApparelSize().getSize());
                        partCustomerPrice.add(custPrice);
                    }
                }

                if (partPriceWrapper.getCustomerBlankPrice() != null) {
                    final List<PartPrice> custBlankPriceList = partPriceWrapper.getCustomerBlankPrice().get(part.getPartId());
                    if(!CollectionUtils.isEmpty(custBlankPriceList)) {
                        PartPrice custBlankPrice = custBlankPriceList.get(0);
                        custBlankPrice.setSize(part.getApparelSize().getSize());
                        partCustomerBlankPrice.add(custBlankPrice);
                    }
                }

                if(inventory != null) {
                    ProductPartInventory partInventory = inventory.getMainInventory().get(part.getPartId());
                    if(partInventory == null) {
                        partInventory = inventory.getOptionalInventory().get(part.getPartId());
                    }

                    if(partInventory != null) {
                        ApparelInventory apparelInventory = new ApparelInventory();
                        apparelInventory.setSize(part.getApparelSize().getSize());
                        apparelInventory.setInventory(partInventory.getLocationInventoryList());
                        apparelInventories.add(apparelInventory);
                    }
                }

                if(part.getDimension() != null) {
                    final ProductPartDimension dimension = part.getDimension();
                    DimensionDetails dimDetails = new DimensionDetails();
                    dimDetails.setHeight(String.format("%.2f",dimension.getHeight() != null ? dimension.getHeight() : 0d));
                    dimDetails.setWidth(String.format("%.2f",dimension.getWidth() != null ? dimension.getWidth() : 0d));
                    dimDetails.setLength(String.format("%.2f",dimension.getDepth() != null ? dimension.getDepth() : 0d));
                    dimDetails.setWeight(String.format("%.2f",dimension.getWeight() != null ? dimension.getWeight() : 0d));
                    dimDetails.setDisplayString(dimDetails.getHeight() + " X " + dimDetails.getWidth() + " X " + dimDetails.getLength());
                    dimDetails.setSize(part.getApparelSize().getSize());

                    dimensionDetails.add(dimDetails);
                }

                if(!CollectionUtils.isEmpty(part.getShippingList())) {
                    final ProductPartShipping shippingDefault = part.getShippingList().get(0);
                    PackagingDetails packDetails = new PackagingDetails();
                    packDetails.setHeight(String.format("%.2f",shippingDefault.getHeight() != null ? shippingDefault.getHeight() : 0d));
                    packDetails.setWidth(String.format("%.2f",shippingDefault.getWidth() != null ? shippingDefault.getWidth() : 0d));
                    packDetails.setLength(String.format("%.2f",shippingDefault.getDepth() != null ? shippingDefault.getDepth() : 0d));
                    packDetails.setWeight(String.format("%.2f",shippingDefault.getWeight() != null ? shippingDefault.getWeight() : 0d));
                    packDetails.setQuantity(String.format("%.2f",shippingDefault.getQuantity() != null ? shippingDefault.getQuantity() : 0d));
                    packDetails.setDisplayString(packDetails.getHeight() + " X " + packDetails.getWidth() + " X " + packDetails.getLength());
                    packDetails.setSize(part.getApparelSize().getSize());

                    packagingDetails.add(packDetails);
                }
            }

            if(CollectionUtils.isEmpty(partImages)) {
                if(!CollectionUtils.isEmpty(productPart.getPartColors())) {
                    partImages.addAll(product.getImageList().stream()
                            .filter((image) -> StringUtils.isNotEmpty(image.getColor()))
                            .filter((ref) -> ref.getColor().equals(productPart.getPartColors().get(0).getColorName()))
                            .collect(Collectors.toList()));

                if(this.ImageListIsEmptyOrOnlySwatch(partImages)) {
                    partImages.addAll(product.getImageList().stream().filter(image -> image.getClassId().contains("1006")).collect(Collectors.toList()));
                }

                } else {
                    partImages.addAll(product.getImageList().stream()
                            .filter((ref) -> ref.getPartId() == null)
                            .collect(Collectors.toList()));
                }
            }

            partDetail.setPartImages(mediaContentService.prioritizeImageList(partImages, product.getSupplierCode()));
            partDetail.setSwatchImage(this.getSwatchImage(partImages));
            partDetail.setListPrice(partListPrice);
            partDetail.setNetPrice(partNetPrice);
            partDetail.setListBlank(partListBlankPrice);
            partDetail.setNetBlank(partNetBlankPrice);
            partDetail.setCustPrice(partCustomerPrice);
            partDetail.setCustBlank(partCustomerBlankPrice);
            partDetail.setApparelInventory(apparelInventories);

            if(!CollectionUtils.isEmpty(dimensionDetails)) {
                ApparelDimension dimension = new ApparelDimension();
                dimension.setDetailList(dimensionDetails);
                dimension.setSizeUom(PriceUOMType.getLabelForUom(partDetail.getDimension().getDimensionUnit()));
                dimension.setWeightUom(PriceUOMType.getLabelForUom(partDetail.getDimension().getWeightUnit()));
                partDetail.setApparelDimension(dimension);
            }

            if(!CollectionUtils.isEmpty(packagingDetails)) {
                ApparelPackaging packaging = new ApparelPackaging();
                packaging.setDetails(packagingDetails);
                packaging.setSizeUom(PriceUOMType.getLabelForUom(partDetail.getShippingList().get(0).getDimensionUnit()));
                packaging.setWeightUom(PriceUOMType.getLabelForUom(partDetail.getShippingList().get(0).getWeightUnit()));
                packaging.setQuantityUom(PriceUOMType.getLabelForUom(partDetail.getShippingList().get(0).getPackageType()));
                partDetail.setApparelPackaging(packaging);
            }

            if(!foundDefault) {
                this.populateApparelDefaultDetails(partDetail, detail);
                foundDefault = true;
            }

            partList.add(partDetail);
        }

        final List<PartDetail> sortedParts = partList.stream()
                .sorted(Comparator.comparing(part -> StringUtils.isEmpty(part.getPartName()) ? part.getPartId() : part.getPartName()))
                .collect(Collectors.toList());
        detail.setPartList(sortedParts);

        return partList;
    }

    private void populateApparelDefaultDetails(final PartDetail partDetail, final ProductDetail productDetail) {

        int count = 0;
        productDetail.setCatPrc1("0");
        productDetail.setPrc1("0");
        productDetail.setNet1("0");
        productDetail.setQty1("0");

        productDetail.setCatPrc2("0");
        productDetail.setPrc2("0");
        productDetail.setNet2("0");
        productDetail.setQty2("0");

        productDetail.setCatPrc3("0");
        productDetail.setPrc3("0");
        productDetail.setNet3("0");
        productDetail.setQty3("0");

        productDetail.setCatPrc4("0");
        productDetail.setPrc4("0");
        productDetail.setNet4("0");
        productDetail.setQty4("0");

        productDetail.setCatPrc5("0");
        productDetail.setPrc5("0");
        productDetail.setNet5("0");
        productDetail.setQty5("0");

        productDetail.setCatPrc7("0");
        productDetail.setPrc7("0");
        productDetail.setNet7("0");
        productDetail.setQty7("0");

        productDetail.setCatPrc8("0");
        productDetail.setPrc8("0");
        productDetail.setNet8("0");
        productDetail.setQty8("0");

        productDetail.setCatPrc9("0");
        productDetail.setPrc9("0");
        productDetail.setNet9("0");
        productDetail.setQty9("0");

        for(final PartPrice listPrice : partDetail.getListBlank()) {
            final String size = listPrice.getSize();
            PartPrice netPrice = null;
            PartPrice custPrice = null;
            if(partDetail.getNetPrice() != null) {
                netPrice = partDetail.getNetBlank().stream()
                        .filter(price -> price.getSize().equals(size))
                        .findAny().orElse(null);
            }
            if(partDetail.getCustPrice() != null) {
                custPrice = partDetail.getCustBlank().stream()
                        .filter(price -> price.getSize().equals(size))
                        .findAny().orElse(null);
            }

            if(count == 0) {
                productDetail.setCatPrc1(String.format("%.2f",custPrice.getPrice()));
                productDetail.setPrc1(String.format("%.2f",listPrice.getPrice()));
                productDetail.setNet1(netPrice != null ? String.format("%.2f",netPrice.getPrice()): "0");
                productDetail.setQty1(size);
            } else if (count == 1) {
                productDetail.setCatPrc2(String.format("%.2f",custPrice.getPrice()));
                productDetail.setPrc2(String.format("%.2f",listPrice.getPrice()));
                productDetail.setNet2(netPrice != null ? String.format("%.2f",netPrice.getPrice()): "0");
                productDetail.setQty2(size);
            } else if (count == 2) {
                productDetail.setCatPrc3(String.format("%.2f",custPrice.getPrice()));
                productDetail.setPrc3(String.format("%.2f",listPrice.getPrice()));
                productDetail.setNet3(netPrice != null ? String.format("%.2f",netPrice.getPrice()): "0");
                productDetail.setQty3(size);
            }else if (count == 3) {
                productDetail.setCatPrc4(String.format("%.2f",custPrice.getPrice()));
                productDetail.setPrc4(String.format("%.2f",listPrice.getPrice()));
                productDetail.setNet4(netPrice != null ? String.format("%.2f",netPrice.getPrice()): "0");
                productDetail.setQty4(size);
            }else if (count == 4) {
                productDetail.setCatPrc5(String.format("%.2f",custPrice.getPrice()));
                productDetail.setPrc5(String.format("%.2f",listPrice.getPrice()));
                productDetail.setNet5(netPrice != null ? String.format("%.2f",netPrice.getPrice()): "0");
                productDetail.setQty5(size);
            }else if (count == 5) {
                productDetail.setCatPrc7(String.format("%.2f",custPrice.getPrice()));
                productDetail.setPrc7(String.format("%.2f",listPrice.getPrice()));
                productDetail.setNet7(netPrice != null ? String.format("%.2f",netPrice.getPrice()): "0");
                productDetail.setQty7(size);
            }else if (count == 6) {
                productDetail.setCatPrc8(String.format("%.2f",custPrice.getPrice()));
                productDetail.setPrc8(String.format("%.2f",listPrice.getPrice()));
                productDetail.setNet8(netPrice != null ? String.format("%.2f",netPrice.getPrice()): "0");
                productDetail.setQty8(size);
            }else if (count == 7) {
                productDetail.setCatPrc9(String.format("%.2f",custPrice.getPrice()));
                productDetail.setPrc9(String.format("%.2f",listPrice.getPrice()));
                productDetail.setNet9(netPrice != null ? String.format("%.2f",netPrice.getPrice()): "0");
                productDetail.setQty9(size);
            }

            count++;
        }

        productDetail.setPrCode("CCCCC");
        productDetail.setCatPrCode("CCCCC");

        productDetail.setApparelStyle(partDetail.getApparelStyle());
        productDetail.setDefaultPart(partDetail.getPartId());
        productDetail.setImages(partDetail.getPartImages());
    }

    private void populateDefaultDetails(final PartDetail partDetail, final ProductDetail productDetail) {

        int count = 0;
        productDetail.setCatPrc1("0");
        productDetail.setPrc1("0");
        productDetail.setNet1("0");
        productDetail.setQty1("0");

        productDetail.setCatPrc2("0");
        productDetail.setPrc2("0");
        productDetail.setNet2("0");
        productDetail.setQty2("0");

        productDetail.setCatPrc3("0");
        productDetail.setPrc3("0");
        productDetail.setNet3("0");
        productDetail.setQty3("0");

        productDetail.setCatPrc4("0");
        productDetail.setPrc4("0");
        productDetail.setNet4("0");
        productDetail.setQty4("0");

        productDetail.setCatPrc5("0");
        productDetail.setPrc5("0");
        productDetail.setNet5("0");
        productDetail.setQty5("0");

        final List<PartPrice> listPriceSorted = partDetail
                .getListPrice().stream()
                .sorted(Comparator.comparing(PartPrice::getMinQuantity))
                .collect(Collectors.toList());
        for(final PartPrice listPrice : listPriceSorted) {
            final int quantity = listPrice.getMinQuantity();
            PartPrice netPrice = null;
            if(partDetail.getNetPrice() != null) {
                netPrice = partDetail.getNetPrice().stream()
                        .filter(price -> price.getMinQuantity() == quantity)
                        .findAny().orElse(null);
            }

            if(count == 0) {
                productDetail.setCatPrc1(String.format("%.2f",listPrice.getPrice()));
                productDetail.setPrc1(String.format("%.2f",listPrice.getPrice()));
                productDetail.setNet1(netPrice != null ? String.format("%.2f",netPrice.getPrice()): "0");
                productDetail.setQty1(String.valueOf(quantity));
            } else if (count == 1) {
                productDetail.setCatPrc2(String.format("%.2f",listPrice.getPrice()));
                productDetail.setPrc2(String.format("%.2f",listPrice.getPrice()));
                productDetail.setNet2(netPrice != null ? String.format("%.2f",netPrice.getPrice()): "0");
                productDetail.setQty2(String.valueOf(quantity));
            } else if (count == 2) {
                productDetail.setCatPrc3(String.format("%.2f",listPrice.getPrice()));
                productDetail.setPrc3(String.format("%.2f",listPrice.getPrice()));
                productDetail.setNet3(netPrice != null ? String.format("%.2f",netPrice.getPrice()): "0");
                productDetail.setQty3(String.valueOf(quantity));
            }else if (count == 3) {
                productDetail.setCatPrc4(String.format("%.2f",listPrice.getPrice()));
                productDetail.setPrc4(String.format("%.2f",listPrice.getPrice()));
                productDetail.setNet4(netPrice != null ? String.format("%.2f",netPrice.getPrice()): "0");
                productDetail.setQty4(String.valueOf(quantity));
            }else if (count == 4) {
                productDetail.setCatPrc5(String.format("%.2f",listPrice.getPrice()));
                productDetail.setPrc5(String.format("%.2f",listPrice.getPrice()));
                productDetail.setNet5(netPrice != null ? String.format("%.2f",netPrice.getPrice()): "0");
                productDetail.setQty5(String.valueOf(quantity));
            }

            count++;
        }

        productDetail.setPrCode("CCCCC");
        productDetail.setCatPrCode("CCCCC");

        if(partDetail.getDimension() != null) {
            final ProductPartDimension dimension = partDetail.getDimension();
            StringBuilder dimensionBuilder = new StringBuilder();

            if(dimension.getHeight() != null && dimension.getHeight() > 0) {
                dimensionBuilder.append("Height "+dimension.getHeight()+" "+dimension.getDimensionUnit());
            }
            if(dimension.getWidth() != null && dimension.getWidth() > 0) {
                dimensionBuilder.append(" Width "+dimension.getWidth()+" "+dimension.getDimensionUnit());
            }
            if(dimension.getDepth() != null && dimension.getDepth() > 0) {
                dimensionBuilder.append(" Depth "+dimension.getDepth()+" "+dimension.getDimensionUnit());
            }
            if(dimension.getWeight() != null && dimension.getWeight() > 0) {
                dimensionBuilder.append(" Weight "+dimension.getWeight()+" "+dimension.getWeightUnit());
            }
            productDetail.setDimensions(dimensionBuilder.toString());
        }

        if(!CollectionUtils.isEmpty(partDetail.getShippingList())) {
            final ProductPartShipping shippingDefault = partDetail.getShippingList().get(0);
            productDetail.setPackageStr(shippingDefault.getPackageType());
            productDetail.setWeightPerCarton(String.valueOf(shippingDefault.getWeight()));
            productDetail.setUnitsPerCarton(String.valueOf(shippingDefault.getQuantity()));
            productDetail.setCartonH(String.valueOf(shippingDefault.getWeight()));
            productDetail.setCartonL(String.valueOf(shippingDefault.getWidth()));
            productDetail.setCartonW(String.valueOf(shippingDefault.getDepth()));
        }

        productDetail.setDefaultPart(partDetail.getPartId());
        productDetail.setImages(partDetail.getPartImages());
    }

    private String buildApparelPartName(final List<ProductPartColor> partColorList, final String supplierCode) {
        final StringBuilder partNameBuilder = new StringBuilder();
        for(ProductPartColor partColor : partColorList) {
            if(partNameBuilder.length() > 0) {
                partNameBuilder.append(" / ");
            }

            if("SS".equals(supplierCode)) {
                if (StringUtils.isNotEmpty(partColor.getColorName())) {
                    partNameBuilder.append(partColor.getColorName());
                }
                else if(StringUtils.isNotEmpty(partColor.getStandardColorName())) {
                    partNameBuilder.append(partColor.getStandardColorName());
                }
            } else {
                if(StringUtils.isNotEmpty(partColor.getStandardColorName())) {
                    partNameBuilder.append(partColor.getStandardColorName());
                } else if (StringUtils.isNotEmpty(partColor.getColorName())) {
                    partNameBuilder.append(partColor.getColorName());
                }
            }


            if(StringUtils.isNotEmpty(partColor.getApproximatePpms())) {
                partNameBuilder.append(" ( " + partColor.getApproximatePpms() + " )" );
            }
        }

        return partNameBuilder.toString();
    }

    private List<String> buildApparelPartHexValue(final List<ProductPartColor> partColorList) {
        final List<String> hexList = new ArrayList<>();
        for(ProductPartColor partColor : partColorList) {
            hexList.add(partColor.getHexValue());
        }

        return hexList;
    }

    private String getSwatchImage(final List<ImageReference> partImageList) {
        if(CollectionUtils.isEmpty(partImageList)) {
            return StringUtils.EMPTY;
        }

        String swatchUrl = StringUtils.EMPTY;
        for(final ImageReference image : partImageList) {
            if(StringUtils.isNotEmpty(image.getClassId())) {
                if ("504".equals(image.getClassId())) {
                    return image.getUrl();
                }

                if ("1004".equals(image.getClassId())) {
                    swatchUrl = image.getUrl();
                }
            }
        }

        return swatchUrl;
    }

    private boolean ImageListIsEmptyOrOnlySwatch(final List<ImageReference> images) {
        if(CollectionUtils.isEmpty(images)) {
            return true;
        }
       else if (images.size() == 1) {
            final ImageReference firstImage = images.get(0);
            if(StringUtils.isNotEmpty(firstImage.getClassId())
                    && (firstImage.getClassId().contains("504") || firstImage.getClassId().contains("1004"))) {

                return true;
            }
        }

       return  false;
    }
}
