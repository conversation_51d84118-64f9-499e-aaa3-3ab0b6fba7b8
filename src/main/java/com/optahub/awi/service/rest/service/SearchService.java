package com.optahub.awi.service.rest.service;

import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._types.query_dsl.Query;
import co.elastic.clients.elasticsearch.core.search.Hit;
import com.optahub.awi.service.elastic.data.PromoStandardsProduct;
import com.optahub.awi.service.elastic.data.supplier.SupplierData;
import com.optahub.awi.service.elastic.service.ElasticQueryBuilderService;
import com.optahub.awi.service.elastic.service.ElasticSearchObjectMapper;
import com.optahub.awi.service.elastic.service.ElasticSearchService;
import com.optahub.awi.service.rest.data.search.*;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.*;

@Service
public class SearchService {

    private final ElasticSearchObjectMapper mapper;
    private final ElasticSearchService searchService;
    private final ElasticQueryBuilderService queryBuilderService;
    private final ElasticsearchClient elasticsearchClient;

    @Autowired
    public SearchService(final ElasticSearchObjectMapper mapper,
                         final ElasticSearchService searchService,
                         final ElasticQueryBuilderService queryBuilderService, ElasticsearchClient elasticsearchClient) {

        this.mapper = mapper;
        this.searchService = searchService;
        this.queryBuilderService = queryBuilderService;
        this.elasticsearchClient = elasticsearchClient;
    }

    public SearchResponse doSearch(final SearchRequest request) {
        final co.elastic.clients.elasticsearch.core.SearchRequest promoStandardsQuery = queryBuilderService.buildSearchQuery(request);

        //final SearchHits<PromoStandardsProduct> hits = searchService.searchProducts(query);
        final co.elastic.clients.elasticsearch.core.SearchResponse<PromoStandardsProduct> promoStandardsProductHits;
        try {
            promoStandardsProductHits  = elasticsearchClient.search(promoStandardsQuery, PromoStandardsProduct.class);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        final List<Hit<PromoStandardsProduct>> hitList = promoStandardsProductHits.hits().hits();
        final List<SearchResponseItem> itemList = new ArrayList<>();
        int count = 0;
        for(Hit<PromoStandardsProduct> hit : hitList) {
            final PromoStandardsProduct content = hit.source();
            final SearchResponseItem responseItem = mapper.toSearchResponseItem(content, count);
            itemList.add(responseItem);
            count ++;
        }

        final SearchResponseItemContainer container = new SearchResponseItemContainer();
        container.setItem(itemList);

        final SearchResponseData responseData = new SearchResponseData();

        responseData.setItems(container);
        responseData.setSuccess("1");
        responseData.setTotalFound(String.valueOf(promoStandardsProductHits.hits().total()));

        final SearchResponse response = new SearchResponse();
        response.setData(responseData);

        return response;
    }

    private boolean isKeywordSearch(final SearchRequest request) {

        if (ArrayUtils.isEmpty(request.getCategories()) ||
                StringUtils.isEmpty(request.getColors())
                || StringUtils.isEmpty(request.getItemName()) || StringUtils.isEmpty(request.getLineName())) {

            return true;
        }

        return false;
    }
}
