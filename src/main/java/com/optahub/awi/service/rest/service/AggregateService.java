package com.optahub.awi.service.rest.service;

import co.elastic.clients.elasticsearch._types.aggregations.Aggregate;
import co.elastic.clients.elasticsearch._types.aggregations.StringTermsAggregate;
import co.elastic.clients.elasticsearch._types.aggregations.StringTermsBucket;
import co.elastic.clients.elasticsearch.core.SearchResponse;
import com.optahub.awi.service.elastic.service.ElasticQueryBuilderService;
import com.optahub.awi.service.elastic.service.ElasticSearchObjectMapper;
import com.optahub.awi.service.elastic.service.ElasticSearchService;
import com.optahub.awi.service.rest.data.aggregate.AggregateResponse;
import com.optahub.awi.service.rest.data.aggregate.NameObject;
import com.optahub.awi.service.rest.data.aggregate.TextObject;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.query.Query;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.*;

import java.util.stream.Collectors;

import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch.core.SearchRequest;
import co.elastic.clients.elasticsearch.core.search.Hit;

@Service
public class AggregateService {

    private final ElasticSearchObjectMapper elasticObjectMapper;
    private final ElasticSearchService elasticSearchService;
    private final ElasticQueryBuilderService queryBuilderService;
    private final ElasticsearchClient elasticsearchClient;

    @Autowired
    public AggregateService(final ElasticSearchObjectMapper elasticObjectMapper, final ElasticSearchService elasticSearchService, final ElasticQueryBuilderService queryBuilderService,
        ElasticsearchClient elasticsearchClient) {

        this.elasticObjectMapper = elasticObjectMapper;
        this.elasticSearchService = elasticSearchService;
        this.queryBuilderService = queryBuilderService;
        this.elasticsearchClient = elasticsearchClient;
    }

    //@Cacheable("category")
    public AggregateResponse getIndexedCategories(String supplier, String brands, Boolean apparel) throws IOException {
        String[] supplierArray = supplier != null ? supplier.split("\\s*,\\s*") : new String[] {};
        String[] branArray = brands != null ? brands.split("\\s*,\\s*") : new String[] {};

        final SearchRequest searchRequest = queryBuilderService.buildCategoryAggregationQuery(Arrays.asList(supplierArray), Arrays.asList(branArray), apparel);

//        final List<Hit<String>> hits = elasticsearchClient.search(searchRequest, String.class).hits().hits();
//        final List<String> aggregations = this.parseAggregations(hits, "Category");

        SearchResponse<String> searchResponse = elasticsearchClient.search(searchRequest, String.class);
        Aggregate categoryAggregate = searchResponse.aggregations().get("Category");
        final List<String> aggregations = parseAggregationsNew(categoryAggregate, "Category");


        final List<NameObject> nameList = new ArrayList<>();
        for (final String aggregation : aggregations) {
            final NameObject nameObject = new NameObject();
            nameObject.setName(aggregation);
            nameList.add(nameObject);
        }

        final AggregateResponse response = new AggregateResponse();
        response.setData(nameList.stream()
            .sorted(Comparator.comparing(NameObject::getName))
            .collect(Collectors.toList()));

        return response;
    }

    public AggregateResponse getIndexedCategories() throws IOException {

        final SearchRequest searchRequest = queryBuilderService.buildCategoryAggregationQuery();
        SearchResponse<String> searchResponse = elasticsearchClient.search(searchRequest, String.class);

        Aggregate aggregate = searchResponse.aggregations().get("Category");

//        final List<String> aggregations = parseAggregations(hits, "Category");

        final List<String> aggregations = parseAggregationsNew(aggregate, "Category");

        final List<NameObject> nameList = new ArrayList<>();
        for (final String aggregation : aggregations) {
            final NameObject nameObject = new NameObject();
            nameObject.setName(aggregation);
            nameList.add(nameObject);
        }

        final AggregateResponse response = new AggregateResponse();
        response.setData(nameList.stream()
            .sorted(Comparator.comparing(NameObject::getName))
            .collect(Collectors.toList()));

        return response;
    }

    public AggregateResponse getSubCategory(final String supplier, final String brands, final String categories, final Boolean apparel) throws IOException {
        String[] supplierArray = supplier != null ? supplier.split("\\s*,\\s*") : new String[] {};
        String[] branArray = brands != null ? brands.split("\\s*,\\s*") : new String[] {};
        String[] categoryArray = categories != null ? categories.split("\\s*,\\s*") : new String[] {};

        final SearchRequest searchRequest = queryBuilderService.buildSubCategoryAggregationQuery(Arrays.asList(supplierArray), Arrays.asList(branArray), Arrays.asList(categoryArray), apparel);

        SearchResponse<String> searchResponse = elasticsearchClient.search(searchRequest, String.class);
        Aggregate subCategory = searchResponse.aggregations().get("SubCategory");
        List<String> aggregations = parseAggregationsNew(subCategory, "SubCategory");

//        final List<Hit<String>> hits = elasticsearchClient.search(searchRequest, String.class).hits().hits();
//        final List<String> aggregations = parseAggregations(hits, "SubCategory");

        final List<NameObject> nameList = new ArrayList<>();
        for (final String aggregation : aggregations) {
            final NameObject nameObject = new NameObject();
            nameObject.setName(aggregation);
            nameList.add(nameObject);
        }
        final AggregateResponse response = new AggregateResponse();
        response.setData(nameList.stream()
            .sorted(Comparator.comparing(NameObject::getName))
            .collect(Collectors.toList()));
        return response;
    }

    public List<TextObject> getSubCategory(final String categories) throws IOException {

        final SearchRequest searchRequest = queryBuilderService.buildSubCategoryAggregationQuery(categories);

        SearchResponse<String> searchResponse = elasticsearchClient.search(searchRequest, String.class);
        Aggregate subCategory = searchResponse.aggregations().get("SubCategory");

//        final List<Hit<String>> hits = elasticsearchClient.search(searchRequest, String.class).hits().hits();
//        final List<String> aggregations = parseAggregations(hits, "SubCategory");

        List<String> aggregations = parseAggregationsNew(subCategory, "subCategory");

        final List<TextObject> textList = new ArrayList<>();
        for (final String aggregation : aggregations) {
            final TextObject textObject = new TextObject();
            textObject.setText(aggregation);
            textObject.setId(aggregation);
            textList.add(textObject);
        }

        return textList.stream()
            .sorted(Comparator.comparing(TextObject::getText))
            .collect(Collectors.toList());

    }

    public AggregateResponse getSuppliers(final Boolean apparel) throws IOException {
        final SearchRequest searchRequest = queryBuilderService.buildSupplierAggregation(apparel);
        SearchResponse<String> searchResponse = elasticsearchClient.search(searchRequest, String.class);

        Aggregate supplierAggregate = searchResponse.aggregations().get("Supplier");
        List<String> aggregations = parseAggregationsNew(supplierAggregate, "Supplier");

        List<NameObject> nameList = aggregations.stream()
                .map(name -> {
                    NameObject obj = new NameObject();
                    obj.setName(name);
                    return obj;
                })
                .sorted(Comparator.comparing(NameObject::getName))
                .collect(Collectors.toList());

        AggregateResponse response = new AggregateResponse();
        response.setData(nameList);
        return response;
    }

    public AggregateResponse getBrands(String supplierNames, final Boolean apparel) throws IOException {
        String[] supplierArray = supplierNames.split("\\s*,\\s*");

        SearchRequest searchRequest = queryBuilderService.buildBrandAggregation(
                Arrays.asList(supplierArray), apparel);

        SearchResponse<String> searchResponse = elasticsearchClient.search(searchRequest, String.class);

        Aggregate brandAgg = searchResponse.aggregations().get("Brands");
        List<String> aggregations = parseAggregationsNew(brandAgg, "Brands");

        List<NameObject> nameList = aggregations.stream()
                .map(name -> {
                    NameObject obj = new NameObject();
                    obj.setName(name);
                    return obj;
                })
                .sorted(Comparator.comparing(NameObject::getName))
                .collect(Collectors.toList());

       final AggregateResponse response = new AggregateResponse();
        response.setData(nameList);
        return response;
    }

    private List<String> parseAggregations(final List<Hit<String>> hits, final String termKey) {
        if (hits.isEmpty()) {
            return List.of();
        }
        /*var aggContainer = hits.getAggregations();
        if (aggContainer.aggregations() instanceof Map<?, ?> aggMap) {
            Object rawAgg = aggMap.get(termKey);
            if (rawAgg instanceof Terms terms) {
                return terms.getBuckets()
                    .stream()
                    .map(Terms.Bucket::getKeyAsString)
                    .collect(Collectors.toList());
            }
        }*/
        return List.of();
    }

    private List<String> parseAggregationsNew(final Aggregate aggregate, final String termKey) {

        if (aggregate != null && aggregate.isSterms()) {
            StringTermsAggregate terms = aggregate.sterms();
            List<String> buckets = new ArrayList<>();
            for (StringTermsBucket bucket : terms.buckets().array()) {
                if (!StringUtils.isEmpty(bucket.key().toString())){
                    buckets.add(bucket.key().stringValue());
                }
            }
            return buckets;
        } else {
            return Collections.emptyList();
        }
    }
}
