package com.optahub.awi.service.data.repository;

import com.optahub.awi.service.data.entity.PromoStandardsSupplierConfig;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@Repository
public class PromoStandardsConfigRepository {
    public Map<String, PromoStandardsSupplierConfig> configMap = new HashMap<>();

    public void add(final PromoStandardsSupplierConfig config) {
        configMap.put(config.getSupplierCode(), config);
    }

    public Optional<PromoStandardsSupplierConfig> get(final String supplierCode) {
        if(configMap.containsKey(supplierCode)) {
            return Optional.of(configMap.get(supplierCode));
        }

        return Optional.empty();
    }
}
