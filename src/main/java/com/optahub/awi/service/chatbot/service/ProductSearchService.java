package com.optahub.awi.service.chatbot.service;

import java.time.Instant;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import co.elastic.clients.elasticsearch._types.FieldValue;
import co.elastic.clients.elasticsearch._types.query_dsl.Query;
import co.elastic.clients.elasticsearch.core.SearchRequest;
import co.elastic.clients.elasticsearch.core.SearchResponse;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.optahub.awi.service.chatbot.entity.ESSearchResult;
import com.optahub.awi.service.chatbot.mapper.EsProductMapper;
import com.optahub.awi.service.chatbot.model.ChatHistoryModel;
import com.optahub.awi.service.chatbot.model.ChatMeta;
import com.optahub.awi.service.chatbot.model.LLMResponse;
import com.optahub.awi.service.chatbot.model.SearchChatResponse;
import com.optahub.awi.service.chatbot.utils.S3Uploader;
import org.springframework.ai.chat.ChatResponse;
import org.springframework.ai.chat.messages.ChatMessage;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.MessageType;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.embedding.EmbeddingClient;
import org.springframework.ai.openai.OpenAiChatClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.optahub.awi.service.chatbot.entity.ESProduct;

import co.elastic.clients.elasticsearch.ElasticsearchClient;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class ProductSearchService {

    private final EmbeddingClient embeddingClient;
    private final ElasticsearchClient elasticsearchClient;
    private final OpenAiChatClient chatClient;
    private final S3Uploader s3Uploader;

    private final EsProductMapper esProductMapper;
    private final ObjectMapper objectMapper;

    @Value("${knn.min-score:0.35}")
    Double MIN_SCORE;

    @Value("${prompt.title}")
    private String TITLE_PROMPT;

    @Value("${prompt.query-refiner}")
    private String QUERY_REFINER_PROMPT;

    @Value("${prompt.context-summary}")
    private String CONTEXT_SUMMARY_PROMPT;

    @Value("${prompt.rerank-and-generate-json}")
    private String RERANK_AND_GENERATE_JSON_PROMPT;

    @Value("${prompt.system-prompt-introduction}")
    private String SYSTEM_PROMPT_INTRODUCTION;

    public ProductSearchService(EmbeddingClient embeddingClient, ElasticsearchClient elasticsearchClient, OpenAiChatClient chatClient, S3Uploader s3Uploader, EsProductMapper esProductMapper, ObjectMapper objectMapper) {
        this.embeddingClient = embeddingClient;
        this.elasticsearchClient = elasticsearchClient;
        this.chatClient = chatClient;
        this.s3Uploader = s3Uploader;
        this.esProductMapper = esProductMapper;
        this.objectMapper = objectMapper;
    }

    public SearchChatResponse search(final String username,
                                     final String chatId,
                                     String textQuery,
                                     final int resultsExpected,
                                     final int neighbours,
                                     final boolean useContextual,
                                     final Boolean isOptamark,
                                     final Boolean isPreferredVendor,
                                     final Set<String> companyNames) {
        try {
            log.info("Searching for text query: {}", textQuery);

            // Efficient context: fetch only the latest entry for summary
            String contextForQuery = "";
            if (useContextual) {
                ChatHistoryModel latestEntry = s3Uploader.getLatestChatEntry(username, chatId);
                if (latestEntry != null && latestEntry.getContextualQuery() != null) {
                    contextForQuery = latestEntry.getContextualQuery();
                }
            }

            String refinedQuery = "";
            if (useContextual) {
                refinedQuery = chatClient.call(QUERY_REFINER_PROMPT.formatted(contextForQuery, textQuery));

            }
            log.info("Refined query: {}", refinedQuery);
            final List<Double> embeddingVector = embeddingClient.embed(useContextual ? refinedQuery : textQuery);
            log.debug("query embedding: {}", embeddingVector);

            final List<Query> filters = new ArrayList<>();
            if (isOptamark != null) {
                filters.add(Query.of(q -> q.term(t -> t
                        .field("isOptamark")
                        .value(isOptamark)
                )));
            }

            if (companyNames != null && !companyNames.isEmpty()) {
                filters.add(Query.of(q -> q.terms(terms -> terms
                        .field("companyName")
                        .terms(t -> t.value(companyNames.stream()
                                .map(FieldValue::of)
                                .toList()))
                )));
            }

            final SearchRequest searchRequest = SearchRequest.of(s -> s
                .index(ESProduct.ES_PRODUCTS)
                .knn(knn -> knn
                    .field("embedding")
                    .numCandidates((long) neighbours)
                    .queryVector(embeddingVector.stream().map(Double::floatValue).toList())
                    .k((long) resultsExpected)
                    .filter(filters)
                )
                .minScore(MIN_SCORE)
                .size(resultsExpected)
                .source(src -> src.filter(f -> f.excludes("embedding")))
            );

            final SearchResponse<ESProduct> searchResponse = elasticsearchClient.search(searchRequest, ESProduct.class);

            final List<ESSearchResult> results = searchResponse.hits().hits().stream()
                .map(hit -> {
                    final ESSearchResult searchResult = esProductMapper.toSearchResult(hit.source());
                    searchResult.setScore(hit.score());
                    if (!searchResult.getIsOptamark()) {
                        searchResult.setUrl("https://app.optamarkgraphics.com/2/promotional/product-details?ProductID=" + searchResult.getProductID());
                    }
                    return searchResult;
                })
                .collect(Collectors.toList());

            final LLMResponse rerankResult = rerankAndGenerateUsingLLM(results, textQuery, contextForQuery);
            List<ESSearchResult> finalResults = rerankFinalProducts(rerankResult, results);

            final String assistantReply = rerankResult.getAssistant_reply() != null ? rerankResult.getAssistant_reply() : "No relevant options found.";

            final String newContextualSummary = summarizeContext(contextForQuery, textQuery, refinedQuery, assistantReply, finalResults);
            log.info("Contextual summary: {}", newContextualSummary);

            final Instant now = Instant.now();
            final String iso = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH-mm-ss_SSS'Z'")
                    .withZone(java.time.ZoneOffset.UTC).format(now);

            final String key = String.format("chat-history/%s/%s/%s.json",
                    username,
                    chatId,
                    iso);

            final ChatHistoryModel entry = ChatHistoryModel.builder()
                    .username(username)
                    .chatId(chatId)
                    .timestamp(now)
                    .userQuery(textQuery)
                    .contextualQuery(newContextualSummary)
                    .assistantResponse(assistantReply)
                    .searchResults(finalResults)
                    .build();

            s3Uploader.uploadHistory(entry, key);
            log.info("Uploaded chat history to S3: {}", key);

            return SearchChatResponse.builder()
                    .assistantReply(assistantReply)
                    .searchResults(finalResults)
                    .build();
        } catch (Exception e) {
            log.error("exception occurred while searching for text query: {}", textQuery, e);
            throw new RuntimeException("Failed to search for text query", e);
        }
    }

    private LLMResponse rerankAndGenerateUsingLLM(List<ESSearchResult> results, String textQuery, String contextForQuery) {

        StringBuilder sb = new StringBuilder();
        for (final ESSearchResult r : results) {
            sb.append(String.format(
                    "- ID: %s | Name: %s | Score: %.2f | Colors: %s | MinPrice: $%s | Desc: %s\n",
                    r.getProductID(), r.getPrName(), r.getScore(), r.getColors(), r.getMinPrc(), r.getDescription()));
        }

        // Include previous context if available
        String contextPrefix = "";
        if (contextForQuery != null && !contextForQuery.isBlank()) {
            contextPrefix = "Previous Conversation Summary:\n" + contextForQuery.strip() + "\n\n";
        }

        final String prompt = contextPrefix + RERANK_AND_GENERATE_JSON_PROMPT.formatted(textQuery, sb.toString());

        log.debug("Calling LLM for rerank prompt. Input truncated:\n{}", prompt.substring(0, Math.min(1500, prompt.length())) + "...");

        String rawJson;
        try {
            rawJson = chatClient.call(prompt).trim();
            log.debug("LLM raw response: {}", rawJson.length() > 1000 ? rawJson.substring(0, 1000) + "..." : rawJson);
        } catch (Exception e) {
            log.error("LLM call failed", e);
            return fallbackLLMResponse(results);
        }

        try {
            LLMResponse response = objectMapper.readValue(rawJson, LLMResponse.class);

            if (response.getRanked_ids() == null || response.getAssistant_reply() == null) {
                throw new IllegalArgumentException("Missing fields in LLM response");
            }

            return response;
        } catch (Exception e) {
            log.warn("LLM JSON parsing failed. Falling back to ES order. Error: {}", e.getMessage());
            return fallbackLLMResponse(results);
        }
    }

    private LLMResponse fallbackLLMResponse(List<ESSearchResult> results) {
        List<String> fallbackIds = results.stream()
                .map(ESSearchResult::getProductID)
                .toList();
        String reply = "Here are some options based on your request.";
        return new LLMResponse(fallbackIds, reply);
    }


    private List<ESSearchResult> rerankFinalProducts(LLMResponse rerankResult, List<ESSearchResult> originalResults) {
        Map<String, ESSearchResult> resultMap = originalResults.stream()
                .collect(Collectors.toMap(ESSearchResult::getProductID, Function.identity()));

        Set<String> validIds = resultMap.keySet();

        List<ESSearchResult> finalList = rerankResult.getRanked_ids().stream()
                .filter(validIds::contains)
                .map(resultMap::get)
                .collect(Collectors.toList());

        if (finalList.size() != rerankResult.getRanked_ids().size()) {
            log.warn("LLM returned unknown product IDs: {}", rerankResult.getRanked_ids().stream()
                    .filter(id -> !validIds.contains(id)).toList());
        }

        return finalList;
    }

    // initializes a new chat with metadata based on the query.
    public ChatMeta initializeChat(final String username, final String chatId, final String query) {
        final String title = chatClient.call(TITLE_PROMPT.formatted(query));
        final ChatMeta meta = ChatMeta.builder()
                .chatId(chatId)
                .title(title)
                .createdAt(Instant.now())
                .updatedAt(Instant.now())
                .build();
        s3Uploader.saveMeta(username, chatId, meta);
        return meta;
    }

    private String summarizeContext(String previousSummary, String userQuery, String refinedQuery, String assistantReply, List<ESSearchResult> searchResults) {

        final String prompt = CONTEXT_SUMMARY_PROMPT.formatted(
                previousSummary != null ? previousSummary.strip() : "",
                userQuery.strip(),
                refinedQuery.strip(),
                assistantReply.strip(),
                searchResults.stream()
                        .map(ESSearchResult::toString)
                        .collect(Collectors.joining(", "))
            );

        final String newLine = chatClient.call(prompt);

        // Append the generated new line to existing context
        return (previousSummary != null && !previousSummary.isBlank())
                ? previousSummary.strip() + "\n" + newLine.strip()
                : newLine.strip();
    }

    public SearchChatResponse searchBySpc(final String username,
                                          final String chatId,
                                          String spc) {
        try {
            log.info("Searching by SPC: {}", spc);


            List<ESSearchResult> results = searchBySpcCode(spc);
            log.info("Found {} results for SPC: {}", results.size(), spc);

            final List<Message> messages = new ArrayList<>();
            messages.add(new ChatMessage(MessageType.SYSTEM, SYSTEM_PROMPT_INTRODUCTION));
            messages.add(new ChatMessage(MessageType.SYSTEM, "User searched for product with SPC code: " + spc));

            if (results.isEmpty()) {
                messages.add(new ChatMessage(MessageType.SYSTEM,
                        "No products were found with SPC code: " + spc + ". Explain this to the user and suggest they verify the SPC code."));
            } else {
                results.forEach(r ->
                        messages.add(new ChatMessage(MessageType.SYSTEM, "Product: " + r.getPrName() +
                                " – " + r.getDescription() + " (Price: $" + r.getMinNet() + ", SPC: " + r.getSpc() + ")"))
                );
            }

            messages.add(new ChatMessage(MessageType.USER, "Find product with SPC: " + spc));
            messages.add(new ChatMessage(MessageType.SYSTEM,
                    "Generate a response about the product(s) found with the given SPC code. If no products were found, explain this clearly."));

            final ChatResponse chatResponse = chatClient.call(new Prompt(messages));
            final String assistantReply = chatResponse.getResult().getOutput().getContent();

            final Instant now = Instant.now();
            final String iso = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH-mm-ss_SSS'Z'")
                    .withZone(java.time.ZoneOffset.UTC).format(now);

            final String key = String.format("chat-history/%s/%s/%s.json", username, chatId, iso);

            final ChatHistoryModel entry = ChatHistoryModel.builder()
                    .username(username)
                    .chatId(chatId)
                    .timestamp(now)
                    .userQuery("SPC search: " + spc)
                    .assistantResponse(assistantReply)
                    .searchResults(results)
                    .build();

            s3Uploader.uploadHistory(entry, key);
            log.info("Uploaded chat history to S3: {}", key);

            return SearchChatResponse.builder()
                    .assistantReply(assistantReply)
                    .searchResults(results)
                    .build();
        } catch (Exception e) {
            log.error("Exception occurred while searching for SPC: {}", spc, e);
            return null;
        }
    }

    private List<ESSearchResult> searchBySpcCode(String spc) {
        try {
            final List<Query> filters = new ArrayList<>();

            String[] spcCodes = spc.split(",");
            if (spcCodes.length == 1) {
                filters.add(Query.of(q -> q.term(t -> t
                        .field("spc")
                        .value(spc.trim())
                )));
            } else {
                List<FieldValue> spcValues = new ArrayList<>();
                for (String spcCode : spcCodes) {
                    spcValues.add(FieldValue.of(spcCode.trim()));
                }

                filters.add(Query.of(q -> q.terms(terms -> terms
                        .field("spc")
                        .terms(t -> t.value(spcValues))
                )));
            }

            final SearchRequest searchRequest = SearchRequest.of(s -> s
                    .index(ESProduct.ES_PRODUCTS)
                    .query(q -> q.bool(b -> b.filter(filters)))
                    .size(spcCodes.length * 10) // Increase size to accommodate multiple results
                    .source(src -> src.filter(f -> f.excludes("embedding")))
            );

            log.debug("Executing SPC search with {} filters for {} SPC codes", filters.size(), spcCodes.length);
            final SearchResponse<ESProduct> searchResponse = elasticsearchClient.search(searchRequest, ESProduct.class);
            log.info("Elasticsearch returned {} hits for SPC(s): {}", searchResponse.hits().hits().size(), spc);

            return searchResponse.hits().hits().stream()
                    .map(hit -> {
                        final ESSearchResult searchResult = esProductMapper.toSearchResult(hit.source());
                        searchResult.setScore(hit.score());

                        if (!searchResult.getIsOptamark()) {
                            searchResult.setUrl("https://app.optamarkgraphics.com/2/promotional/product-details?ProductID=" + searchResult.getProductID());
                        } else {
                            searchResult.setUrl(searchResult.getUrl());
                        }
                        return searchResult;
                    })
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Error executing SPC search", e);
            return new ArrayList<>();
        }
    }

    public SearchChatResponse searchByItemNum(final String username,
                                              final String chatId,
                                              String itemNum) {
        try {
            log.info("Searching by itemNum: {}", itemNum);

            List<ESSearchResult> results = searchByItemNum(itemNum);
            log.info("Found {} results for itemNum: {}", results.size(), itemNum);

            final List<Message> messages = new ArrayList<>();
            messages.add(new ChatMessage(MessageType.SYSTEM, SYSTEM_PROMPT_INTRODUCTION));
            messages.add(new ChatMessage(MessageType.SYSTEM, "User searched for product with item code: " + itemNum));

            if (results.isEmpty()) {
                messages.add(new ChatMessage(MessageType.SYSTEM,
                        "No products were found with item code: " + itemNum + ". Explain this to the user and suggest they verify the item code."));
            } else {
                results.forEach(r ->
                        messages.add(new ChatMessage(MessageType.SYSTEM, "Product: " + r.getPrName() +
                                " – " + r.getDescription() + " (Price: $" + r.getMinNet() + ", ItemNum: " + r.getItemNum() + ")"))
                );
            }

            messages.add(new ChatMessage(MessageType.USER, "Find product with item code: " + itemNum));
            messages.add(new ChatMessage(MessageType.SYSTEM,
                    "Generate a response about the product(s) found with the given itemNum. If no products were found, explain this clearly."));

            final ChatResponse chatResponse = chatClient.call(new Prompt(messages));
            final String assistantReply = chatResponse.getResult().getOutput().getContent();

            final Instant now = Instant.now();
            final String iso = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH-mm-ss_SSS'Z'")
                    .withZone(java.time.ZoneOffset.UTC).format(now);

            final String key = String.format("chat-history/%s/%s/%s.json", username, chatId, iso);

            final ChatHistoryModel entry = ChatHistoryModel.builder()
                    .username(username)
                    .chatId(chatId)
                    .timestamp(now)
                    .userQuery("Item code search: " + itemNum)
                    .assistantResponse(assistantReply)
                    .searchResults(results)
                    .build();

            s3Uploader.uploadHistory(entry, key);
            log.info("Uploaded chat history to S3: {}", key);

            return SearchChatResponse.builder()
                    .assistantReply(assistantReply)
                    .searchResults(results)
                    .build();
        } catch (Exception e) {
            log.error("Exception occurred while searching for itemNum: {}", itemNum, e);
            return null;
        }
    }

    private List<ESSearchResult> searchByItemNum(String itemNum) {
        try {
            final List<Query> filters = new ArrayList<>();

            String[] itemNumCodes = itemNum.split(",");
            if (itemNumCodes.length == 1) {
                filters.add(Query.of(q -> q.term(t -> t
                        .field("itemNum")
                        .value(itemNum.trim())
                )));
            } else {
                List<FieldValue> itemNumValues = new ArrayList<>();
                for (String itemNumCode : itemNumCodes) {
                    itemNumValues.add(FieldValue.of(itemNumCode.trim()));
                }

                filters.add(Query.of(q -> q.terms(terms -> terms
                        .field("itemNum")
                        .terms(t -> t.value(itemNumValues))
                )));
            }

            final SearchRequest searchRequest = SearchRequest.of(s -> s
                    .index(ESProduct.ES_PRODUCTS)
                    .query(q -> q.bool(b -> b.filter(filters)))
                    .size(itemNumCodes.length * 10)
                    .source(src -> src.filter(f -> f.excludes("embedding")))
            );

            log.debug("Executing itemNum search with {} filters for {} itemNum codes", filters.size(), itemNumCodes.length);
            final SearchResponse<ESProduct> searchResponse = elasticsearchClient.search(searchRequest, ESProduct.class);
            log.info("Elasticsearch returned {} hits for itemNum(s): {}", searchResponse.hits().hits().size(), itemNum);

            return searchResponse.hits().hits().stream()
                    .map(hit -> {
                        final ESSearchResult searchResult = esProductMapper.toSearchResult(hit.source());
                        searchResult.setScore(hit.score());

                        if (!searchResult.getIsOptamark()) {
                            searchResult.setUrl("https://app.optamarkgraphics.com/2/promotional/product-details?ProductID=" + searchResult.getProductID());
                        } else {
                            searchResult.setUrl(searchResult.getUrl());
                        }
                        return searchResult;
                    })
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Error executing itemNum search", e);
            return new ArrayList<>();
        }
    }
}
