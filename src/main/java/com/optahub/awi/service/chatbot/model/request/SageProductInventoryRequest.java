package com.optahub.awi.service.chatbot.model.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@NoArgsConstructor
@ToString
@JsonFormat
public class SageProductInventoryRequest {

    @JsonProperty("productId")
    private String productId;
    private String productIdFull;

    public SageProductInventoryRequest(final String productId) {
        this.productId = productId.substring(2);
        this.productIdFull = productId;
    }
}
