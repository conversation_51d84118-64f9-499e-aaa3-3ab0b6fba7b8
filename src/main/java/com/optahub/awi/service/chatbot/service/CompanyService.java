package com.optahub.awi.service.chatbot.service;

import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._types.aggregations.Aggregation;
import co.elastic.clients.elasticsearch._types.aggregations.StringTermsAggregate;
import co.elastic.clients.elasticsearch._types.aggregations.StringTermsBucket;
import co.elastic.clients.elasticsearch._types.aggregations.TermsAggregation;
import co.elastic.clients.elasticsearch.core.SearchRequest;
import co.elastic.clients.elasticsearch.core.SearchResponse;
import com.optahub.awi.service.chatbot.entity.ESProduct;
import com.optahub.awi.service.chatbot.model.response.CompanyResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Service
@Slf4j
public class CompanyService {

    private final ElasticsearchClient elasticsearchClient;

    @Autowired
    public CompanyService(ElasticsearchClient elasticsearchClient) {
        this.elasticsearchClient = elasticsearchClient;
    }

    @Cacheable(value = "companyNames", unless = "#result.isEmpty()")
    public List<CompanyResponse> getUniqueCompanyNames() {
        try {
            SearchRequest searchRequest = SearchRequest.of(s -> s
                    .index(ESProduct.ES_PRODUCTS)
                    .size(0)
                    .aggregations("unique_companyName", Aggregation.of(a -> a
                            .terms(TermsAggregation.of(t -> t
                                    .field("companyName")
                                    .size(Integer.MAX_VALUE)
                            ))
                    ))
            );

            SearchResponse<Void> response = elasticsearchClient.search(searchRequest, Void.class);

            return parseCompanyNamesAggregation(response);

        } catch (IOException e) {
            log.error("Unexpected error while fetching unique company names: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to fetch unique company names", e);
        }
    }

    @CacheEvict(value = "companyNames", allEntries = true)
    public void clearCompanyNamesCache() {
        log.info("Clearing company names cache");
    }

    private List<CompanyResponse> parseCompanyNamesAggregation(SearchResponse<Void> response) {
        if (response.aggregations() != null && response.aggregations().containsKey("unique_companyName")) {
            var aggregate = response.aggregations().get("unique_companyName");

            if (aggregate.isSterms()) {
                StringTermsAggregate terms = aggregate.sterms();
                List<CompanyResponse> companyResponses = new ArrayList<>();

                for (StringTermsBucket bucket : terms.buckets().array()) {
                    String companyName = bucket.key().stringValue();
                    if (!StringUtils.isEmpty(companyName)) {
                        CompanyResponse companyResponse = new CompanyResponse();
                        companyResponse.setName(companyName);
                        companyResponses.add(companyResponse);
                    }
                }
                return companyResponses;
            }
        }
        return Collections.emptyList();
    }
}