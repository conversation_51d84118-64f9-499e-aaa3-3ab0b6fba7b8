package com.optahub.awi.service.chatbot.entity;

import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Document(indexName = "sage_product_category")
@JsonIgnoreProperties(ignoreUnknown = true)
@ToString
public class CategoryIndexingLog {

    @Id
    @Field(type = FieldType.Keyword)
    private String name;

    @Field(type = FieldType.Long)
    private Long totalRecord;

    @Field(type = FieldType.Integer)
    private Integer inventoryCount;

    @Field(type = FieldType.Long)
    private Long timeTaken;

    @Field(type = FieldType.Text)
    private String status;

    @Field(type = FieldType.Text)
    private String inventoryStatus;

}
