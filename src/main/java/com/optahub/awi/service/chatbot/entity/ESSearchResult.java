package com.optahub.awi.service.chatbot.entity;

import java.util.List;

import org.springframework.data.annotation.Id;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@Builder
public class ESSearchResult {

    // id, url, spc, prName, company, itemNum ,desc, min-max price, pic url

    private String productID;

    private String url;

    private Double score;

    private String spc;

    private String prName;

    private String itemNum;

    private List<Sku> skus;

    private String description;

    private String colors;

    private String companyName;

    private String minPrc;

    private String maxPrc;

    private String minNet;

    private String maxNet;

    private String thumbPicLink;

    private Boolean isOptamark;

    private String minimumQty;

    private String unitPrice;

    private String totalPrice;

    @Override
    public String toString() {
        return "Product: {" +
                "productID='" + productID + '\'' +
                ", spc='" + spc + '\'' +
                ", productName='" + prName + '\'' +
                ", itemNum='" + itemNum + '\'' +
                ", skus=" + skus +
                ", description='" + description + '\'' +
                ", colors='" + colors + '\'' +
                ", companyName='" + companyName + '\'' +
                ", minPrice='" + minPrc + '\'' +
                ", isProductFromOptamark=" + isOptamark +
                ", minimumQty='" + minimumQty + '\'' +
                ", unitPrice='" + unitPrice + '\'' +
                '}';
    }
}
