package com.optahub.awi.service.chatbot.controller;

import com.optahub.awi.service.chatbot.model.ChatHistoryModel;
import com.optahub.awi.service.chatbot.model.ChatMeta;
import com.optahub.awi.service.chatbot.service.ChatHistoryService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/history")
@RequiredArgsConstructor
public class ChatHistoryController {

        @Autowired
        ChatHistoryService chatHistoryService;

        @GetMapping("/list")
        public ResponseEntity<List<ChatMeta>> listChats(
                @RequestHeader("X-Username") String username) {

            return ResponseEntity.ok(chatHistoryService.listChats(username));
        }

        @GetMapping("/messages")
        public ResponseEntity<List<ChatHistoryModel>> getMessages(
                @RequestHeader("X-Username") String username,
                @RequestHeader("chatId") String chatId) {

            return ResponseEntity.ok(chatHistoryService.getFullChat(username, chatId));
        }

        @DeleteMapping("/delete")
        public ResponseEntity<Void> deleteChat(
                @RequestHeader("X-Username") String username,
                @RequestHeader("chatId") String chatId) {

            chatHistoryService.deleteChat(username, chatId);
            return ResponseEntity.noContent().build();

        }

        @PutMapping("/update-title")
        public ResponseEntity<?> updateChatTitle(
                @RequestHeader("X-Username") String username,
                @RequestHeader("chatId") String chatId,
                @RequestParam("title") String title) {

            try {
                chatHistoryService.updateChatTitle(username, chatId, title);
                return ResponseEntity.noContent().build();
            } catch (IllegalArgumentException e) {
                return ResponseEntity.badRequest().body(e.getMessage());
            } catch (Exception e) {
                return ResponseEntity.status(500).body("Failed to update chat title: " + e.getMessage());
            }
        }


}
