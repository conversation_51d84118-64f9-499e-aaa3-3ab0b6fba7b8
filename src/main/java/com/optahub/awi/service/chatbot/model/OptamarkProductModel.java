package com.optahub.awi.service.chatbot.model;

import com.opencsv.bean.CsvBindByName;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class OptamarkProductModel {

    @CsvBindByName(column = "products_id")
    private String productsId;

    @CsvBindByName(column = "products_title")
    private String productsTitle;

    @CsvBindByName(column = "category_name")
    private String categoryName;

    @CsvBindByName(column = "category_url")
    private String categoryUrl;

    @CsvBindByName(column = "category_group")
    private String categoryGroup;

    @CsvBindByName(column = "badge")
    private String badge;

    @CsvBindByName(column = "product_url")
    private String productUrl;

    @CsvBindByName(column = "products_sku")
    private String productsSku;

    @CsvBindByName(column = "color")
    private String color;

    @CsvBindByName(column = "minimum_qty")
    private String minimumQty;

    @CsvBindByName(column = "unit_price")
    private String unitPrice;

    @CsvBindByName(column = "total_price")
    private String totalPrice;

    @CsvBindByName(column = "image")
    private String image;

    @CsvBindByName(column = "description_encoded")
    private String descriptionEncoded;

    @CsvBindByName(column = "description_decoded")
    private String descriptionDecoded;

}