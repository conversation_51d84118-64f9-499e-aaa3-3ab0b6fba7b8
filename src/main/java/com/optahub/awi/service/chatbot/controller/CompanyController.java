package com.optahub.awi.service.chatbot.controller;

import com.optahub.awi.service.chatbot.model.response.CompanyResponse;
import com.optahub.awi.service.chatbot.service.CompanyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/company")
public class CompanyController {

    private final CompanyService companyService;

    public CompanyController(CompanyService companyService) {
        this.companyService = companyService;
    }

    @GetMapping("/names")
    public ResponseEntity<List<CompanyResponse>> getAll() {
        List<CompanyResponse> companyResponses = companyService.getUniqueCompanyNames();
        return ResponseEntity.ok(companyResponses);
    }

    @PostMapping("/cache/clear")
    public ResponseEntity<String> clearCache() {
        companyService.clearCompanyNamesCache();
        return ResponseEntity.ok("Company names cache cleared successfully");
    }
}