package com.optahub.awi.service.chatbot.entity;

import java.util.Date;
import java.util.List;

import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@Document(indexName = "indexing_log")
@JsonIgnoreProperties(ignoreUnknown = true)
@ToString
public class IndexingLog {

    @Id
    @Field(name = "date", type = FieldType.Date)
    private Date date;

    @Field(type = FieldType.Text)
    private String status;

    @Field(type = FieldType.Text)
    private Long totalTimeTaken;

    @Field(type = FieldType.Text)
    private Long totalRecords;

    @Field(type = FieldType.Nested)
    @ToString.Exclude
    private List<CategoryIndexingLog> categories;

    private List<String> failedCategories;

    public IndexingLog(Date date) {
        this.date = date;
    }
}
