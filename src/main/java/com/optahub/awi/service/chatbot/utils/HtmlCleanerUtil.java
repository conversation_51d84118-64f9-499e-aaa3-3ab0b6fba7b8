package com.optahub.awi.service.chatbot.utils;

import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;

public class HtmlCleanerUtil {

    public static String cleanHtml(String input) {
        if (input == null || input.isEmpty()) {
            return "";
        }

        input = input.replaceAll("&nbsp;", " ")
                .replaceAll("&#39;", "'")
                .replaceAll("&rdquo;", "\"")
                .replaceAll("&ldquo;", "\"")
                .replaceAll("&ndash;", "-")
                .replaceAll("&mdash;", "--");

        Document doc = Jsoup.parse(input);
        String text = doc.text();

        text = text.replaceAll("\\s+", " ").trim();

        return text;
    }

}