package com.optahub.awi.service.chatbot.entity;

import java.util.Date;
import java.util.List;

import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@Document(indexName = "embedding_generation_log")
@JsonIgnoreProperties(ignoreUnknown = true)
@ToString
public class EmbeddingLog {

    @Id
    @Field(name = "date", type = FieldType.Date)
    private Date date;

    @Field(type = FieldType.Text)
    private String status;

    @Field(type = FieldType.Text)
    private Long totalTimeTaken;

    @Field(type = FieldType.Text)
    private Integer totalProductIds;

    @Field(type = FieldType.Text)
    private Long totalUpdatedRecords;

    @Field(type = FieldType.Text)
    private Long totalFailedRecords;

    private List<String> failedProductIds;

    public EmbeddingLog(Date date) {
        this.date = date;
    }
}
