package com.optahub.awi.service.chatbot.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Set;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.optahub.awi.service.chatbot.service.IndexSageProductService;
import org.springframework.web.multipart.MultipartFile;

@RestController
public class IndexSageProductController {

    private final IndexSageProductService service;

    public IndexSageProductController(IndexSageProductService service) {
        this.service = service;
    }

    @PostMapping("/sage/products/index")
    public ResponseEntity<?> indexSageProducts(@RequestParam(required = false) Set<String> includeCategories, @RequestParam Integer maxCategories) {

        return ResponseEntity.of(service.indexSageProducts(includeCategories, maxCategories));
    }

    @PostMapping("/optamark/products/index")
    public ResponseEntity<?> indexOptamarkProducts(){
        service.indexOptamarkProducts();

        return ResponseEntity.ok("Success");
    }

    @PostMapping(value="/upload/optamark/products", consumes = "multipart/form-data")
    public ResponseEntity<?> uploadOptamarkProducts(@RequestParam("file") MultipartFile productCSV){
        service.uploadOptamarkProducts(productCSV);

        return ResponseEntity.ok("Success");
    }

    @PostMapping("/generate/embedding")
    public ResponseEntity<?> generateEmbedding(@RequestParam Integer maxRecords, @RequestParam Integer poolSize){
        service.generateEmbedding(maxRecords, poolSize);

        return ResponseEntity.ok("Success");
    }

    @PostMapping("/generate/embeddingByProductId")
    public ResponseEntity<?> generateEmbeddingByProductId(@RequestParam String productIds, @RequestParam Integer poolSize){
        service.generateEmbeddingByProductId(Arrays.stream(productIds.split(",")).map(String::trim).toList(), poolSize);
        return ResponseEntity.ok("Success");
    }

}
