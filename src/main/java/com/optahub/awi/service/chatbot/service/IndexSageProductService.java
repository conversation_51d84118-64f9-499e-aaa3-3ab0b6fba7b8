package com.optahub.awi.service.chatbot.service;

import static com.optahub.awi.service.chatbot.utils.TextSanitizer.*;
import static java.lang.String.*;
import static java.lang.String.format;
import static java.util.Optional.*;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.Reader;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

import javax.annotation.Nullable;

import com.opencsv.bean.CsvToBean;
import com.opencsv.bean.CsvToBeanBuilder;
import com.optahub.awi.service.chatbot.entity.EmbeddingGenerationLog;
import com.optahub.awi.service.chatbot.entity.EmbeddingLog;
import com.optahub.awi.service.chatbot.model.OptamarkProductModel;
import com.optahub.awi.service.chatbot.repository.EmbeddingLogRepository;
import com.optahub.awi.service.chatbot.utils.HtmlCleanerUtil;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.client.RequestOptions;
import org.json.JSONObject;
import org.springframework.ai.embedding.EmbeddingClient;
import org.springframework.beans.factory.annotation.Value;

import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.ai.document.Document;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.optahub.awi.service.chatbot.entity.CategoryIndexingLog;
import com.optahub.awi.service.chatbot.entity.IndexingLog;
import com.optahub.awi.service.chatbot.entity.ESProduct;
import com.optahub.awi.service.chatbot.model.request.SageProductInventoryRequest;
import com.optahub.awi.service.chatbot.model.response.ProductInventoryResponse;
import com.optahub.awi.service.chatbot.repository.IndexingLogRepository;
import com.optahub.awi.service.chatbot.repository.SageProductRepository;
import com.optahub.awi.service.chatbot.utils.TextSanitizer;
import com.optahub.awi.service.rest.data.aggregate.NameObject;
import com.optahub.awi.service.rest.data.sage.search.SageSearchRequest;
import com.optahub.awi.service.rest.data.search.SearchResponse;
import com.optahub.awi.service.rest.data.search.SearchResponseItem;
import com.optahub.awi.service.rest.service.SageAggregateService;
import com.optahub.awi.service.sage.data.constants.SageListType;
import com.optahub.awi.service.sage.service.SageConnectService;
import com.optahub.awi.service.sage.service.SageSearchService;

import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch.core.ScrollResponse;
import co.elastic.clients.elasticsearch.core.UpdateRequest;
import co.elastic.clients.elasticsearch.core.search.Hit;
import co.elastic.clients.json.JsonData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

@Service
@Slf4j
public class IndexSageProductService {

    @Value("${sage.max.record}")
    int maxSageRecord;

    @Value("${sage.max.request.attempt}")
    int maxRequestAttempt;

    @Value("${optamark.product.excel.api}")
    String optamarkProductExcelApi;

    final int batchSize = 5000;

    private final SageConnectService connectService;
    private final SageAggregateService sageAggregateService;
    private final SageSearchService sageSearchService;
    private final SageProductRepository sageProductRepository;
    private final IndexingLogRepository indexingLogRepository;
    private final ObjectMapper objectMapper;
    private final ElasticsearchClient elasticsearchClient;
    private final RestTemplate restTemplate;
    private final EmbeddingClient embeddingClient;
    private final EmbeddingLogRepository embeddingLogRepository;
    private static final ExecutorService executor = Executors.newFixedThreadPool(Runtime.getRuntime()
        .availableProcessors() + 1);

    public IndexSageProductService(EmbeddingClient embeddingClient, SageConnectService connectService, SageAggregateService sageAggregateService, SageSearchService sageSearchService,
                                   SageProductRepository sageProductRepository, IndexingLogRepository indexingLogRepository, ObjectMapper objectMapper,
                                    ElasticsearchClient elasticsearchClient, RestTemplate restTemplate, EmbeddingLogRepository embeddingLogRepository) {
        this.embeddingClient = embeddingClient;
        this.connectService = connectService;
        this.sageAggregateService = sageAggregateService;
        this.sageSearchService = sageSearchService;
        this.sageProductRepository = sageProductRepository;
        this.indexingLogRepository = indexingLogRepository;
        this.objectMapper = objectMapper;
        this.elasticsearchClient = elasticsearchClient;
        this.restTemplate = restTemplate;
        this.embeddingLogRepository = embeddingLogRepository;
    }

    public Optional<IndexingLog> indexSageProducts(final Set<String> includeCategories, final Integer maxCategories) {
        var categoriesStream = sageAggregateService.getAggregate(SageListType.CATEGORIES)
            .getData()
            .stream()
            .map(NameObject::getName);

        if (null != includeCategories && !includeCategories.isEmpty()) {
            categoriesStream = categoriesStream.filter(includeCategories::contains);
        }

        if (null != maxCategories && maxCategories > 0) {
            categoriesStream = categoriesStream.limit(maxCategories);
        }

        final List<String> categories = categoriesStream.collect(Collectors.toList());
        log.info(format("Indexing for total categories: %s", categories.size()));
        log.info(format("Categories: %s", categories));

        final IndexingLog indexingLog = new IndexingLog(new Date());
        indexingLog.setFailedCategories(new ArrayList<>());
        indexingLog.setCategories(new ArrayList<>());

        final AtomicLong timeTaken = new AtomicLong();
        final AtomicLong totalRecords = new AtomicLong();

        final List<Callable<CategoryIndexingLog>> callables = categories.stream()
            .map(this::fetchProductsForCategory)
            .collect(Collectors.toList());

        List<Future<CategoryIndexingLog>> futureList = new ArrayList<>();

        try {
            futureList = executor.invokeAll(callables);
        } catch (InterruptedException e) {
            log.error("Interruption occurred", e);
        }

        futureList.stream()
            .map(this::getFutureResult)
            .forEach(categoryIndexingLog -> {
                indexingLog.getCategories().add(categoryIndexingLog);
                timeTaken.addAndGet(ofNullable(categoryIndexingLog.getTimeTaken()).orElse(0L));
                totalRecords.addAndGet(ofNullable(categoryIndexingLog.getTotalRecord()).orElse(0L));
                if(ERROR.equals(categoryIndexingLog.getStatus())){
                    indexingLog.getFailedCategories().add(categoryIndexingLog.getName());
                }
            });

        indexingLog.setStatus(SUCCESS);
        indexingLog.setTotalTimeTaken((timeTaken.get() / MINUTES) > 1 ? (timeTaken.get() / MINUTES) : 1);
        indexingLog.setTotalRecords(totalRecords.get());

        indexingLogRepository.save(indexingLog);
        log.info(format("Indexing completed with status: %s, total products indexed: %s, time take in minutes: %s", indexingLog.getStatus(),
            indexingLog.getTotalRecords(), indexingLog.getTotalTimeTaken()));
        log.debug(format("Indexing completed with indexing status: %s, total products indexed: %s, time take in minutes: %s, and category logs: %s", indexingLog,
            indexingLog.getTotalRecords(), indexingLog.getTotalTimeTaken(), indexingLog.getCategories()));
        return Optional.of(indexingLog);
    }

    public Optional<ProductInventoryResponse> getProductInventory(final List<SageProductInventoryRequest> sageProductInventoryRequest, final String category) {
        final JSONObject productInventoryRequestJson = new JSONObject();
        productInventoryRequestJson.put("products", sageProductInventoryRequest);
        try {
            final Optional<String> sageResponse = connectService.doPost(productInventoryRequestJson, 107);
            var replacedString = sageResponse.orElse("{}");
            //Files.writeString(Path.of("/Users/<USER>/Docs/EarBuds.json"), replacedString);
            replacedString = replacedString.replaceAll(DOUBLE_COMMA, SINGLE_COMMA)
                    .replaceAll(MALFORMED_MEMO, CORRECT_MEMO)
                    .replaceAll(MALFORMED_MEMO_1, CORRECT_MEMO)
                    .replaceAll(MALFORMED_MEMO_2, CORRECT_MEMO);

            return Optional.of(objectMapper.readValue(replacedString, ProductInventoryResponse.class));
        } catch (Exception e) {
            log.error(String.format("Exception occurred while parsing inventory response for category: %s and product ids: %s and response: %s", category,
                    sageProductInventoryRequest.stream()
                            .map(SageProductInventoryRequest::getProductId)
                            .collect(Collectors.joining(SINGLE_COMMA)), productInventoryRequestJson), e);
        }
        return Optional.empty();
    }

    private CategoryIndexingLog getFutureResult(final Future<CategoryIndexingLog> future) {
        CategoryIndexingLog cr = new CategoryIndexingLog();
        cr.setStatus(UNKNOWN);
        try {
            cr = future.get();
        } catch (InterruptedException e) {
            cr.setStatus(ERROR);
            log.error(format("Thread was interrupted while waiting for task completion for category: %s", future), e);
        } catch (ExecutionException e) {
            cr.setStatus(ERROR);
            log.error(format("Exception occurred during task execution for category: %s", future), e);
        }
        return cr;
    }

    private EmbeddingGenerationLog getFutureEmbeddingResult(final Future<EmbeddingGenerationLog> future) {
        EmbeddingGenerationLog cr = new EmbeddingGenerationLog();
        cr.setStatus(UNKNOWN);
        try {
            cr = future.get();
        } catch (InterruptedException e) {
            cr.setStatus(ERROR);
            log.error(format("Thread was interrupted while waiting for task completion for productId: %s", future), e);
        } catch (ExecutionException e) {
            cr.setStatus(ERROR);
            log.error(format("Exception occurred during task execution for productId: %s", future), e);
        }
        return cr;
    }

    private Callable<CategoryIndexingLog> fetchProductsForCategory(final String category) {
        return () -> {
            final CategoryIndexingLog categoryIndexingLog = new CategoryIndexingLog();
            categoryIndexingLog.setStatus(UNKNOWN);
            categoryIndexingLog.setName(category);
            try {
                final long startTime = System.currentTimeMillis();
                final SageSearchRequest sageSearchRequest = new SageSearchRequest();
                sageSearchRequest.setCategory(category);
                sageSearchRequest.setStartNum(valueOf(0));
                sageSearchRequest.setMaxRecs(valueOf(maxSageRecord));
                sageSearchRequest.setMaxTotalItems(maxSageRecord);

                SearchResponse searchResponse;
                log.info(format("Fetching products for category: %s ", category));
                final Optional<SearchResponse> optionalSearchResponse = getSageSearchResponse(sageSearchRequest);
                if (optionalSearchResponse.isPresent()) {
                    searchResponse = optionalSearchResponse.get();
                    log.info(format("Fetched products for category: %s count: %s", category, searchResponse.getData()
                        .getTotalFound()));
                    categoryIndexingLog.setTotalRecord(Long.valueOf(ofNullable(searchResponse.getData()
                        .getTotalFound()).orElse("0")));
                    categoryIndexingLog.setStatus(SUCCESS);
                } else {
                    categoryIndexingLog.setTotalRecord(-1L);
                    categoryIndexingLog.setTimeTaken((System.currentTimeMillis() - startTime) / MILLISECONDS);
                    categoryIndexingLog.setStatus(ERROR);
                    return categoryIndexingLog;
                }

                if (searchResponse.getData() == null || searchResponse.getData()
                    .getTotalFound() == null) {
                    categoryIndexingLog.setTotalRecord(-1L);
                    categoryIndexingLog.setStatus(EMPTY);
                    categoryIndexingLog.setTimeTaken((System.currentTimeMillis() - startTime) / MILLISECONDS);
                    return categoryIndexingLog;
                }

                final List<String> savedElasticProducts = new ArrayList<>();

                final List<ESProduct> products = searchResponse.getData()
                        .getItems()
                        .getItem()
                        .stream()
                        .map(IndexSageProductService::buildSageProduct)
                        .collect(Collectors.toList());

                for (int i = 0; i < products.size(); i += batchSize) {
                    sageProductRepository.saveAll(products.subList(i, Math.min(i + batchSize, products.size())))
                            .forEach(savedProduct -> savedElasticProducts.add(savedProduct.getProductID()));
                }

                log.info(format("Products saved for category: %s count: %s", category, savedElasticProducts.size()));
                final HashMap<String, String> idMap = new HashMap<>();
                final List<SageProductInventoryRequest> requestList = savedElasticProducts.stream()
                        .map(SageProductInventoryRequest::new)
                        .collect(Collectors.toList());

                requestList.forEach(id -> idMap.put(id.getProductId(), id.getProductIdFull()));

                ProductInventoryResponse productInventoryResponse;
                final Optional<ProductInventoryResponse> optionalProductInventoryResponse = getProductInventoryWithRetries(requestList, category);

                if (optionalProductInventoryResponse.isEmpty()) {
                    log.error("Inventory response is null for category: {}", category);
                    categoryIndexingLog.setInventoryCount(-1);
                    categoryIndexingLog.setInventoryStatus(ERROR);
                } else {
                    productInventoryResponse = optionalProductInventoryResponse.get();
                    if (productInventoryResponse.getProducts() == null) {
                        log.error("Inventory response is empty for category: {}", category);
                        categoryIndexingLog.setInventoryCount(-1);
                        categoryIndexingLog.setInventoryStatus(EMPTY);
                    } else {
                        final AtomicInteger actualInventoryCount = new AtomicInteger();
                        productInventoryResponse.getProducts()
                            .stream()
                            .filter(Objects::nonNull)
                            .filter(product -> product.getProductId() != null)
                            .filter(product -> product.getSkus() != null)
                            .forEach(product -> {
                                final List<Map<String, Object>> skusList = product.getSkus()
                                    .stream()
                                    .filter(Objects::nonNull)
                                    .map(sku -> Map.of("onHand", ofNullable(sku.getOnHand()).orElse(-1L), "attributes",
                                        ofNullable(sku.getAttributes()).orElse(Collections.emptyList())
                                            .stream()
                                            .filter(Objects::nonNull)
                                            .map(attr -> Map.of("typeId", ofNullable(attr.getTypeId()).orElse(-1L), "value",
                                                ofNullable(attr.getValue()).orElse("N/A")))
                                            .collect(Collectors.toList())))
                                    .toList();

                                final co.elastic.clients.elasticsearch._types.Script script =  co.elastic.clients.elasticsearch._types.Script.of(builder ->
                                    builder.inline(i -> i
                                    .source("ctx._source.skus = params.skus")
                                    .params("skus", JsonData.of(skusList))
                                ));

                                final UpdateRequest<Object, Object> updateRequest = new UpdateRequest.Builder<Object, Object>()
                                    .index(ESProduct.ES_PRODUCTS)
                                    .id(idMap.get(valueOf(product.getProductId())))
                                    .script(script)
                                    .build();

                                try {
                                    log.debug("inventory updated for product with id : {}", idMap.get(valueOf(product.getProductId())));
                                    elasticsearchClient.update(updateRequest, RequestOptions.DEFAULT.getClass());
                                    actualInventoryCount.incrementAndGet();
                                    categoryIndexingLog.setInventoryCount(actualInventoryCount.get());
                                    categoryIndexingLog.setInventoryStatus(SUCCESS);
                                } catch (IOException e) {
                                    categoryIndexingLog.setInventoryStatus(ERROR);
                                    log.error("Exception occurred while updating inventory for category {}", category, e);
                                }
                            });
                        log.info(format("Actual Inventory response for category: %s has count: %s", category, actualInventoryCount.get()));
                    }
                }
                log.info(format("Indexing for category: %s took seconds : %s ", category, (System.currentTimeMillis() - startTime) / MILLISECONDS));
                categoryIndexingLog.setTimeTaken((System.currentTimeMillis() - startTime) / MILLISECONDS);
            } catch (Exception e) {
                log.error(format("Exception occurred in fetching products for category %s", category), e);
            }
            return categoryIndexingLog;
        };
    }

    private static ESProduct buildSageProduct(final SearchResponseItem searchResponseItem) {
        final String RANGE_SEPARATOR = " - ";
        final String CATEGORY_SEPARATOR = ",";

        final String[] prcRange = ofNullable(searchResponseItem.getPrc()).orElse(StringUtils.EMPTY+RANGE_SEPARATOR+StringUtils.EMPTY).split(RANGE_SEPARATOR);
        final String[] netRange = ofNullable(searchResponseItem.getNet()).orElse(StringUtils.EMPTY+RANGE_SEPARATOR+StringUtils.EMPTY).split(RANGE_SEPARATOR);
        final String[] categories = searchResponseItem.getCategory().split(CATEGORY_SEPARATOR);

        return ESProduct.builder()
            .productID(searchResponseItem.getProductId())
            .spc(searchResponseItem.getSpc())
            .prName(searchResponseItem.getPrName())
            .category(categories[0])
            .secondaryCategory(categories.length > 1 ? categories[1] : null)
            .itemNum(searchResponseItem.getItemNum())
            .skus(Collections.emptyList())
            .description(searchResponseItem.getDescription())
            .colors(searchResponseItem.getColors())
            .themes(searchResponseItem.getThemes())
            .prodTime(searchResponseItem.getProdTime())
            .companyName(searchResponseItem.getCompanyName())
            .lineName(searchResponseItem.getLineName())
            .isOptamark(false)
            .minPrc(prcRange[0].trim())
            .maxPrc(prcRange.length > 1 ? prcRange[1].trim() : StringUtils.EMPTY)
            .minNet(netRange[0].trim())
            .maxNet(netRange.length > 1 ? netRange[1].trim() : StringUtils.EMPTY)
            .suppID(searchResponseItem.getSuppId())
            .thumbPicLink(searchResponseItem.getThumbPicLink())
            .build();
    }

    private Optional<SearchResponse> getSageSearchResponse(final SageSearchRequest sageSearchRequest) {
        int count = 0;
        while (count < maxRequestAttempt) {
            try {
                return ofNullable(sageSearchService.searchSage(sageSearchRequest));
            } catch (Exception e) {
                count++;
                log.error(format("Exception occurred while searching SAGE with category: %s, sageSearchRequest: %s, count: %d"
                    , sageSearchRequest.getCategory(), sageSearchRequest, count), e);
            }
        }
        return empty();
    }

    private Optional<ProductInventoryResponse> getProductInventoryWithRetries(final List<SageProductInventoryRequest> request, final String category) {
        int count = 0;
        while (count < maxRequestAttempt) {
            try {
                return getProductInventory(request, category);
            } catch (Exception e) {
                count++;
                log.error(format("Exception occurred while calling inventory service for category: %s with request count: %s", category, count), e);
            }
        }
        return empty();
    }

    private static final int MILLISECONDS = 1000;
    private static final int MINUTES = 60;
    private static final String DOUBLE_COMMA = ",,";
    private static final String SINGLE_COMMA = ",";
    private static final String MALFORMED_MEMO = "lease check on availability and production schedul";
    private static final String MALFORMED_MEMO_1 = "lease check on availability of colors and production schedul";
    private static final String MALFORMED_MEMO_2 = "lease call to confirm availability of color";
    private static final String CORRECT_MEMO =
        "\"skus\": [\n" + "    {\n" + "      \"memo\": \"please check on availability and production schedule\"\n" + "    }\n" + "  ]";
    private static final String SUCCESS = "Success";
    private static final String ERROR = "Error";
    private static final String EMPTY = "Empty";
    private static final String UNKNOWN = "Unknown";

    public void indexOptamarkProducts() {

        final byte[] excelData = retryOptamarkProductDownloadCSV();
        final List<OptamarkProductModel> optamarkProducts = readOptamarkProductCSVFile(excelData);
        final List<String> itemNumList = returnItemNumList(optamarkProducts);
        itemNumList.removeIf(s -> s.trim().isEmpty());
        final List<ESProduct> productList = sageProductRepository.findByItemNumIn(itemNumList);

        if (productList.isEmpty()) {
            log.info("No products found to update.");
        }

        int updatedCount = 0;

        if (!productList.isEmpty()) {
            for (final ESProduct product : productList) {
                /*final UpdateRequest updateRequest = new UpdateRequest("es_products", product.getProductID())
                        .script(new Script(ScriptType.INLINE.toString(), "painless", "ctx._source.isOptamark = true"));
                try {
                    restHighLevelClient.update(updateRequest, RequestOptions.DEFAULT);
                    updatedCount++;
                } catch (IOException e) {
                    log.error(format("Exception occurred while updating isOptamark for product id %s", product.getProductID()), e);
                }*/
            }
        }

        log.info("Update summary: {} records updated successfully, {} records not found out of {} total records.",
                updatedCount, (itemNumList.size() - updatedCount) , itemNumList.size());

        log.info("Before removal, Optamark product count: {}", optamarkProducts.size());

        optamarkProducts.removeIf(optamarkProduct ->
                productList.stream()
                        .anyMatch(product -> product.getItemNum().equals(returnItemNum(optamarkProduct.getProductsSku())))
        );

        log.info("After removal, Optamark product count: {}", optamarkProducts.size());

        saveOptamarkProduct(optamarkProducts);
    }

    private List<String> returnItemNumList(final List<OptamarkProductModel> optamarkProducts) {
        return optamarkProducts.stream()
                .map(product -> product.getProductsSku()
                        .trim()
                        .replaceFirst(OP, StringUtils.EMPTY)
                        .replaceFirst(REGEX, StringUtils.EMPTY))
                .collect(Collectors.toList());
    }

    private String returnItemNum(String itemNum) {
        return itemNum.trim()
                .replaceFirst(OP, StringUtils.EMPTY)
                .replaceFirst(REGEX, StringUtils.EMPTY);
    }

    private byte[] retryOptamarkProductDownloadCSV() {
        int count = 0;
        while (count < maxRequestAttempt) {
            try {
                log.info("Attempt {} to download Optamark product Excel.", count + 1);
                return optamarkProductDownloadCSV();
            } catch (Exception e) {
                count++;
                log.warn("Attempt {} failed: {}", count, e.getMessage());
            }
        }
        log.error("Failed to download Optamark product Excel after {} attempts.", maxRequestAttempt);
        return new byte[0];
    }

    private byte[] optamarkProductDownloadCSV() {
        try {
            ResponseEntity<byte[]> response = restTemplate.exchange(
                    optamarkProductExcelApi,
                    HttpMethod.GET,
                    null,
                    byte[].class
            );

            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                log.info("Successfully downloaded Optamark product Excel.");
                return response.getBody();
            } else {
                log.warn("Failed to download Excel: HTTP status {}", response.getStatusCode());
                return new byte[0];
            }
        } catch (Exception e) {
            log.error("Error downloading Excel: {}", e.getMessage());
            throw new RuntimeException("Error downloading Excel", e);
        }
    }

    private List<OptamarkProductModel> readOptamarkProductCSVFile(final byte[] csvData) {
        if (csvData == null || csvData.length == 0) {
            log.warn("CSV data is empty or null.");
            return Collections.emptyList();
        }

        try (ByteArrayInputStream bis = new ByteArrayInputStream(csvData);
             InputStreamReader reader = new InputStreamReader(bis)) {

            CsvToBean<OptamarkProductModel> csvToBean = new CsvToBeanBuilder<OptamarkProductModel>(reader)
                    .withType(OptamarkProductModel.class)
                    .withIgnoreLeadingWhiteSpace(true)
                    .withIgnoreEmptyLine(true)
                    .withThrowExceptions(false)
                    .build();

            final List<OptamarkProductModel> products = csvToBean.parse();
            log.info("Successfully parsed {} products from CSV.", products.size());
            return products;

        } catch (Exception e) {
            log.error("Error processing CSV file: {}", e.getMessage());
            throw new RuntimeException("Error processing CSV file", e);
        }
    }

    private void saveOptamarkProduct(List<OptamarkProductModel> optamarkProducts) {
        log.info("Processing {} Optamark products for saving...", optamarkProducts.size());
        List<ESProduct> esProducts = optamarkProducts.stream()
                .map(optamarkProduct -> {
                    try {
                        return ESProduct.builder()
                                .productID("OP-" + optamarkProduct.getProductsId())
                                .prName(optamarkProduct.getProductsTitle())
                                .category(optamarkProduct.getCategoryName())
                                .categoryGroup(optamarkProduct.getCategoryGroup())
                                .colors(optamarkProduct.getColor())
                                .minimumQty(optamarkProduct.getMinimumQty())
                                .unitPrice(optamarkProduct.getUnitPrice())
                                .totalPrice(optamarkProduct.getTotalPrice())
                                .isOptamark(true)
                                .thumbPicLink(optamarkProduct.getImage().split("\n")[0])
                                .description(HtmlCleanerUtil.cleanHtml(optamarkProduct.getDescriptionEncoded()))
                                .url(optamarkProduct.getProductUrl())
                                .build();
                    } catch (Exception e) {
                        log.error("Error processing product ID: {} - {}", optamarkProduct.getProductsId(), e.getMessage());
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        if (!esProducts.isEmpty()) {
            sageProductRepository.saveAll(esProducts);
            log.info("Successfully saved {} Optamark products.", esProducts.size());
        } else {
            log.warn("No valid Optamark products to save.");
        }
    }

    public void uploadOptamarkProducts(final MultipartFile productCSV) {
        if (productCSV == null || productCSV.isEmpty()) {
            log.warn("Uploaded CSV file is empty or null.");
            return;
        }

        try (Reader reader = new InputStreamReader(productCSV.getInputStream())) {
            CsvToBean<OptamarkProductModel> csvToBean = new CsvToBeanBuilder<OptamarkProductModel>(reader)
                    .withType(OptamarkProductModel.class)
                    .withIgnoreLeadingWhiteSpace(true)
                    .withIgnoreEmptyLine(true)
                    .withThrowExceptions(false)
                    .build();

            List<OptamarkProductModel> productList = csvToBean.parse();
            log.info("Parsed {} products from CSV file.", productList.size());

            saveOptamarkProduct(productList);
        } catch (Exception e) {
            log.error("Error processing the uploaded CSV file: {}", e.getMessage(), e);
        }
    }

    protected static final String OP = "^OP-?";
    protected static final String REGEX = "-.*$";

    public void generateEmbeddingByProductId(final List<String> productIds, final Integer poolSize) {
        generateEmbeddingForProducts(productIds, poolSize);
    }


    public void generateEmbedding(final Integer maxRecords, final Integer poolSize) {
        // Get all product IDs from Elasticsearch
        List<String> productIds = scrollAndCollectProductIds(maxRecords);

        log.info("size product list for embedding generation {}", productIds.size());

        generateEmbeddingForProducts(productIds, poolSize);
    }

    private List<String> scrollAndCollectProductIds(@Nullable final Integer maxResults) {
        final int batchSize = 1000;
        final List<String> collectedIds = new ArrayList<>();

        final String[] scrollIdHolder = new String[1];
        final co.elastic.clients.elasticsearch.core.SearchResponse<Map>[] responseHolder =
            new co.elastic.clients.elasticsearch.core.SearchResponse[1];
        log.info("Starting scrolling to collect productIds");
        final long start = System.currentTimeMillis();
        try {
            log.info("Starting initial scroll search on index '{}' for field '{}'", ESProduct.ES_PRODUCTS, "productID");

            // Initial search request with scroll
            responseHolder[0] = elasticsearchClient.search(s -> s
                    .index(ESProduct.ES_PRODUCTS)
                    .size(batchSize)
                    .scroll(t -> t.time("1m"))
                    .query(q -> q.matchAll(m -> m))
                    .source(src -> src.filter(f -> f.includes("productID"))),
                Map.class
            );

            scrollIdHolder[0] = responseHolder[0].scrollId();
            log.info("Initial scroll ID received: {}", scrollIdHolder[0]);

            int batchCount = 0;

            // Scroll loop
            while (responseHolder[0].hits().hits().size() > 0) {
                batchCount++;
                int currentBatchSize = responseHolder[0].hits().hits().size();
                log.info("Processing batch {} with {} hits", batchCount, currentBatchSize);

                for (Hit<Map> hit : responseHolder[0].hits().hits()) {
                    Object idObj = hit.source().get("productID");
                    if (idObj != null) {
                        collectedIds.add(idObj.toString());
                    }

                    // Stop early if maxResults is defined and reached
                    if (maxResults != null && maxResults > 0 && collectedIds.size() >= maxResults) {
                        log.info("Reached maxResults limit ({}), stopping scroll.", maxResults);
                        break;
                    }
                }

                if (maxResults != null && maxResults > 0 && collectedIds.size() >= maxResults) {
                    break;
                }

                log.info("Collected total {} IDs so far", collectedIds.size());

                // Fetch next batch using scroll
                ScrollResponse<Map> scrollResponse = elasticsearchClient.scroll(r -> r
                        .scrollId(scrollIdHolder[0])
                        .scroll(t -> t.time("1m")),
                    Map.class
                );

                scrollIdHolder[0] = scrollResponse.scrollId();
                log.info("Scroll ID for next batch: {}", scrollIdHolder[0]);

                responseHolder[0] = new co.elastic.clients.elasticsearch.core.SearchResponse.Builder<Map>()
                    .took(scrollResponse.took())
                    .timedOut(scrollResponse.timedOut())
                    .shards(scrollResponse.shards())
                    .hits(scrollResponse.hits())
                    .scrollId(scrollResponse.scrollId())
                    .build();
            }

            log.info("Completed scrolling. Total product IDs collected: {}", collectedIds.size());

            // Clear scroll context to free ES memory
            if (scrollIdHolder[0] != null) {
                log.info("Clearing scroll context for scroll ID: {}", scrollIdHolder[0]);
                elasticsearchClient.clearScroll(r -> r.scrollId(scrollIdHolder[0]));
            }

        } catch (Exception e) {
            log.error("Error during scroll operation", e);
            throw new RuntimeException("Error during scroll operation", e);
        }
        log.info("Collecting productIds took time(s): {}", (System.currentTimeMillis() - start) / 1000);
        return maxResults != null && maxResults > 0 && collectedIds.size() > maxResults
            ? collectedIds.subList(0, maxResults)
            : collectedIds;
    }




    private void generateEmbeddingForProducts(final List<String> productIds, final Integer poolSize) {

        final EmbeddingLog embeddingLog = new EmbeddingLog(Date.from(Instant.now()));
        embeddingLog.setTotalProductIds(productIds.size());
        embeddingLog.setStatus("Started");
        embeddingLog.setTotalFailedRecords(0L);
        embeddingLogRepository.save(embeddingLog);

        final AtomicLong count = new AtomicLong(0);

        final List<Callable<EmbeddingGenerationLog>> embeddingGenerationCallables = productIds.stream()
            .map(p -> updateEmbeddings(p, count))
            .collect(Collectors.toList());

        List<Future<EmbeddingGenerationLog>> futureList = new ArrayList<>();
        log.info("starting embedding generation with total callables {}", embeddingGenerationCallables.size());
        long start = System.currentTimeMillis();
        try {
            futureList = Executors.newFixedThreadPool(poolSize).invokeAll(embeddingGenerationCallables);
        } catch (InterruptedException e) {
            log.error("Interruption occurred", e);
        }
        log.info("embedding generation completed with total futures {}", futureList.size());

        embeddingLog.setFailedProductIds(new ArrayList<>());
        futureList.stream()
            .map(this::getFutureEmbeddingResult)
            .forEach(embeddingGenerationLog  -> {
                embeddingLog.setTotalUpdatedRecords(Optional.ofNullable(embeddingLog.getTotalUpdatedRecords()).orElse(0L)  + 1);
                if(ERROR.equals(embeddingGenerationLog.getStatus())){
                    embeddingLog.setTotalFailedRecords(Optional.ofNullable(embeddingLog.getTotalFailedRecords()).orElse(0L)  + 1);
                    embeddingLog.getFailedProductIds().add(embeddingGenerationLog.getProductId());
                }
            });

        if (embeddingLog.getFailedProductIds().isEmpty()) {
            embeddingLog.setStatus(SUCCESS);
        } else {
            embeddingLog.setStatus(ERROR);
        }
        embeddingLog.setTotalTimeTaken((System.currentTimeMillis() - start) / 1000);
        embeddingLogRepository.save(embeddingLog);
        log.info("embedding generation status {}", embeddingLog);
    }

    private Callable<EmbeddingGenerationLog> updateEmbeddings(final String productId, final AtomicLong count){
        return () -> {
            log.info("starting embedding generation for product id: {}", productId);
            EmbeddingGenerationLog egl = new EmbeddingGenerationLog();
            egl.setProductId(productId);
            egl.setStatus("Started");
            egl.setEmbeddingGenerated(Date.from(Instant.now()));
            try {
                final List<Double> productEmbedding = generateEmbedding(productId);
                if(!productEmbedding.isEmpty()) {
                    Map<String, JsonData> params = new HashMap<>();
                    params.put("embedding", JsonData.of(productEmbedding));
                    params.put("embedding_generation_date", JsonData.of(Instant.now()
                        .toString())); // ISO 8601 format

                    final co.elastic.clients.elasticsearch._types.Script script = co.elastic.clients.elasticsearch._types.Script.of(builder ->
                        builder.inline(i -> i.source("ctx._source.embedding = params.embedding; ctx._source.embedding_generation_date = params.embedding_generation_date")
                        .params(params)));

                    final UpdateRequest<Object, Object> updateRequest = new UpdateRequest.Builder<Object, Object>().index(ESProduct.ES_PRODUCTS)
                        .id(String.valueOf(productId))
                        .script(script)
                        .build();

                    elasticsearchClient.update(updateRequest, RequestOptions.DEFAULT.getClass());
                    egl.setStatus(SUCCESS);
                } else {
                    log.error("empty embedding received for product: {}", productId);
                    egl.setStatus(ERROR);
                }
            }
            catch (Exception e){
                log.error("exception occurred while embedding generation for product: {}", productId, e);
                egl.setStatus(ERROR);
            }
            log.info("completed embedding generation for product id: {} and total count {}", productId, count.incrementAndGet());
            return egl;
        };
    }

    private List<Double> generateEmbedding(final String productId) {
        final ESProduct product = sageProductRepository.findById(productId).orElseThrow();
            final Map<String, Object> metadata = new HashMap<>();
            if(product.getIsOptamark()){
                metadata.put("prName", sanitizeForEmbedding(product.getPrName()));
                metadata.put("category", sanitizeForEmbedding(product.getCategory()));
                metadata.put("description", sanitizeForEmbedding(product.getDescription() != null ? product.getDescription() : ""));
                metadata.put("colors", sanitizeForEmbedding(product.getColors() != null ? product.getColors() : ""));
                metadata.put("minimumQty", sanitizeForEmbedding(product.getMinimumQty()));
                metadata.put("unitPrice", sanitizeForEmbedding(product.getUnitPrice()));
                metadata.put("totalPrice", sanitizeForEmbedding(product.getTotalPrice()));
                metadata.put("isOptamark", product.getIsOptamark());
            } else {
                metadata.put("spc", sanitizeForEmbedding(product.getSpc()));
                metadata.put("prName", sanitizeForEmbedding(product.getPrName()));
                metadata.put("category", sanitizeForEmbedding(product.getCategory()));
                metadata.put("itemNum", sanitizeForEmbedding(product.getItemNum()));
                metadata.put("skus", product.getSkus() != null ? product.getSkus()
                    .stream()
                    .limit(100)
                    .collect(Collectors.toList()) : new String[] {});
                metadata.put("description", sanitizeForEmbedding(product.getDescription()));
                metadata.put("colors", sanitizeForEmbedding(product.getColors()));
                metadata.put("prodTime", sanitizeForEmbedding(product.getProdTime()));
                metadata.put("suppID", sanitizeForEmbedding(product.getSuppID()));
                metadata.put("lineName", sanitizeForEmbedding(product.getLineName()));
                metadata.put("companyName", sanitizeForEmbedding(product.getCompanyName()));
                metadata.put("minPrc", sanitizeForEmbedding(product.getMinPrc()));
                metadata.put("maxPrc", sanitizeForEmbedding(product.getMaxPrc()));
                metadata.put("minNet", sanitizeForEmbedding(product.getMinNet()));
                metadata.put("maxNet", sanitizeForEmbedding(product.getMaxNet()));
                metadata.put("isOptamark", product.getIsOptamark());
            }
            String text = product.getPrName().concat(product.getDescription());
            Document document = new Document(product.getProductID(), text , metadata);
            return generateEmbedding(document);
    }

    private void saveProductEmbeddings(List<ESProduct> products) {
        sageProductRepository.saveAll(products);
    }

    private List<Double> generateEmbedding(final Document document){
        if (embeddingClient == null) {
            log.error("Embedding client is not initialized. Cannot generate embedding.");
        }
        if (document == null) {
            log.error("Null document passed to embedding generator.");
        }
        try {
            var response = embeddingClient.embed(document);
            if (response.isEmpty()) {
                log.warn("Empty embedding response for productId: {}", document.getId());
                return Collections.emptyList();
            }
            return response;
        } catch (Exception e) {
            log.error("Error generating embedding for productId: {} document: {}", document.getId(), document, e);
            return new ArrayList<>();
        }
    }

    private static List<Double> generateDummyEmbedding() {
        List<Double> dummyEmbedding = new ArrayList<>(1536);
        for (int i = 0; i < 1536; i++) {
            dummyEmbedding.add(0.001);
        }
        return dummyEmbedding;
    }
}
