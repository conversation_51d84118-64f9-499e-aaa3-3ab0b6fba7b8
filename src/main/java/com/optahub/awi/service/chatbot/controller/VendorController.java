package com.optahub.awi.service.chatbot.controller;

import com.optahub.awi.service.chatbot.entity.Vendor;
import com.optahub.awi.service.chatbot.service.VendorService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.validation.Valid;
import java.util.List;
import java.util.NoSuchElementException;

import lombok.AllArgsConstructor;

@RestController
@RequestMapping("/api/vendors")
@AllArgsConstructor
public class VendorController {

    private final VendorService vendorService;

    @PostMapping("/add")
    public ResponseEntity<String> addVendor(@Valid @RequestBody Vendor vendor) {
        vendorService.saveVendor(vendor);
        return ResponseEntity.status(HttpStatus.CREATED).body("Vendor added successfully!");
    }

    @DeleteMapping("/delete/{id}")
    public ResponseEntity<String> deleteVendor(@PathVariable String id) {
        try {
            vendorService.deleteVendor(id);
            return ResponseEntity.status(HttpStatus.OK).body("Vendor deleted successfully!");
        } catch (NoSuchElementException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body("Vendor not found with ID: " + id);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("Failed to delete vendor: " + e.getMessage());
        }
    }

    @GetMapping("/all")
    public ResponseEntity<Iterable<Vendor>> getAllVendors(@RequestParam(defaultValue = "0") int page,
                                                          @RequestParam(defaultValue = "10") int size) {
        try {
            return ResponseEntity.ok(vendorService.getAllVendors(page, size));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }

    @GetMapping("/byVendorId/{vendorId}")
    public ResponseEntity<List<Vendor>> getVendorsByVendorId(@PathVariable String vendorId) {
        try {
            return ResponseEntity.ok(vendorService.findByVendorId(vendorId));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }

    @PutMapping("/update/{id}")
    public ResponseEntity<String> updateVendor(@PathVariable String id, @Valid @RequestBody Vendor vendor) {
        try {
            vendorService.updateVendor(id, vendor);
            return ResponseEntity.ok("Vendor updated successfully!");
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("Failed to update vendor: " + e.getMessage());
        }
    }

    @PostMapping(value = "/upload", consumes = "multipart/form-data")
    public ResponseEntity<String> uploadExcelFile(@RequestParam("file") MultipartFile file) {
        try {
            vendorService.processExcelFile(file);
            return ResponseEntity.status(HttpStatus.CREATED).body("File uploaded and vendor data indexed successfully!");
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("Failed to upload file: " + e.getMessage());
        }
    }
}
