package com.optahub.awi.service.chatbot.repository;

import java.util.List;

import com.optahub.awi.service.chatbot.entity.ESProduct;
import com.optahub.awi.service.chatbot.entity.Sku;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.elasticsearch.annotations.Query;
import org.springframework.data.elasticsearch.repository.ElasticsearchRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface SageProductRepository extends ElasticsearchRepository<ESProduct, String> {


    @Query("{ \"script\": { \"source\": \"ctx._source.skus = params.skus\", \"lang\": \"painless\", \"params\": { \"skus\": :#{#skus} } }, \"query\": { \"term\": { \"productID\": :#{#productID} } } }")
    void updateSkusByProductID(@Param("productID") String productID, @Param("skus") List<Sku> skus);

    List<ESProduct> findByItemNumIn(List<String> productID);

    @Query("SELECT p FROM ESProduct p ORDER BY p.productID ASC")
    Page<ESProduct> findProductsInBatches(Pageable pageable);
}
