package com.optahub.awi.service.chatbot.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;

import java.util.List;

@Getter
public class LLMResponse {

    private final List<String> ranked_ids;
    private final String assistant_reply;

    @JsonCreator
    public LLMResponse(
            @JsonProperty(value = "ranked_ids", required = true) List<String> ranked_ids,
            @JsonProperty(value = "assistant_reply", required = true) String assistant_reply) {
        this.ranked_ids = ranked_ids;
        this.assistant_reply = assistant_reply;
    }
}
