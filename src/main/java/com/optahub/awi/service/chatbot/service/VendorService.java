package com.optahub.awi.service.chatbot.service;

import com.optahub.awi.service.chatbot.entity.Vendor;
import com.optahub.awi.service.chatbot.repository.VendorRepository;
import org.apache.poi.ss.usermodel.*;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class VendorService {

    private final VendorRepository vendorRepository;
    public VendorService(VendorRepository vendorRepository) {
        this.vendorRepository = vendorRepository;
    }

    public void saveVendor(Vendor vendor) {
        log.info("Saving vendor with ID: {}", vendor.getVendorId());
        vendorRepository.save(vendor);
    }

    public void updateVendor(String id, Vendor vendor) {
        log.info("Updating vendor with ID: {}", id);
        Optional<Vendor> existingVendor = vendorRepository.findById(id);
        if (existingVendor.isPresent()) {
            Vendor updatedVendor = existingVendor.get();
            updatedVendor.setVendorName(vendor.getVendorName());
            updatedVendor.setVendorId(vendor.getVendorId());
            vendorRepository.save(updatedVendor);
            log.info("Vendor with ID: {} updated successfully", id);
        } else {
            log.error("Vendor with ID: {} not found", id);
            throw new RuntimeException("Vendor with ID " + id + " not found.");
        }
    }

    public void deleteVendor(String id) {
        log.info("Deleting vendor with ID: {}", id);
        vendorRepository.deleteById(id);
        log.info("Vendor with ID: {} deleted successfully", id);
    }

    public Iterable<Vendor> getAllVendors(int page, int size) {
        log.info("Fetching all vendors with pagination - page: {}, size: {}", page, size);
        return vendorRepository.findAll(PageRequest.of(page, size));
    }

    public List<Vendor> findByVendorId(String vendorId) {
        log.info("Fetching vendors with vendor ID: {}", vendorId);
        return vendorRepository.findByVendorId(vendorId);
    }

    public void processExcelFile(MultipartFile file) {
        log.info("Processing uploaded Excel file: {}", file.getOriginalFilename());
        if (file.isEmpty()) {
            log.error("Uploaded file is empty.");
            throw new IllegalArgumentException("Uploaded file is empty.");
        }

        if (!file.getOriginalFilename().endsWith(".xls") && !file.getOriginalFilename().endsWith(".xlsx")) {
            log.error("Invalid file type. Please upload an Excel file.");
            throw new IllegalArgumentException("Invalid file type. Please upload an Excel file.");
        }
        File tempFile = null;
        try {
            tempFile = File.createTempFile("uploaded-", file.getOriginalFilename());
            file.transferTo(tempFile);

            try (FileInputStream fis = new FileInputStream(tempFile);
                 Workbook workbook = WorkbookFactory.create(fis)) {

                Sheet sheet = workbook.getSheetAt(0);
                List<Vendor> vendors = new ArrayList<>();
                for (Row row : sheet) {
                    Cell vendorNameCell = row.getCell(0);
                    Cell vendorIdCell = row.getCell(1);

                    if (vendorNameCell == null || vendorIdCell == null) {
                        continue;
                    }

                    String vendorName = vendorNameCell.getStringCellValue().trim();
                    String vendorId = vendorIdCell.getStringCellValue().trim();

                    Vendor vendor = new Vendor();
                    vendor.setVendorName(vendorName);
                    vendor.setVendorId(vendorId);

                    vendors.add(vendor);
                }

                vendorRepository.saveAll(vendors);
                log.info("Excel file processed and vendor data indexed successfully.");
            }
        } catch (IOException e) {
            log.error("Failed to process Excel file: {}", e.getMessage());
            throw new RuntimeException("Failed to process Excel file: " + e.getMessage());
        } finally {
            if (tempFile != null && tempFile.exists()) {
                tempFile.delete();
            }
        }
    }
}