package com.optahub.awi.service.chatbot.model;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SkuModel {

    private List<AttributeModel> attributes;
    private Long onHand;
    private Long onOrder;
    private String memo;
    private Date onOrderExpectedDate;
    private String warehouseId;
    private String warehouseCountry;
    private String warehouseZip;
    private String refreshLeadDays;
    private String unlimited;

}
