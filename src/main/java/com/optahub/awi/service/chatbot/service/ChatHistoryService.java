package com.optahub.awi.service.chatbot.service;

import com.optahub.awi.service.chatbot.model.ChatHistoryModel;
import com.optahub.awi.service.chatbot.model.ChatMeta;
import com.optahub.awi.service.chatbot.utils.S3Uploader;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.List;

@Service
public class ChatHistoryService {
    private final S3Uploader s3Uploader;

    public ChatHistoryService(S3Uploader s3Uploader) {
        this.s3Uploader = s3Uploader;
    }

    public List<ChatMeta> listChats(String username) {
        return s3Uploader.listChats(username);
    }

    public List<ChatHistoryModel> getFullChat(String username, String chatId) {
        return s3Uploader.getFullChat(username, chatId);
    }

    public void deleteChat(String username, String chatId) {
        boolean isDeleted = s3Uploader.deleteChat(username, chatId);
        if (!isDeleted) {
            throw new RuntimeException("Failed to delete chat with ID " + chatId + " for user " + username);
        }
    }

    public void updateChatTitle(String username, String chatId, String title) {
        if (username == null || username.isEmpty()) {
            throw new IllegalArgumentException("Username cannot be empty");
        }
        if (chatId == null || chatId.isEmpty()) {
            throw new IllegalArgumentException("Chat ID cannot be empty");
        }
        if (title == null) {
            throw new IllegalArgumentException("Title cannot be null");
        }

        ChatMeta existingMeta = s3Uploader.getExistingMeta(username, chatId);

        if (existingMeta == null) {
            throw new IllegalArgumentException("Chat with ID " + chatId + " not found for user " + username);
        }

        ChatMeta updatedMeta = ChatMeta.builder()
                .chatId(chatId)
                .title(title)
                .updatedAt(Instant.now())
                .createdAt(existingMeta.getCreatedAt())
                .build();

        try {
            s3Uploader.saveMeta(username, chatId, updatedMeta);
        } catch (RuntimeException e) {
            throw new RuntimeException("Failed to update chat title: " + e.getMessage(), e);
        }
    }

}
