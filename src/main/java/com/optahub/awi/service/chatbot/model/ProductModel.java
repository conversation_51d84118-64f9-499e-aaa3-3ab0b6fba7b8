package com.optahub.awi.service.chatbot.model;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ProductModel {

    private Long productId;
    private Integer sageNum;
    private String itemNum;
    private boolean ok;
    private Integer onHand;
    private List<SkuModel> skus;
    private Date lastUpdated;
    private String error;
}