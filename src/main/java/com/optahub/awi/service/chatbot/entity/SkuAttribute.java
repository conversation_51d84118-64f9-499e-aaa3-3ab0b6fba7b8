package com.optahub.awi.service.chatbot.entity;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class SkuAttribute {

    @Field(type = FieldType.Long)
    private Long typeId;

    @Field(type = FieldType.Text)
    private String value;

}
