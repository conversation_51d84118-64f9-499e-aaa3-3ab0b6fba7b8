package com.optahub.awi.service.chatbot.utils;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.optahub.awi.service.chatbot.entity.ESProduct;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.Objects;

public class HashUtils {

    private static final ObjectMapper mapper = new ObjectMapper()
            .setSerializationInclusion(JsonInclude.Include.NON_NULL)
            .configure(SerializationFeature.ORDER_MAP_ENTRIES_BY_KEYS, true);


    public static String generateProductHash(ESProduct esProduct){

        try {
            ObjectNode objectNode = mapper.createObjectNode();
            objectNode.put("productID", esProduct.getProductID());
            objectNode.put("spc", esProduct.getSpc());
            objectNode.put("prName", esProduct.getPrName());
            objectNode.put("category", esProduct.getCategory());
            objectNode.put("secondaryCategory", esProduct.getSecondaryCategory());
            objectNode.put("itemNum", esProduct.getItemNum());
            objectNode.put("skus", mapper.valueToTree(esProduct.getSkus()));
            objectNode.put("description", esProduct.getDescription());
            objectNode.put("colors", esProduct.getColors());
            objectNode.put("themes", esProduct.getThemes());
            objectNode.put("prodTime", esProduct.getProdTime());
            objectNode.put("suppID", esProduct.getSuppID());
            objectNode.put("lineName", esProduct.getLineName());
            objectNode.put("companyName", esProduct.getCompanyName());
            objectNode.put("minPrc", esProduct.getMinPrc());
            objectNode.put("maxPrc", esProduct.getMaxPrc());
            objectNode.put("minNet", esProduct.getMinNet());
            objectNode.put("maxNet", esProduct.getMaxNet());
            objectNode.put("isOptamark", esProduct.getIsOptamark());
            objectNode.put("categoryGroup", esProduct.getCategoryGroup());
            objectNode.put("productSku", esProduct.getProductSku());
            objectNode.put("minimumQty", esProduct.getMinimumQty());
            objectNode.put("unitPrice", esProduct.getUnitPrice());
            objectNode.put("totalprice", esProduct.getTotalPrice());

            String json = mapper.writeValueAsString(objectNode);
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hashBytes = digest.digest(json.getBytes(StandardCharsets.UTF_8));

            StringBuilder builder = new StringBuilder();
            for(byte x:hashBytes){
                builder.append(String.format("%02x", x));
            }

            return builder.toString();
        } catch (Exception e) {
            throw new RuntimeException("Error generating hash", e);
        }
    }

    public static boolean hasChanged(String oldHash, String newHash) {
        return !Objects.equals(oldHash, newHash);
    }


}
