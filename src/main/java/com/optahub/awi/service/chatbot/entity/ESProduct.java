package com.optahub.awi.service.chatbot.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.*;
import org.joda.time.DateTime;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Document(indexName = ESProduct.ES_PRODUCTS)
@JsonIgnoreProperties(ignoreUnknown = true)
@Builder
public class ESProduct {

    @Id
    @Field(type = FieldType.Keyword)
    private String productID;

    @Field(type = FieldType.Keyword)
    private String spc;

    @Field(type = FieldType.Text)
    private String prName;

    @Field(type = FieldType.Keyword)
    private String category;

    @Field(type = FieldType.Keyword)
    private String secondaryCategory;

    @Field(type = FieldType.Keyword)
    private String itemNum;

    @Field(type = FieldType.Nested)
    private List<Sku> skus;

    @Field(type = FieldType.Text)
    private String description;

    @Field(type = FieldType.Text)
    private String colors;

    @Field(type = FieldType.Text)
    private String themes;

    @Field(type = FieldType.Text)
    private String prodTime;

    @Field(type = FieldType.Keyword)
    private String suppID;

    @Field(type = FieldType.Keyword)
    private String lineName;

    @Field(type = FieldType.Keyword)
    private String companyName;

    @Field(type = FieldType.Text)
    private String minPrc;

    @Field(type = FieldType.Text)
    private String maxPrc;

    @Field(type = FieldType.Text)
    private String minNet;

    @Field(type = FieldType.Text)
    private String maxNet;

    @Field(type = FieldType.Text)
    private String thumbPicLink;

    @Builder.Default
    @Field(type = FieldType.Boolean)
    private Boolean isOptamark = false;

    @Field(type = FieldType.Text)
    private String categoryGroup;

    @Field(type = FieldType.Text)
    private String productSku;

    @Field(type = FieldType.Integer)
    private String minimumQty;

    @Field(type = FieldType.Integer)
    private String unitPrice;

    @Field(type = FieldType.Integer)
    private String totalPrice;

    @Field(type = FieldType.Dense_Vector, dims = 1536)
    private List<Double> embedding;

    @Field(type = FieldType.Date)
    private DateTime createdAt;

    @Field(type = FieldType.Date)
    private DateTime updatedAt;

    @Field(type = FieldType.Keyword)
    private String hashcode;

    @Field(type = FieldType.Date)
    private DateTime embeddingGenerationDate;

    @Field(type = FieldType.Keyword)
    private String url;

    public static final String ES_PRODUCTS = "es_products_v3";
}
