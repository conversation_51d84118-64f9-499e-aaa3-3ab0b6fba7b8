package com.optahub.awi.service.chatbot.model;


import com.optahub.awi.service.chatbot.entity.ESSearchResult;
import lombok.*;

import java.time.Instant;
import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ChatHistoryModel {
    private String username;
    private String chatId;
    private Instant timestamp;
    private String userQuery;
    private String contextualQuery;
    private String assistantResponse;
    private List<ESSearchResult> searchResults;
}
