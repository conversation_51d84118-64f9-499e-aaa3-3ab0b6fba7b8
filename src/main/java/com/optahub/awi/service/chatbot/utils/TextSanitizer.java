package com.optahub.awi.service.chatbot.utils;

public class TextSanitizer {

    /**
     * Removes all special characters from the input text, preserving letters, digits, and whitespace.
     *
     * @param input The raw text to be sanitized
     * @return Sanitized text
     */
    public static String sanitizeForEmbedding(String input) {
        if (input == null) return "";

        // Keep letters, digits, and whitespace. Remove everything else.
        return input.replaceAll("[^\\p{L}\\p{Nd}\\s\\.]", "").replaceAll("\\s+", " ").trim();
    }

}
