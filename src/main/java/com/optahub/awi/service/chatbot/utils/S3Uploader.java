package com.optahub.awi.service.chatbot.utils;

import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.*;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.optahub.awi.service.chatbot.model.ChatHistoryModel;
import com.optahub.awi.service.chatbot.model.ChatMeta;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
@RequiredArgsConstructor
public class S3Uploader {

    private final AmazonS3 amazonS3;
    private final ObjectMapper objectMapper;

    @Value("${aws.s3.history.bucket}")
    private String bucketName;

    public void uploadHistory(final ChatHistoryModel entry, final String key) {
        try {
            final String json = objectMapper.writeValueAsString(entry);
            final byte[] contentBytes = json.getBytes(StandardCharsets.UTF_8);
            final ObjectMetadata metadata = new ObjectMetadata();
            metadata.setContentLength(contentBytes.length);
            metadata.setContentType("application/json");

            amazonS3.putObject(bucketName, key, new ByteArrayInputStream(contentBytes), metadata);
        } catch (Exception e) {
            throw new RuntimeException("Failed to upload chat history", e);
        }
    }


    public void saveMeta(final String username, String chatId, ChatMeta meta) {
        final String metaKey = String.format("chat-history/%s/%s/meta.json", username, chatId);
        try {
            final byte[] bytes = objectMapper.writeValueAsString(meta)
                    .getBytes(StandardCharsets.UTF_8);
            final ObjectMetadata md = new ObjectMetadata();
            md.setContentType("application/json");
            md.setContentLength(bytes.length);
            amazonS3.putObject(bucketName, metaKey,
                    new ByteArrayInputStream(bytes), md);
        } catch (Exception e) {
            log.error("Failed to save chat metadata for user: {}, chatId: {}", username, chatId, e);
            throw new RuntimeException("Failed to save chat metadata: " + e.getMessage(), e);
        }
    }

    public List<ChatMeta> listChats(final String username) {
        final String prefix = "chat-history/" + username + "/";
        final ListObjectsV2Request req = new ListObjectsV2Request()
                .withBucketName(bucketName)
                .withPrefix(prefix);
        final List<ChatMeta> list = new ArrayList<>();
        ListObjectsV2Result res;
        do {
            res = amazonS3.listObjectsV2(req);
            for (final S3ObjectSummary s : res.getObjectSummaries()) {
                if (s.getKey().endsWith("/meta.json")) {
                    try (final S3Object obj = amazonS3.getObject(bucketName, s.getKey())) {
                        final ChatMeta cm = objectMapper.readValue(
                                obj.getObjectContent(),
                                ChatMeta.class
                        );
                        list.add(cm);
                    } catch (Exception e) {
                        log.error("Failed to load chat metadata", e);
                    }
                }
            }
            req.setContinuationToken(res.getNextContinuationToken());
        } while (res.isTruncated());
        return list.stream()
                .sorted(Comparator.comparing(ChatMeta::getUpdatedAt).reversed())
                .collect(Collectors.toList());
    }

    public List<ChatHistoryModel> getFullChat(String username, String chatId) {
        final String prefix = String.format("chat-history/%s/%s/", username, chatId);
        final ListObjectsV2Request req = new ListObjectsV2Request()
                .withBucketName(bucketName)
                .withPrefix(prefix);
        final List<ChatHistoryModel> entries = new ArrayList<>();
        ListObjectsV2Result res;
        do {
            res = amazonS3.listObjectsV2(req);
            for (final S3ObjectSummary s : res.getObjectSummaries()) {
                if (!s.getKey().endsWith("meta.json")) {
                    try (final S3Object obj = amazonS3.getObject(bucketName, s.getKey())) {
                        final ChatHistoryModel e = objectMapper.readValue(obj.getObjectContent(), ChatHistoryModel.class);
                        entries.add(e);
                    } catch (Exception e) {
                        log.error("Failed to load chat history entry from S3", e);
                    }
                }
            }
            req.setContinuationToken(res.getNextContinuationToken());
        } while (res.isTruncated());
        return entries.stream()
                .sorted(Comparator.comparing(ChatHistoryModel::getTimestamp))
                .collect(Collectors.toList());
    }

    public boolean deleteChat(String username, String chatId) {
        String prefix = String.format("chat-history/%s/%s/", username, chatId);
        ListObjectsV2Request listRequest = new ListObjectsV2Request()
                .withBucketName(bucketName)
                .withPrefix(prefix);

        try {
            ListObjectsV2Result listResult;
            do {
                listResult = amazonS3.listObjectsV2(listRequest);
                List<DeleteObjectsRequest.KeyVersion> keysToDelete = new ArrayList<>();


                for (S3ObjectSummary summary : listResult.getObjectSummaries()) {
                    keysToDelete.add(new DeleteObjectsRequest.KeyVersion(summary.getKey()));
                }

                if (!keysToDelete.isEmpty()) {
                    DeleteObjectsRequest deleteRequest = new DeleteObjectsRequest(bucketName)
                            .withKeys(keysToDelete);
                    amazonS3.deleteObjects(deleteRequest);
                }

                listRequest.setContinuationToken(listResult.getNextContinuationToken());
            } while (listResult.isTruncated());

            return true;
        } catch (Exception e) {
            log.error("Failed to delete chat", e);
            return false;
        }
    }

    public ChatMeta getExistingMeta(String username, String chatId) {
        String metaKey = String.format("chat-history/%s/%s/meta.json", username, chatId);
        try {

            if (!amazonS3.doesObjectExist(bucketName, metaKey)) {
                return null;
            }

            try (S3Object obj = amazonS3.getObject(bucketName, metaKey)) {
                return objectMapper.readValue(obj.getObjectContent(), ChatMeta.class);
            }
        } catch (AmazonS3Exception e) {
            log.error("S3 error retrieving metadata for user: {}, chatId: {}", username, chatId, e);
            throw new RuntimeException("Failed to retrieve chat metadata from S3", e);
        } catch (Exception e) {
            log.error("Error parsing metadata for user: {}, chatId: {}", username, chatId, e);
            throw new RuntimeException("Failed to parse chat metadata", e);
        }
    }

    public ChatHistoryModel getLatestChatEntry(String username, String chatId) {
        String prefix = String.format("chat-history/%s/%s/", username, chatId);
        ListObjectsV2Request req = new ListObjectsV2Request()
                .withBucketName(bucketName)
                .withPrefix(prefix);
        ChatHistoryModel latest = null;
        Instant latestTimestamp = null;
        ListObjectsV2Result res;
        do {
            res = amazonS3.listObjectsV2(req);
            for (S3ObjectSummary s : res.getObjectSummaries()) {
                String key = s.getKey();
                if (key.endsWith(".json") && !key.endsWith("meta.json")) {
                    try (S3Object obj = amazonS3.getObject(bucketName, key)) {
                        ChatHistoryModel entry = objectMapper.readValue(obj.getObjectContent(), ChatHistoryModel.class);
                        Instant entryTimestamp = entry.getTimestamp();
                        if (entryTimestamp != null && (latestTimestamp == null || entryTimestamp.isAfter(latestTimestamp))) {
                            latest = entry;
                            latestTimestamp = entryTimestamp;
                        }
                    } catch (Exception e) {
                        log.error("Failed to read chat entry: {}", key, e);
                    }
                }
            }
            req.setContinuationToken(res.getNextContinuationToken());
        } while (res.isTruncated());
        return latest;
    }
}
