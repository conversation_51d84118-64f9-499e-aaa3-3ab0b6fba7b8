package com.optahub.awi.service.chatbot.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import jakarta.validation.constraints.NotBlank;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Document(indexName = "vendors")
public class Vendor {

    @Id
    private String id;

    @Field(type = FieldType.Keyword)
    @NotBlank(message = "Vendor name cannot be blank")
    private String vendorName;

    @Field(type = FieldType.Keyword)
    @NotBlank(message = "Vendor ID cannot be blank")
    private String vendorId;
}
