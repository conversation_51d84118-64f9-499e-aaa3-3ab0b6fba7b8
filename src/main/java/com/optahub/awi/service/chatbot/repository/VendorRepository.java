package com.optahub.awi.service.chatbot.repository;

import com.optahub.awi.service.chatbot.entity.Vendor;
import org.springframework.data.elasticsearch.repository.ElasticsearchRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface VendorRepository extends ElasticsearchRepository<Vendor, String> {
    List<Vendor> findByVendorId(String vendorId);
}
