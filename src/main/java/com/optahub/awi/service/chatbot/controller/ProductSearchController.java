package com.optahub.awi.service.chatbot.controller;

import java.util.Set;
import java.util.UUID;

import com.optahub.awi.service.chatbot.model.ChatMeta;
import com.optahub.awi.service.chatbot.model.SearchChatResponse;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Schema;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.optahub.awi.service.chatbot.service.ProductSearchService;

@RestController
public class ProductSearchController {

    private static final Logger logger = LoggerFactory.getLogger(ProductSearchController.class);
    private final ProductSearchService service;

    public ProductSearchController(ProductSearchService service) {
        this.service = service;
    }

    @GetMapping("/search/product")
    public ResponseEntity<?> indexSageProducts(
            @RequestHeader("X-Username") String username,
            @RequestHeader(value = "chatId", required = false) String chatId,
            @RequestParam final String query,
            @RequestParam(defaultValue = "5") final int requiredResults,
            @RequestParam(defaultValue = "1000") final int neighbours,
            @RequestParam(required = false, defaultValue = "true") final boolean useContextual,
            @RequestParam(required = false) final Boolean isOptamark,
            @RequestParam(required = false, defaultValue = "false") final Boolean isPreferredVendor,
            @Parameter(
                    description = "List of company names to filter by",
                    example = "Company1,Company2,Company3",
                    array = @ArraySchema(schema = @Schema(type = "string"))
            )
            @RequestParam(required = false) Set<String> companyNames) {


        if (query == null || query.trim().isEmpty()) {
            logger.warn("Empty search query received from user: {}", username);
            return ResponseEntity.badRequest().body("Search query cannot be empty");
        }
        try {
            boolean isNewChat = false;
            ChatMeta meta = new ChatMeta();
            if (chatId == null || chatId.isBlank()) {
                // create a new chat ID and initialize metadata
                chatId = UUID.randomUUID().toString();
                meta = service.initializeChat(username, chatId, query);
                isNewChat = true;
            }

            SearchChatResponse searchResponse = service.search(username, chatId, query, requiredResults, neighbours, useContextual, isOptamark, isPreferredVendor, companyNames);
            if (searchResponse == null) {
                return ResponseEntity.notFound().build();
            }

            String chatIdHeader = isNewChat ? meta.getChatId() : chatId;
            ResponseEntity.BodyBuilder responseBuilder = ResponseEntity.ok()
                    .header("Chat-Id", chatIdHeader)
                    .header("Access-Control-Expose-Headers", "Chat-Id, Chat-Title, Chat-Created-At");

            if (isNewChat) {
                responseBuilder
                        .header("Chat-Title", meta.getTitle())
                        .header("Chat-Created-At", meta.getCreatedAt().toString());
            }
            return responseBuilder.body(searchResponse);
        } catch (IllegalArgumentException e) {
            logger.error("Invalid argument provided: {}", e.getMessage());
            return ResponseEntity.badRequest().body("Invalid input: " + e.getMessage());
        } catch (Exception e) {
            logger.error("Unexpected error: {}", e.getMessage());
            return ResponseEntity.status(500).body("An unexpected error occurred");
        }
    }

    @GetMapping("/search/product/spc")
    public ResponseEntity<?> searchBySpc(
            @RequestHeader("X-Username") String username,
            @RequestHeader(value = "chatId", required = false) String chatId,
            @RequestParam final String spc) {

        boolean isNewChat = false;
        ChatMeta meta = new ChatMeta();
        if (chatId == null || chatId.isBlank()) {
            chatId = UUID.randomUUID().toString();
            meta = service.initializeChat(username, chatId, "Search by SPC: " + spc);
            isNewChat = true;
        }

        SearchChatResponse searchResponse = service.searchBySpc(username, chatId, spc);
        if (searchResponse == null) {
            return ResponseEntity.notFound().build();
        }

        String chatIdHeader = isNewChat ? meta.getChatId() : chatId;
        ResponseEntity.BodyBuilder responseBuilder = ResponseEntity.ok()
                .header("Chat-Id", chatIdHeader)
                .header("Access-Control-Expose-Headers", "Chat-Id, Chat-Title, Chat-Created-At");

        if (isNewChat) {
            responseBuilder
                    .header("Chat-Title", meta.getTitle())
                    .header("Chat-Created-At", meta.getCreatedAt().toString());
        }
        return responseBuilder.body(searchResponse);
    }

    @GetMapping("/search/product/item-num")
    public ResponseEntity<?> searchByItemNum(
            @RequestHeader("X-Username") String username,
            @RequestHeader(value = "chatId", required = false) String chatId,
            @RequestParam final String itemNum) {

        boolean isNewChat = false;
        ChatMeta meta = new ChatMeta();
        if (chatId == null || chatId.isBlank()) {
            chatId = UUID.randomUUID().toString();
            meta = service.initializeChat(username, chatId, "Search by itemNum: " + itemNum);
            isNewChat = true;
        }

        SearchChatResponse searchResponse = service.searchByItemNum(username, chatId, itemNum);
        if (searchResponse == null) {
            return ResponseEntity.notFound().build();
        }

        String chatIdHeader = isNewChat ? meta.getChatId() : chatId;
        ResponseEntity.BodyBuilder responseBuilder = ResponseEntity.ok()
                .header("Chat-Id", chatIdHeader)
                .header("Access-Control-Expose-Headers", "Chat-Id, Chat-Title, Chat-Created-At");

        if (isNewChat) {
            responseBuilder
                    .header("Chat-Title", meta.getTitle())
                    .header("Chat-Created-At", meta.getCreatedAt().toString());
        }
        return responseBuilder.body(searchResponse);
    }
}
