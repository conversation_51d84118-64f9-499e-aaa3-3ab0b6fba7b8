package com.optahub.awi.service.chatbot.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class SageProduct {

    private String categories;
    private String startNum;
    private String maxRecs;
    private String maxTotalItems;
   private String extraReturnFields = "ITEMNUM,CATEGORY,DESCRIPTION,COLORS,NETPRICES,DIMENSIONS";

}
