package com.optahub.awi.service.chatbot.mapper;

import java.util.List;

import org.mapstruct.Mapper;

import com.optahub.awi.service.chatbot.entity.Sku;
import com.optahub.awi.service.chatbot.model.SkuModel;

@Mapper(componentModel = "spring")
public interface SkuMapper {

    SkuModel toModel(Sku sku);

    List<SkuModel> toModels(List<Sku> sku);

    Sku toSku(SkuModel sku);

    List<Sku> toSkus(List<SkuModel> sku);
}
