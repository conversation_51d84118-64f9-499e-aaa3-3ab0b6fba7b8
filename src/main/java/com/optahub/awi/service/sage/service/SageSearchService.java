package com.optahub.awi.service.sage.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.optahub.awi.service.rest.data.aggregate.NameObject;
import com.optahub.awi.service.rest.data.sage.search.SageSearchRequest;
import com.optahub.awi.service.rest.data.search.SearchResponse;
import com.optahub.awi.service.rest.data.search.SearchResponseData;
import com.optahub.awi.service.rest.data.search.SearchResponseItemContainer;
import com.optahub.awi.service.sage.data.SageListItem;
import com.optahub.awi.service.sage.data.SageSearchResponse;
import com.optahub.awi.service.sage.data.SearchResponseItem;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

@Service
public class SageSearchService {

    private final SageConnectService connectService;
    private final ObjectMapper objectMapper;

    @Autowired
    public SageSearchService(final SageConnectService connectService, final ObjectMapper objectMapper) {

        this.objectMapper = objectMapper;
        this.connectService = connectService;
    }

    public SearchResponse searchSage(final SageSearchRequest searchRequest) {

        final SearchResponse searchResponse = new SearchResponse();
        try {
            final com.optahub.awi.service.sage.data.SageSearchRequest apiRequest = new com.optahub.awi.service.sage.data.SageSearchRequest();
            apiRequest.setItemNumExact(searchRequest.getItemNumExact() != null ? Boolean.valueOf(searchRequest.getItemNumExact()) : Boolean.FALSE);

            if(apiRequest.isItemNumExact() && StringUtils.isNotBlank(searchRequest.getSpc())) {
                apiRequest.setItemNum(searchRequest.getSpc());
            } else if(StringUtils.isNotBlank(searchRequest.getSpc())) {
                if(searchRequest.getSpc().startsWith("S:")) {
                    apiRequest.setSuppId(Integer.valueOf(searchRequest.getSpc().substring(2)));
                } else {
                    apiRequest.setSpc(searchRequest.getSpc());
                }
            }

            if(StringUtils.isNotBlank(searchRequest.getCategory())) {
                apiRequest.setCategories(searchRequest.getCategory());
            }
            if(StringUtils.isNotBlank(searchRequest.getColors())) {
                apiRequest.setColors(searchRequest.getColors());
            }
            if(StringUtils.isNotBlank(searchRequest.getKeywords())) {
                apiRequest.setKeywords(searchRequest.getKeywords());
            }
            if(StringUtils.isNotBlank(searchRequest.getLineName())) {
                apiRequest.setLineName(searchRequest.getLineName());
            }
            if(StringUtils.isNotBlank(searchRequest.getThemes())) {
                apiRequest.setThemes(searchRequest.getThemes());
            }
            if(StringUtils.isNotBlank(searchRequest.getPrefGroups())) {
                apiRequest.setPrefGroups(searchRequest.getPrefGroups());
            }
            if(StringUtils.isNotBlank(searchRequest.getPriceLow())) {
                apiRequest.setPriceLow(searchRequest.getPriceLow());
            }
            if(StringUtils.isNotBlank(searchRequest.getPriceHigh())) {
                apiRequest.setPriceHigh(searchRequest.getPriceHigh());
            }

            apiRequest.setQty(searchRequest.getQuantity() != null ? Integer.valueOf(searchRequest.getQuantity()) : 0);
            apiRequest.setSort(searchRequest.getSort());
            apiRequest.setMaxRecs(searchRequest.getMaxRecs() != null ? Integer.valueOf(searchRequest.getMaxRecs()) : 0);
            apiRequest.setMaxTotalItems(searchRequest.getMaxTotalItems() != null ? Integer.valueOf(searchRequest.getMaxTotalItems()) : 0);
            apiRequest.setStartNum(searchRequest.getStartNum() != null ? Integer.valueOf(searchRequest.getStartNum()) : 0);
            apiRequest.setExtraReturnFields("LINE,SUPPLIER,COMPANY,ITEMNUM,SUPPID,CATEGORY,DESCRIPTION,COLORS,THEMES,NETPRICES,PRODTIME,DIMENSIONS");

            final JSONObject searchRequestJson = new JSONObject();
            searchRequestJson.put("search", new JSONObject(objectMapper.writeValueAsString(apiRequest)));

            final Optional<String> sageResponse = connectService.doPost(searchRequestJson, 103);
            if (sageResponse.isEmpty()) {
                searchResponse.setMessage("Unable to fetch search response from SAGE.");
            } else {
                final SageSearchResponse sageResp = objectMapper.readValue(sageResponse.get(), SageSearchResponse.class);
                final SearchResponseData sageResponseData = new SearchResponseData();
                if (sageResp.isOk() && !CollectionUtils.isEmpty(sageResp.getProducts())) {
                    SearchResponseItemContainer itemContainer = new SearchResponseItemContainer();
                    final List<com.optahub.awi.service.rest.data.search.SearchResponseItem> itemList = new ArrayList<>();
                    int count = 0;
                    for (SearchResponseItem respItem : sageResp.getProducts()) {
                        com.optahub.awi.service.rest.data.search.SearchResponseItem item = new com.optahub.awi.service.rest.data.search.SearchResponseItem();
                        item.setCount(String.valueOf(count));
                        item.setCategory(respItem.getCategory());
                        item.setColors(respItem.getColors());
                        item.setItemNum(respItem.getItemNum());
                        item.setDescription(respItem.getDescription());
                        item.setPrc(respItem.getPrc());
                        item.setNet(respItem.getNet());
                        item.setCompanyName(respItem.getSupplier());
                        item.setLineName(respItem.getLine());
                        item.setPrName(respItem.getName());
                        item.setProdTime(String.valueOf(respItem.getProdTime()));
                        item.setSpc(respItem.getSpc());
                        item.setSuppId(String.valueOf(respItem.getSuppId()));
                        item.setThumbPicLink(respItem.getThumbPic());
                        item.setProductId(String.valueOf(respItem.getProdEId()));

                        itemList.add(item);

                        count++;
                    }

                    itemContainer.setItem(itemList);
                    sageResponseData.setItems(itemContainer);
                    sageResponseData.setSuccess("1");
                    sageResponseData.setTotalFound(String.valueOf(sageResp.getTotalFound()));

                } else {
                    searchResponse.setMessage("No valid response from SAGE.");
                    sageResponseData.setErrMsg(Arrays.asList(sageResp.getErrMsg()));
                }
                searchResponse.setData(sageResponseData);
            }
        }catch (final JsonProcessingException ex) {
            searchResponse.setMessage("Unable to parse response from SAGE.");

        }

        return searchResponse;
    }
}
