package com.optahub.awi.service.sage.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.optahub.awi.service.rest.data.aggregate.AggregateResponse;
import com.optahub.awi.service.rest.data.aggregate.NameObject;
import com.optahub.awi.service.sage.data.SageListItem;
import com.optahub.awi.service.sage.data.SageListResponse;
import com.optahub.awi.service.sage.data.constants.SageListType;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
public class SageListService {

    private final SageConnectService connectService;
    private final ObjectMapper objectMapper;

    @Autowired
    public SageListService(final SageConnectService connectService, final ObjectMapper objectMapper) {

        this.objectMapper = objectMapper;
        this.connectService = connectService;
    }

    public AggregateResponse getListData(final SageListType listType) {
        final JSONObject listRequestJson = new JSONObject();
        listRequestJson.put("listType", listType.getValue());

        final Optional<String> sageResponse = connectService.doPost(listRequestJson, 101);
        AggregateResponse aggregateResponse = new AggregateResponse();
        if (sageResponse.isEmpty()) {
            aggregateResponse.setMessage("Unable to fetch list data from SAGE.");
        } else {
            try {
                final SageListResponse listResponse = objectMapper.readValue(sageResponse.get(), SageListResponse.class);
                if(listResponse.isOk() && !CollectionUtils.isEmpty(listResponse.getItems())) {
                    final List<NameObject> listNameObject = new ArrayList<>();
                    for(SageListItem listItem : listResponse.getItems()) {
                        NameObject nameObject = new NameObject();
                        nameObject.setName(listItem.getName());
                        listNameObject.add(nameObject);
                    }

                    aggregateResponse.setData(listNameObject);
                } else {
                    aggregateResponse.setMessage("No valid response from SAGE.");
                }
            } catch (final JsonProcessingException ex) {
                aggregateResponse.setMessage("Unable to parse list response from SAGE.");

            }
        }

        return aggregateResponse;
    }
}
