package com.optahub.awi.service.sage.data;

import com.fasterxml.jackson.annotation.JsonProperty;

public class SearchResponseItem {

    @JsonProperty("prodEId")
    private int prodEId;
    @JsonProperty("spc")
    private String spc;
    @JsonProperty("name")
    private String name;
    @JsonProperty("itemNum")
    private String itemNum;
    @JsonProperty("category")
    private String category;
    @JsonProperty("description")
    private String description;
    @JsonProperty("colors")
    private String colors;
    @JsonProperty("themes")
    private String themes;
    @JsonProperty("line")
    private String line;
    @JsonProperty("supplier")
    private String supplier;
    @JsonProperty("prc")
    private String prc;
    @JsonProperty("net")
    private String net;
    @JsonProperty("prodTime")
    private String prodTime;
    @JsonProperty("thumbPic")
    private String thumbPic;
    @JsonProperty("suppID")
    private int suppId;

    public int getProdEId() {
        return prodEId;
    }

    public void setProdEId(int prodEId) {
        this.prodEId = prodEId;
    }

    public String getSpc() {
        return spc;
    }

    public void setSpc(String spc) {
        this.spc = spc;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getItemNum() {
        return itemNum;
    }

    public void setItemNum(String itemNum) {
        this.itemNum = itemNum;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getColors() {
        return colors;
    }

    public void setColors(String colors) {
        this.colors = colors;
    }

    public String getThemes() {
        return themes;
    }

    public void setThemes(String themes) {
        this.themes = themes;
    }

    public String getLine() {
        return line;
    }

    public void setLine(String line) {
        this.line = line;
    }

    public String getSupplier() {
        return supplier;
    }

    public void setSupplier(String supplier) {
        this.supplier = supplier;
    }

    public String getPrc() {
        return prc;
    }

    public void setPrc(String prc) {
        this.prc = prc;
    }

    public String getNet() {
        return net;
    }

    public void setNet(String net) {
        this.net = net;
    }

    public String getProdTime() {
        return prodTime;
    }

    public void setProdTime(String prodTime) {
        this.prodTime = prodTime;
    }

    public String getThumbPic() {
        return thumbPic;
    }

    public void setThumbPic(String thumbPic) {
        this.thumbPic = thumbPic;
    }

    public int getSuppId() {
        return suppId;
    }

    public void setSuppId(int suppId) {
        this.suppId = suppId;
    }
}
