package com.optahub.awi.service.sage.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.optahub.awi.service.rest.data.aggregate.NameObject;
import com.optahub.awi.service.rest.data.detail.ProductDataResponseWrapper;
import com.optahub.awi.service.rest.data.detail.ProductDetail;
import com.optahub.awi.service.rest.data.detail.SupplierDetail;
import com.optahub.awi.service.rest.data.sage.search.SageSearchRequest;
import com.optahub.awi.service.rest.data.search.SearchResponse;
import com.optahub.awi.service.rest.data.search.SearchResponseData;
import com.optahub.awi.service.rest.data.search.SearchResponseItemContainer;
import com.optahub.awi.service.sage.data.SageListItem;
import com.optahub.awi.service.sage.data.SageListResponse;
import com.optahub.awi.service.sage.data.SageSearchResponse;
import com.optahub.awi.service.sage.data.SearchResponseItem;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

@Service
public class SageProductService {

    private final SageConnectService connectService;
    private final ObjectMapper objectMapper;

    @Autowired
    public SageProductService(final SageConnectService connectService, final ObjectMapper objectMapper) {

        this.objectMapper = objectMapper;
        this.connectService = connectService;
    }

    public ProductDataResponseWrapper getProduct(final String productId) {

        final JSONObject productRequestJson = new JSONObject();
        productRequestJson.put("prodEId", productId);
        productRequestJson.put("includeSuppInfo", Boolean.TRUE);

        final ProductDataResponseWrapper responseWrapper = new ProductDataResponseWrapper();
        final Optional<String> sageResponse = connectService.doPost(productRequestJson, 105);

        if (sageResponse.isPresent()) {
            final ProductDetail response = new ProductDetail();
            final JSONObject sageJson = new JSONObject(sageResponse.get());
            final JSONObject productResponse = sageJson.getJSONObject("product");
            response.setProductId(String.valueOf(productResponse.optInt("prodEid")));
            response.setCategory(productResponse.optString("category"));
            response.setSuppId(String.valueOf(productResponse.optInt("category", 0)));
            response.setLineName(productResponse.optString("lineName"));
            response.setCatPage(Arrays.asList(String.valueOf(productResponse.optInt("catPage", 0))));
            response.setCatYear(String.valueOf(productResponse.optInt("catYear", 0)));
            response.setItemNum(productResponse.optString("itemNum"));
            response.setSpc(productResponse.optString("spc"));
            response.setPrName(productResponse.optString("prName"));
            response.setDescription(productResponse.optString("description"));
            response.setDimensions(productResponse.optString("dimensions"));
            response.setKeywords(productResponse.optString("keywords"));
            response.setColors(productResponse.optString("colors"));
            response.setThemes(productResponse.optString("themes"));

            //Pic link and name.
            this.populatePicData(response, productResponse);
            this.populateQuantityAndPrice(response, productResponse);

            response.setCurrency(productResponse.optString("currency"));
            response.setPriceAdjustMsg(Arrays.asList(productResponse.optString("priceAdjustMsg")));
            response.setMadeInCountry(productResponse.optString("madeInCountry"));
            response.setAssembledInCountry(productResponse.optString("assembledInCountry"));
            response.setDecoratedInCountry(productResponse.optString("decoratedInCountry"));
            response.setRecyclable(String.valueOf(productResponse.optInt("recyclable", 0)));
            response.setNewProduct(String.valueOf(productResponse.optInt("newProduct", 0)));
            response.setEnvFriendly(String.valueOf(productResponse.optInt("envFriendly", 0)));
            response.setFood(String.valueOf(productResponse.optInt("food", 0)));
            response.setClothing(String.valueOf(productResponse.optInt("clothing", 0)));
            response.setVerified(String.valueOf(productResponse.optInt("verified", 0)));
            response.setProductCompliance(productResponse.optString("productCompliance"));
            response.setWarningLbl(productResponse.optString("warningLbl"));
            response.setProductComplianceMemo(productResponse.optString("productComplianceMemo"));
            response.setImprintArea(productResponse.optString("imprintLoc"));
            response.setSecondImprintArea(productResponse.optString("secondImprintLoc"));
            response.setDecorationMethod(productResponse.optString("decorationMethod"));
            response.setDecorationNotOffered(String.valueOf(productResponse.optInt("decorationNotOffered", 0)));
            response.setSetupChg(productResponse.optString("setupChg"));
            response.setSetupChgCode(productResponse.optString("setupChgCode"));
            response.setRepeatSetupChg(productResponse.optString("repeatSetupChg"));
            response.setRepeatSetupChgCode(productResponse.optString("repeatSetupChgCode"));
            response.setScreenChg(productResponse.optString("screenChg"));
            response.setScreenChgCode(productResponse.optString("screenChgCode"));
            response.setDieChg(productResponse.optString("dieChg"));
            response.setPlateChg(productResponse.optString("plateChg"));
            response.setAddClrChg(productResponse.optString("addClrChg"));
            response.setAddClrChgCode(productResponse.optString("addClrChgCode"));
            response.setAddClrRunChgCode(productResponse.optString("addClrRunChgCode"));

            final JSONArray addClrRunArray = productResponse.getJSONArray("addClrRunChg");
            response.setAddClrRunChg1(addClrRunArray.getString(0));
            response.setAddClrRunChg2(addClrRunArray.getString(1));
            response.setAddClrRunChg3(addClrRunArray.getString(2));
            response.setAddClrRunChg4(addClrRunArray.getString(3));
            response.setAddClrRunChg5(addClrRunArray.getString(4));
            response.setAddClrRunChg6(addClrRunArray.getString(5));

            response.setPriceIncludes(productResponse.optString("priceIncludes"));
            response.setPackageStr(productResponse.optString("package"));
            response.setWeightPerCarton(String.valueOf(productResponse.optFloat("weightPerCarton", 0f)));
            response.setUnitsPerCarton(String.valueOf(productResponse.optInt("unitsPerCarton", 0)));
            response.setCartonW(String.valueOf(productResponse.optInt("cartonW",0)));
            response.setCartonH(String.valueOf(productResponse.optInt("cartonH",0)));
            response.setCartonL(String.valueOf(productResponse.optInt("cartonL",0)));
            response.setProdTime(productResponse.optString("prodTime"));
            response.setShipPointCountry(productResponse.optString("shipPointCountry"));
            response.setShipPointZip(productResponse.optString("shipPointZip"));
            response.setExpDate(productResponse.optString("expDate"));
            response.setComment(productResponse.optString("comment"));
            response.setSuppId(productResponse.optString("suppId"));

            final JSONObject supplierJSON = productResponse.getJSONObject("supplier");

            SupplierDetail supplierDetail = new SupplierDetail();
            supplierDetail.setSuppID(supplierJSON.optString("suppId"));
            supplierDetail.setCoName(supplierJSON.optString("coName"));
            supplierDetail.setLineName(supplierJSON.optString("lineName"));
            supplierDetail.setmAddr(supplierJSON.optString("mAddr"));
            supplierDetail.setmCity(supplierJSON.optString("mCity"));
            supplierDetail.setmZip(supplierJSON.optString("mZip"));
            supplierDetail.setmState(supplierJSON.optString("mState"));
            supplierDetail.setmCountry(supplierJSON.optString("mCountry"));
            supplierDetail.setTel(supplierJSON.optString("tel"));
            supplierDetail.setTollFreeTel(supplierJSON.optString("tollFreeTel"));
            supplierDetail.setFax(supplierJSON.optString("fax"));
            supplierDetail.setEmail(supplierJSON.optString("email"));
            supplierDetail.setWeb(supplierJSON.optString("web"));
            supplierDetail.setArtContactEmail(supplierJSON.optString("artContactEmail"));
            supplierDetail.setCatYear(supplierJSON.optString("catYear"));
            supplierDetail.setCatExpOn(supplierJSON.optString("catExpOn"));
            supplierDetail.setCatCurrency(supplierJSON.optString("catCurrency"));
            supplierDetail.setComment(supplierJSON.optString("comment"));
            supplierDetail.setPrefGroupIds(supplierJSON.optString("prefGroupIds"));
            supplierDetail.setPrefGroup(supplierJSON.optString("prefGroups"));
            supplierDetail.setsAddr(supplierJSON.optString("sAddr"));
            supplierDetail.setsCity(supplierJSON.optString("sCity"));
            supplierDetail.setsZip(supplierJSON.optString("sZip"));
            supplierDetail.setsState(supplierJSON.optString("sState"));
            supplierDetail.setsCountry(supplierJSON.optString("sCountry"));
            supplierDetail.setPersCustNum(supplierJSON.optString("persCustNum"));
            supplierDetail.setContactName(supplierJSON.optString("contactName"));
            supplierDetail.setArtContactName(supplierJSON.optString("artContactName"));


            response.setSupplierDetail(supplierDetail);

            responseWrapper.setData(response);
        } else {
            responseWrapper.setMessage("Unable to get product details.");
        }

        return responseWrapper;
    }

    private void populateQuantityAndPrice (final ProductDetail detail, final JSONObject productResponse) {
        final JSONArray qtyArray = productResponse.getJSONArray("qty");
        detail.setQty1(qtyArray.getString(0));
        detail.setQty2(qtyArray.getString(1));
        detail.setQty3(qtyArray.getString(2));
        detail.setQty4(qtyArray.getString(3));
        detail.setQty5(qtyArray.getString(4));
        detail.setQty6(qtyArray.getString(5));

        final JSONArray priceArray = productResponse.getJSONArray("prc");

        detail.setPrc1(priceArray.getString(0));
        detail.setPrc2(priceArray.getString(1));
        detail.setPrc3(priceArray.getString(2));
        detail.setPrc4(priceArray.getString(3));
        detail.setPrc5(priceArray.getString(4));

        final JSONArray catPriceArray = productResponse.getJSONArray("catPrc");

        detail.setCatPrc1(catPriceArray.getString(0));
        detail.setCatPrc2(catPriceArray.getString(1));
        detail.setCatPrc3(catPriceArray.getString(2));
        detail.setCatPrc4(catPriceArray.getString(3));
        detail.setCatPrc5(catPriceArray.getString(4));

        final JSONArray netPriceArray = productResponse.getJSONArray("net");

        detail.setNet1(netPriceArray.getString(0));
        detail.setNet2(netPriceArray.getString(1));
        detail.setNet3(netPriceArray.getString(2));
        detail.setNet4(netPriceArray.getString(3));
        detail.setNet5(netPriceArray.getString(4));

        final JSONArray unitPieceArray = productResponse.getJSONArray("piecesPerUnit");

        detail.setPiecesPerUnit1(unitPieceArray.getString(0));
        detail.setPiecesPerUnit2(unitPieceArray.getString(1));
        detail.setPiecesPerUnit3(unitPieceArray.getString(2));
        detail.setPiecesPerUnit4(unitPieceArray.getString(3));
        detail.setPiecesPerUnit5(unitPieceArray.getString(4));
        detail.setPiecesPerUnit6(unitPieceArray.getString(5));

    }

    private void populatePicData (final ProductDetail detail, final JSONObject productResponse) {
        final List<JSONObject> logoImages = new ArrayList<>();
        final JSONArray picArray = productResponse.getJSONArray("pics");
        for(int i = 0 ; i < picArray.length() ; i++) {
            final JSONObject picObject = picArray.getJSONObject(i);

            if(i == 0) {
                detail.setPicLink(picObject.optString("url"));
                detail.setPicName(detail.getSpc() + " Main");
            }

            if(picObject.optInt("hasLogo", 0) == 1) {
                logoImages.add(picObject);
            }
        }

        for(final JSONObject logoObject : logoImages) {
            detail.setPicLink(logoObject.optString("url"));

            if(StringUtils.isNotBlank(logoObject.optString("caption", ""))) {
                detail.setPicName(detail.getSpc() + " " + logoObject.optString("caption", ""));
                return;
            }
        }
    }
}
