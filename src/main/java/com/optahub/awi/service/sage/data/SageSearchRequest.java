package com.optahub.awi.service.sage.data;

public class SageSearchRequest {

    private String categories;
    private String keywords;
    private String colors;
    private String themes;
    private String quickSearch;
    private String spc;
    private String itemNum;
    private boolean itemNumExact;
    private int qty;
    private String prefGroups;
    private int suppId;
    private String lineName;
    private String sort;
    private int maxTotalItems;
    private int startNum;
    private int maxRecs;
    private String priceLow;
    private String priceHigh;

    private String extraReturnFields;

    public String getCategories() {
        return categories;
    }

    public void setCategories(String categories) {
        this.categories = categories;
    }

    public String getKeywords() {
        return keywords;
    }

    public void setKeywords(String keywords) {
        this.keywords = keywords;
    }

    public String getColors() {
        return colors;
    }

    public void setColors(String colors) {
        this.colors = colors;
    }

    public String getThemes() {
        return themes;
    }

    public void setThemes(String themes) {
        this.themes = themes;
    }

    public String getQuickSearch() {
        return quickSearch;
    }

    public void setQuickSearch(String quickSearch) {
        this.quickSearch = quickSearch;
    }

    public String getSpc() {
        return spc;
    }

    public void setSpc(String spc) {
        this.spc = spc;
    }

    public String getItemNum() {
        return itemNum;
    }

    public void setItemNum(String itemNum) {
        this.itemNum = itemNum;
    }

    public boolean isItemNumExact() {
        return itemNumExact;
    }

    public void setItemNumExact(boolean itemNumExact) {
        this.itemNumExact = itemNumExact;
    }

    public int getQty() {
        return qty;
    }

    public void setQty(int qty) {
        this.qty = qty;
    }

    public String getPrefGroups() {
        return prefGroups;
    }

    public void setPrefGroups(String prefGroups) {
        this.prefGroups = prefGroups;
    }

    public int getSuppId() {
        return suppId;
    }

    public void setSuppId(int suppId) {
        this.suppId = suppId;
    }

    public String getLineName() {
        return lineName;
    }

    public void setLineName(String lineName) {
        this.lineName = lineName;
    }

    public String getSort() {
        return sort;
    }

    public void setSort(String sort) {
        this.sort = sort;
    }

    public int getMaxTotalItems() {
        return maxTotalItems;
    }

    public void setMaxTotalItems(int maxTotalItems) {
        this.maxTotalItems = maxTotalItems;
    }

    public int getStartNum() {
        return startNum;
    }

    public void setStartNum(int startNum) {
        this.startNum = startNum;
    }

    public int getMaxRecs() {
        return maxRecs;
    }

    public void setMaxRecs(int maxRecs) {
        this.maxRecs = maxRecs;
    }

    public String getExtraReturnFields() {
        return extraReturnFields;
    }

    public void setExtraReturnFields(String extraReturnFields) {
        this.extraReturnFields = extraReturnFields;
    }

    public String getPriceLow() {
        return priceLow;
    }

    public void setPriceLow(String priceLow) {
        this.priceLow = priceLow;
    }

    public String getPriceHigh() {
        return priceHigh;
    }

    public void setPriceHigh(String priceHigh) {
        this.priceHigh = priceHigh;
    }
}
