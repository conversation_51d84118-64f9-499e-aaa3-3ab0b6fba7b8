package com.optahub.awi.service.sage.data;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class SageSearchResponse {

    private boolean ok;
    private int totalFound;
    private int errNum;
    private String errMsg;
    private String searchResponseMsg;
    private List<SearchResponseItem> products;

    public boolean isOk() {
        return ok;
    }

    public void setOk(boolean ok) {
        this.ok = ok;
    }

    public int getTotalFound() {
        return totalFound;
    }

    public void setTotalFound(int totalFound) {
        this.totalFound = totalFound;
    }

    public int getErrNum() {
        return errNum;
    }

    public void setErrNum(int errNum) {
        this.errNum = errNum;
    }

    public String getErrMsg() {
        return errMsg;
    }

    public void setErrMsg(String errMsg) {
        this.errMsg = errMsg;
    }

    public String getSearchResponseMsg() {
        return searchResponseMsg;
    }

    public void setSearchResponseMsg(String searchResponseMsg) {
        this.searchResponseMsg = searchResponseMsg;
    }

    public List<SearchResponseItem> getProducts() {
        return products;
    }

    public void setProducts(List<SearchResponseItem> products) {
        this.products = products;
    }
}
