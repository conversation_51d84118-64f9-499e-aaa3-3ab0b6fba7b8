package com.optahub.awi.service.sage.service;

import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.Map;
import java.util.Optional;

@Service
public class SageConnectService {

    private final RestTemplate restTemplate;

    private final String sageConnectUrl;

    private final JSONObject jsonObject;


    @Autowired
    public SageConnectService(final RestTemplate restTemplate,
                              @Value("${sage.auth.id:231324}") final Integer authId,
                              @Value("${sage.auth.key:b0c02d1d28d7a18e2834a7bb1a57684f}") final String authKey,
                              @Value("${sage.login.id:tarang}") final String loginId,
                              @Value("${sage.connect.url:https://www.promoplace.com/ws/ws.dll/ConnectAPI}") final String sageConnectUrl) {

        this.restTemplate = restTemplate;
        this.sageConnectUrl = sageConnectUrl;

        this.jsonObject = new JSONObject();
        this.jsonObject.put("apiVer", 130);

        final JSONObject authJsonObj = new JSONObject();
        authJsonObj.put("acctId", authId);
        authJsonObj.put("key", authKey);
        authJsonObj.put("loginId", loginId);

        this.jsonObject.put("auth", authJsonObj);
    }

    public Optional<String> doPost(final JSONObject requestJson, final int serviceId) {
        final Map<String, Object> requestPayload = this.jsonObject.toMap();
        requestPayload.putAll(requestJson.toMap());
        requestPayload.put("serviceId", serviceId);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> request = new HttpEntity<>(new JSONObject(requestPayload).toString(), headers);

        ResponseEntity<String> response = restTemplate
                .exchange(this.sageConnectUrl, HttpMethod.POST, request, String.class);

        if(!response.getStatusCode().is2xxSuccessful()) {
            //something is not correct
            return Optional.empty();
        }

        return Optional.of(response.getBody());
    }
}
