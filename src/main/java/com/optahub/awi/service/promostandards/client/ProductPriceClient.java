package com.optahub.awi.service.promostandards.client;

import com.optahub.awi.service.elastic.data.supplier.SupplierData;
import com.optahub.awi.service.promostandards.data.constant.PriceType;
import com.optahub.awi.service.promostandards.data.service.ServiceDescription;
import org.apache.commons.lang3.StringUtils;
import org.optahub.promostandards.wsdl.pricing.*;
import org.springframework.oxm.jaxb.Jaxb2Marshaller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.ws.client.core.support.WebServiceGatewaySupport;
import org.springframework.ws.soap.client.core.SoapActionCallback;

import java.util.List;
import java.util.Optional;

public class ProductPriceClient extends WebServiceGatewaySupport {

    private static final String API_CODE = "PPC";
    private static final String API_VERSION = "1.0.0";
    private String apiKey;
    private String apiSecret;

    public ProductPriceClient(final SupplierData supplierConfig,
                              final Jaxb2Marshaller marshaller,
                              final PromoStandardsServiceClient serviceClient) {

        this.setMarshaller(marshaller);
        this.setUnmarshaller(marshaller);
        this.setDefaultUri(this.getUrl(supplierConfig.getCode(), serviceClient));

        this.apiKey = supplierConfig.getKey();
        this.apiSecret = supplierConfig.getSecret();
    }

    public Optional<GetConfigurationAndPricingResponse> getProductPrice(final String productId,
                                                                        final String fobId,
                                                                        final PriceType priceType) {

        final GetConfigurationAndPricingRequest request = new GetConfigurationAndPricingRequest();
        request.setId(this.apiKey);
        request.setWsVersion(API_VERSION);
        request.setPassword(this.apiSecret);
        request.setProductId(productId);
        request.setFobId(fobId);
        request.setPriceType(priceType.getPriceType());
        request.setCurrency(CurrencyCodeType.USD);
        request.setLocalizationCountry("US");
        request.setLocalizationLanguage("en");
        request.setConfigurationType(priceType.getConfigurationType());

        try {
            final GetConfigurationAndPricingResponse response = (GetConfigurationAndPricingResponse) getWebServiceTemplate()
                    .marshalSendAndReceive(request, new SoapActionCallback("getConfigurationAndPricing"));

            return Optional.ofNullable(response);
        } catch (final HttpClientErrorException ex) {
            System.out.println("Unable to get product prices for "+productId);
            ex.printStackTrace();
        }

        return Optional.empty();
    }

    public String getFobIdForProductAndCountry(final String productId,
                                               final String countryCode) {

        final Optional<GetFobPointsResponse> response = this.getProductFOB(productId);

        if (response.isPresent()) {
            final GetFobPointsResponse fobResponse = response.get();
            if (fobResponse.getFobPointArray() != null
                    && !(CollectionUtils.isEmpty(fobResponse.getFobPointArray().getFobPoint()))) {
                final List<FobPoint> fobList = fobResponse.getFobPointArray().getFobPoint();
                for (FobPoint fob : fobList) {
                    if (countryCode.equalsIgnoreCase(fob.getFobCountry())) {
                        return fob.getFobId();
                    }
                }

                return fobList.get(0).getFobId();
            }
        }

        return "0";
    }

    private Optional<GetFobPointsResponse> getProductFOB(final String productId) {

        final GetFobPointsRequest request = new GetFobPointsRequest();
        request.setId(this.apiKey);
        request.setWsVersion(API_VERSION);
        request.setPassword(this.apiSecret);
        request.setProductId(productId);
        request.setLocalizationCountry("US");
        request.setLocalizationLanguage("en");

        try {
            final GetFobPointsResponse response = (GetFobPointsResponse) getWebServiceTemplate()
                    .marshalSendAndReceive(request, new SoapActionCallback("getFobPoints"));

            return Optional.ofNullable(response);
        } catch (final HttpClientErrorException ex) {
            System.out.println("Unable to get product fob points for "+productId);
            ex.printStackTrace();
        }

        return Optional.empty();
    }

    private String getUrl(final String companyCode, final PromoStandardsServiceClient serviceClient) {
        final ServiceDescription[] descriptions = serviceClient.getEndpoints(companyCode, API_CODE);
        if(descriptions != null || descriptions.length > 0) {
            for(ServiceDescription description : descriptions) {
                if(StringUtils.equals(API_VERSION, description.getService().getVersion())) {
                    return description.getUrl();
                }
            }
        }

        return StringUtils.EMPTY;
    }
}
