package com.optahub.awi.service.promostandards.data.inventory;

import java.util.List;

public class LocationInventory {
    private String locationName;
    private String locationZip;
    private String locationCountry;

    private String availableQuantity;

    private Double availableQuantityNumeric;

    private String uom;

    private boolean mainPart;

    private List<LocationFutureInventory> futureList;

    public String getLocationName() {
        return locationName;
    }

    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    public String getLocationZip() {
        return locationZip;
    }

    public void setLocationZip(String locationZip) {
        this.locationZip = locationZip;
    }

    public String getLocationCountry() {
        return locationCountry;
    }

    public void setLocationCountry(String locationCountry) {
        this.locationCountry = locationCountry;
    }

    public String getAvailableQuantity() {
        return availableQuantity;
    }

    public void setAvailableQuantity(String availableQuantity) {
        this.availableQuantity = availableQuantity;
    }

    public String getUom() {
        return uom;
    }

    public void setUom(String uom) {
        this.uom = uom;
    }

    public boolean isMainPart() {
        return mainPart;
    }

    public void setMainPart(boolean mainPart) {
        this.mainPart = mainPart;
    }

    public List<LocationFutureInventory> getFutureList() {
        return futureList;
    }

    public void setFutureList(List<LocationFutureInventory> futureList) {
        this.futureList = futureList;
    }

    public Double getAvailableQuantityNumeric() {
        return availableQuantityNumeric;
    }

    public void setAvailableQuantityNumeric(Double availableQuantityNumeric) {
        this.availableQuantityNumeric = availableQuantityNumeric;
    }
}
