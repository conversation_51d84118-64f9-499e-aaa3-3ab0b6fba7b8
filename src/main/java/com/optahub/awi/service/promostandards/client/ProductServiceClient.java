package com.optahub.awi.service.promostandards.client;

import com.optahub.awi.service.elastic.data.supplier.SupplierData;
import com.optahub.awi.service.promostandards.data.service.ServiceDescription;

import org.apache.commons.lang3.StringUtils;
import org.optahub.promostandards.wsdl.productservice.*;
import org.springframework.oxm.jaxb.Jaxb2Marshaller;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.ws.client.core.support.WebServiceGatewaySupport;
import org.springframework.ws.soap.client.core.SoapActionCallback;

import java.util.Optional;

public class ProductServiceClient extends WebServiceGatewaySupport {

    private static final String API_CODE = "Product";
    private static final String API_VERSION = "2.0.0";
    private String apiKey;
    private String apiSecret;

    public ProductServiceClient(final SupplierData supplierConfig,
                                final Jaxb2Marshaller marshaller,
                                final PromoStandardsServiceClient serviceClient) {

        this.setMarshaller(marshaller);
        this.setUnmarshaller(marshaller);
        this.setDefaultUri(this.getUrl(supplierConfig.getCode(), serviceClient));

        this.apiKey = supplierConfig.getKey();
        this.apiSecret = supplierConfig.getSecret();
    }

    public Optional<GetProductSellableResponse> getProductSellable() {

        final GetProductSellableRequest request = new GetProductSellableRequest();
        request.setId(this.apiKey);
        request.setWsVersion(API_VERSION);
        request.setPassword(this.apiSecret);
        request.setIsSellable(true);
        request.setLocalizationCountry("US");
        request.setLocalizationLanguage("en");




        try{
            final GetProductSellableResponse response = (GetProductSellableResponse) getWebServiceTemplate()
                    .marshalSendAndReceive(request, new SoapActionCallback("getProductSellable"));

            return Optional.ofNullable(response);

            //return Optional.ofNullable(response);
        } catch (final HttpClientErrorException ex) {
            System.out.println("Unable to get product selleable ");
            ex.printStackTrace();
        }catch (final Exception ex) {
            System.out.println("Unable to get product sellable");
            ex.printStackTrace();
        }

        return Optional.empty();
    }

    public Optional<GetProductResponse> getProductDetails(final String productId) {

        final GetProductRequest request = new GetProductRequest();
        request.setId(this.apiKey);
        request.setWsVersion(API_VERSION);
        request.setPassword(this.apiSecret);
        request.setProductId(productId);
        request.setLocalizationCountry("US");
        request.setLocalizationLanguage("en");
        try{
            final GetProductResponse response = (GetProductResponse) getWebServiceTemplate()
                            .marshalSendAndReceive(request, new SoapActionCallback("getProduct"));

            return Optional.ofNullable(response);
        } catch (final HttpClientErrorException ex) {
            System.out.println("Unable to get product details for "+productId);
            ex.printStackTrace();
        }catch (final Exception ex) {
            System.out.println("Unable to get product details for "+productId);
            ex.printStackTrace();
        }

        return Optional.empty();
    }


    private String getUrl(final String companyCode, final PromoStandardsServiceClient serviceClient) {
        final ServiceDescription[] descriptions = serviceClient.getEndpoints(companyCode, API_CODE);
        if(descriptions != null || descriptions.length > 0) {
            for(ServiceDescription description : descriptions) {
                if(StringUtils.equals(API_VERSION, description.getService().getVersion())) {
                    return description.getUrl();
                }
            }
        }
        return StringUtils.EMPTY;
    }
}
