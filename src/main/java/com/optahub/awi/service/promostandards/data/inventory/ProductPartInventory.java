package com.optahub.awi.service.promostandards.data.inventory;

import java.util.List;

public class ProductPartInventory {
    private String partId;
    private List<LocationInventory> locationInventoryList;

    private String partColor;
    private String description;

    private boolean optional;

    public String getPartId() {
        return partId;
    }

    public void setPartId(String partId) {
        this.partId = partId;
    }

    public List<LocationInventory> getLocationInventoryList() {
        return locationInventoryList;
    }

    public void setLocationInventoryList(List<LocationInventory> locationInventoryList) {
        this.locationInventoryList = locationInventoryList;
    }

    public String getPartColor() {
        return partColor;
    }

    public void setPartColor(String partColor) {
        this.partColor = partColor;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public boolean isOptional() {
        return optional;
    }

    public void setOptional(boolean optional) {
        this.optional = optional;
    }
}
