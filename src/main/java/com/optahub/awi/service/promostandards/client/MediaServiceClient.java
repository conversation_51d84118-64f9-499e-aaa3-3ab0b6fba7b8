package com.optahub.awi.service.promostandards.client;

import com.optahub.awi.service.elastic.data.PromoStandardsProduct;
import com.optahub.awi.service.elastic.data.supplier.SupplierData;
import com.optahub.awi.service.promostandards.data.media.ImageReference;
import com.optahub.awi.service.promostandards.data.service.ServiceDescription;
import org.apache.commons.lang3.StringUtils;
import org.optahub.promostandards.wsdl.media.GetMediaContentRequest;
import org.optahub.promostandards.wsdl.media.GetMediaContentResponse;
import org.optahub.promostandards.wsdl.media.MediaTypeType;

import org.springframework.oxm.jaxb.Jaxb2Marshaller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.ws.client.core.support.WebServiceGatewaySupport;
import org.springframework.ws.soap.client.core.SoapActionCallback;

import java.util.List;
import java.util.Optional;

public class MediaServiceClient extends WebServiceGatewaySupport {

    private static final String API_CODE = "MED";
    private static final String API_VERSION = "1.1.0";
    private String apiKey;
    private String apiSecret;

    public MediaServiceClient(final SupplierData supplierConfig,
                              final Jaxb2Marshaller marshaller,
                              final PromoStandardsServiceClient serviceClient) {

        this.setMarshaller(marshaller);
        this.setUnmarshaller(marshaller);
        this.setDefaultUri(this.getUrl(supplierConfig.getCode(), serviceClient));

        this.apiKey = supplierConfig.getKey();
        this.apiSecret = supplierConfig.getSecret();
    }

    public Optional<GetMediaContentResponse> getProductImages(final String productId) {

        final GetMediaContentRequest request = new GetMediaContentRequest();
        request.setId(this.apiKey);
        request.setWsVersion(API_VERSION);
        request.setPassword(this.apiSecret);
        request.setProductId(productId);
        request.setMediaType(MediaTypeType.IMAGE);

        try {
            final GetMediaContentResponse response = (GetMediaContentResponse) getWebServiceTemplate()
                    .marshalSendAndReceive(request, new SoapActionCallback("getMediaContent"));

            return Optional.ofNullable(response);
        } catch (final HttpClientErrorException ex) {
            System.out.println("Unable to get product images for "+productId);
            ex.printStackTrace();
        }

        return Optional.empty();
    }

    private String getUrl(final String companyCode, final PromoStandardsServiceClient serviceClient) {
        final ServiceDescription[] descriptions = serviceClient.getEndpoints(companyCode, API_CODE);
        if(descriptions != null || descriptions.length > 0) {
            for(ServiceDescription description : descriptions) {
                if(StringUtils.equals(API_VERSION, description.getService().getVersion())) {
                    return description.getUrl();
                }
            }
        }

        return StringUtils.EMPTY;
    }
}
