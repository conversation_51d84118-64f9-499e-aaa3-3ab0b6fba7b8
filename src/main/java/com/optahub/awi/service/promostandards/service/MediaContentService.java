package com.optahub.awi.service.promostandards.service;

import com.optahub.awi.service.elastic.data.PromoStandardsProduct;
import com.optahub.awi.service.elastic.data.constant.ImagePrioritizationType;
import com.optahub.awi.service.elastic.data.supplier.SupplierData;
import com.optahub.awi.service.promostandards.client.MediaServiceClient;
import com.optahub.awi.service.promostandards.data.media.ImageReference;
import com.optahub.awi.service.promostandards.factory.SupplierConfigFactory;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.optahub.promostandards.wsdl.media.ClassType;
import org.optahub.promostandards.wsdl.media.Decoration;
import org.optahub.promostandards.wsdl.media.GetMediaContentResponse;
import org.optahub.promostandards.wsdl.media.MediaContent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class MediaContentService {


    private final SupplierService supplierService;

    private final SupplierConfigFactory configFactory;

    @Autowired
    public MediaContentService(final SupplierService supplierService,
                               final SupplierConfigFactory configFactory) {

        this.supplierService = supplierService;
        this.configFactory = configFactory;

    }
    public List<ImageReference> getImages(final String supplierCode, final String productId) {
        final SupplierData supplierData = supplierService.getSupplier(supplierCode);
        final MediaServiceClient client = configFactory.configureMediaClient(supplierData);

        final Optional<GetMediaContentResponse> response = client.getProductImages(productId);

        if(response.isPresent()) {
            return this.parseMediaResponse(response.get(), supplierCode);
        }

        return Collections.emptyList();
    }

    public List<ImageReference> parseMediaResponse(final GetMediaContentResponse contentResponse, final String supplierCode) {

        final List<ImageReference> imageReferences = new ArrayList<>();
        if ( contentResponse.getMediaContentArray() != null
                && !CollectionUtils.isEmpty(contentResponse.getMediaContentArray().getMediaContent())) {

            final List<MediaContent> mediaList = contentResponse.getMediaContentArray().getMediaContent();
            for(final MediaContent content : mediaList) {

                ImageReference imageReference = new ImageReference();
                imageReference.setProductId(content.getProductId());
                imageReference.setPartId(content.getPartId());
                imageReference.setUrl(content.getUrl());
                imageReference.setColor(content.getColor());
                if(content.getClassTypeArray() != null
                        && !CollectionUtils.isEmpty(content.getClassTypeArray().getClassType())) {
                    final List<ClassType> classList = content.getClassTypeArray().getClassType();
                    final StringBuilder builder = new StringBuilder();
                    final StringBuilder idBuilder = new StringBuilder();
                    for (final ClassType type : classList) {
                        builder.append(type.getClassTypeName()).append(",");
                        idBuilder.append(type.getClassTypeId()).append(",");
                    }
                    final int classStringLen = builder.length();
                    if(classStringLen > 0) {
                        builder.deleteCharAt(classStringLen - 1);
                    }

                    final int classIdLen = idBuilder.length();
                    if(classIdLen > 0) {
                        idBuilder.deleteCharAt(classIdLen - 1);
                    }

                    imageReference.setClassType(builder.toString());
                    imageReference.setClassId(idBuilder.toString());
                }

                if(content.getDecorationArray() != null
                        && !CollectionUtils.isEmpty(content.getDecorationArray().getDecoration())) {

                    final List<Decoration> decorations = content.getDecorationArray().getDecoration();

                    final StringBuilder builder = new StringBuilder();
                    for (final Decoration decoration : decorations) {
                        builder.append(decoration.getDecorationName()).append(",");
                    }

                    final int decorationStringLen = builder.length();
                    if(decorationStringLen > 0) {
                        builder.deleteCharAt(decorationStringLen - 1);
                    }

                    imageReference.setDecorationType(builder.toString());
                    imageReference.computeDisplayName();
                }

                if(!filterImage(imageReference, supplierCode)) {
                    imageReferences.add(imageReference);
                }
            }
        }

        return imageReferences;
    }

    public Pair<String, String> getPrimaryImage(final PromoStandardsProduct product) {
        final List<ImageReference> images = product.getImageList();
        String displayName = product.getId() + " - Main";

        if(CollectionUtils.isEmpty(images)) {
            return Pair.of(product.getImageUrl(), displayName);
        }

        final List<ImageReference> prioirtyImages = this.prioritizeImageList(images, product.getSupplierCode());

        if(CollectionUtils.isEmpty(prioirtyImages)) {
            return Pair.of(product.getImageUrl(), displayName);
        }

        return Pair.of(prioirtyImages.get(0).getUrl(), displayName);
    }

    private boolean filterImage(final ImageReference reference, final String supplierCode) {
        if("PCNA".equalsIgnoreCase(supplierCode)) {
            if (reference.getClassType() != null
                    && !reference.getClassType().toLowerCase().contains("HiRes".toLowerCase())) {

                return true;
            }
        }

        if("GEM".equalsIgnoreCase(supplierCode)) {
            if (reference.getClassType() != null
                    && !reference.getClassType().toLowerCase().contains("HiRes".toLowerCase())) {

                return true;
            }
        }

        return false;
    }

    public List<ImageReference> prioritizeImageList(final List<ImageReference> partImageList, final String supplierCode) {

        final List<String> imagePriorityList = ImagePrioritizationType.getPriorityImages(supplierCode);

        if(CollectionUtils.isEmpty(imagePriorityList)) {
            return partImageList;
        }

        final Map<String, Integer> classPriority = new HashMap<>();
        for(int i = 0 ; i < imagePriorityList.size() ; i++) {
            final String imagePriorityClassId = imagePriorityList.get(i);
            String classId = partImageList.stream()
                    .map(ImageReference::getClassId)
                    .filter(item -> item.contains(imagePriorityClassId))
                    .findFirst().orElse(imagePriorityList.get(i));
            classPriority.put(classId, i+1);
        }

        return this.filterImage(partImageList, supplierCode).stream()
                .filter(imageRef -> StringUtils.isNotEmpty(imageRef.getClassId()))
                .filter(imageRef -> imagePriorityList.stream().anyMatch(item -> imageRef.getClassId().contains(item)))
                .sorted(Comparator.comparing(image -> classPriority.containsKey(image.getClassId()) ? classPriority.get(image.getClassId()) : 9999))
                .collect(Collectors.toList());
    }

    private List<ImageReference> filterImage(final List<ImageReference> images, final String supplierCode) {
        if("PCNA".equalsIgnoreCase(supplierCode) || "GEM".equalsIgnoreCase(supplierCode)) {
            return images.stream()
                    .filter(image -> StringUtils.isNotEmpty(image.getClassType()))
                    .filter(image -> image.getClassType().toLowerCase().contains("HiRes".toLowerCase()))
                    .collect(Collectors.toList());
        }

        if("alphabroder".equalsIgnoreCase(supplierCode)) {
            final List<ImageReference> imageReferences = new ArrayList<>();
            final List<ImageReference> sortedImages = images.stream()
                    .sorted(Comparator.comparing(ImageReference::getUrl))
                    .collect(Collectors.toList());

            final Iterator<ImageReference> imageIterator = sortedImages.listIterator();
            ImageReference imageReference = null;
            while(imageIterator.hasNext()) {
                final ImageReference nextImage = imageIterator.next();
                if(imageReference == null || !nextImage.getUrl().equals(imageReference.getUrl())) {
                    imageReference = nextImage;
                    imageReferences.add(nextImage);
                } else {
                    imageIterator.remove();
                }
            }

            return  imageReferences;
        }

        return images;
    }
}
