package com.optahub.awi.service.promostandards.factory;

import com.optahub.awi.service.elastic.data.PromoStandardsProduct;
import com.optahub.awi.service.elastic.data.supplier.SupplierData;
import com.optahub.awi.service.promostandards.client.*;
import com.optahub.awi.service.promostandards.contants.PromostandardsTypeEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.oxm.jaxb.Jaxb2Marshaller;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Component
public class SupplierConfigFactory {

    private final PromoStandardsServiceClient serviceClient;
    private final MarshallerFactory marshallerFactory;

    final Map<String, Map<String, Object>> clientObject;

    @Autowired
    public SupplierConfigFactory(final PromoStandardsServiceClient serviceClient,
                                 final MarshallerFactory marshallerFactory) {
        this.serviceClient = serviceClient;
        this.marshallerFactory = marshallerFactory;
        clientObject = new ConcurrentHashMap<>();
    }

    public ProductServiceClient configureProductClient(final SupplierData config) {
        final ProductServiceClient cachedClient = (ProductServiceClient) this.getClient(config.getCode(), PromostandardsTypeEnum.PRODUCT_SERVICE);
        if(cachedClient != null) {
            return cachedClient;
        }

        final Jaxb2Marshaller marshaller = marshallerFactory.getMarshaller(PromostandardsTypeEnum.PRODUCT_SERVICE);
        final ProductServiceClient productServiceClient =  new ProductServiceClient(config, marshaller, serviceClient);
        this.addClient(config.getCode(), PromostandardsTypeEnum.PRODUCT_SERVICE, productServiceClient);
        return productServiceClient;

    }

    public ProductPriceClient configurePriceClient(final SupplierData config) {
        final ProductPriceClient cachedClient = (ProductPriceClient) this.getClient(config.getCode(), PromostandardsTypeEnum.PRODUCT_PRICE_SERVICE);
        if(cachedClient != null) {
            return cachedClient;
        }
        final Jaxb2Marshaller marshaller = marshallerFactory.getMarshaller(PromostandardsTypeEnum.PRODUCT_PRICE_SERVICE);
        final ProductPriceClient priceClient = new ProductPriceClient(config, marshaller, serviceClient);
        this.addClient(config.getCode(), PromostandardsTypeEnum.PRODUCT_PRICE_SERVICE, priceClient);
        return priceClient;
    }

    public InventoryClient configureInventoryClient(final SupplierData config) {
        final InventoryClient cachedClient = (InventoryClient) this.getClient(config.getCode(), PromostandardsTypeEnum.INVENTORY);
        if(cachedClient != null) {
            return cachedClient;
        }
        final Jaxb2Marshaller marshaller = marshallerFactory.getMarshaller(PromostandardsTypeEnum.INVENTORY);
        final InventoryClient inventoryClient = new InventoryClient(config, marshaller, serviceClient);
        this.addClient(config.getCode(), PromostandardsTypeEnum.INVENTORY, inventoryClient);
        return inventoryClient;
    }

    public MediaServiceClient configureMediaClient(final SupplierData config) {
        final MediaServiceClient cachedClient = (MediaServiceClient) this.getClient(config.getCode(), PromostandardsTypeEnum.MEDIA);
        if(cachedClient != null) {
            return cachedClient;
        }
        final Jaxb2Marshaller marshaller = marshallerFactory.getMarshaller(PromostandardsTypeEnum.MEDIA);
        final MediaServiceClient client = new MediaServiceClient(config, marshaller, serviceClient);
        this.addClient(config.getCode(), PromostandardsTypeEnum.MEDIA, client);
        return client;
    }

    private Object getClient(final String supplierCode, final PromostandardsTypeEnum type) {
        if(clientObject.containsKey(supplierCode)) {
            final Map<String, Object> clientObjectMap = clientObject.get(supplierCode);
            return clientObjectMap.get(type.name());
        }

        return null;
    }

    private void addClient(final String supplierCode, final PromostandardsTypeEnum type, final Object clientObj) {
        Map<String, Object> supplierMap = clientObject.get(supplierCode);
        if(supplierMap == null) {
            supplierMap = new ConcurrentHashMap<>();
            clientObject.put(supplierCode, supplierMap);
        }

        supplierMap.put(type.name(), clientObj);
    }
}

