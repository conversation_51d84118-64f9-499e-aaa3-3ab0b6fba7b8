package com.optahub.awi.service.promostandards.service;

import com.optahub.awi.service.elastic.data.PromoStandardsProduct;
import com.optahub.awi.service.elastic.data.supplier.SupplierData;
import com.optahub.awi.service.elastic.service.ElasticSearchObjectMapper;
import com.optahub.awi.service.promostandards.client.InventoryClient;
import com.optahub.awi.service.promostandards.data.inventory.ProductInventory;
import com.optahub.awi.service.promostandards.factory.SupplierConfigFactory;

import org.optahub.promostandards.wsdl.inventory.GetInventoryLevelsResponse;
import org.optahub.promostandards.wsdl.inventory.Inventory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
public class ProductInventoryService {

    private final SupplierService supplierService;
    private final SupplierConfigFactory configFactory;
    private final ElasticSearchObjectMapper elasticSearchObjectMapper;

    @Autowired
    public ProductInventoryService(final SupplierService supplierService,
                                   final ElasticSearchObjectMapper elasticSearchObjectMapper,
                                   final SupplierConfigFactory configFactory) {

        this.supplierService = supplierService;
        this.configFactory = configFactory;
        this.elasticSearchObjectMapper = elasticSearchObjectMapper;

    }
    public Optional<ProductInventory> getInventory(final PromoStandardsProduct product) {
        final SupplierData supplierData = supplierService.getSupplier(product.getSupplierCode());

        if(supplierData.isDisableInventory()) {
            return Optional.empty();
        }

        final InventoryClient client = configFactory.configureInventoryClient(supplierData);
        final Optional<GetInventoryLevelsResponse> responseOpt = client.getInventory(product.getProductId());
        if(responseOpt.isPresent()) {
            final Inventory inventory =  responseOpt.get().getInventory();
            final ProductInventory productInventory = elasticSearchObjectMapper.buildInventory(inventory);
            return Optional.of(productInventory);
        }

        return Optional.empty();
    }
}
