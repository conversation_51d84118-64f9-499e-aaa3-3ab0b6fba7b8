package com.optahub.awi.service.promostandards.data.decoration;

public class Charge {
    private String name;
    private String type;

    private Integer minQuantityX;
    private String uomX;

    private Integer maxQuantityY;
    private String uomY;

    private Integer price;

    private Integer repeatPrice;

    private String discountCode;

    private String repeatDiscountCode;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Integer getMinQuantityX() {
        return minQuantityX;
    }

    public void setMinQuantityX(Integer minQuantityX) {
        this.minQuantityX = minQuantityX;
    }

    public String getUomX() {
        return uomX;
    }

    public void setUomX(String uomX) {
        this.uomX = uomX;
    }

    public Integer getMaxQuantityY() {
        return maxQuantityY;
    }

    public void setMaxQuantityY(Integer maxQuantityY) {
        this.maxQuantityY = maxQuantityY;
    }

    public String getUomY() {
        return uomY;
    }

    public void setUomY(String uomY) {
        this.uomY = uomY;
    }

    public Integer getPrice() {
        return price;
    }

    public void setPrice(Integer price) {
        this.price = price;
    }

    public Integer getRepeatPrice() {
        return repeatPrice;
    }

    public void setRepeatPrice(Integer repeatPrice) {
        this.repeatPrice = repeatPrice;
    }

    public String getDiscountCode() {
        return discountCode;
    }

    public void setDiscountCode(String discountCode) {
        this.discountCode = discountCode;
    }

    public String getRepeatDiscountCode() {
        return repeatDiscountCode;
    }

    public void setRepeatDiscountCode(String repeatDiscountCode) {
        this.repeatDiscountCode = repeatDiscountCode;
    }
}
