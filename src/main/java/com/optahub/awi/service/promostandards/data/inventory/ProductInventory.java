package com.optahub.awi.service.promostandards.data.inventory;

import java.util.HashMap;
import java.util.Map;

public class ProductInventory {
    private Map<String, ProductPartInventory> mainInventory;
    private Map<String, ProductPartInventory> optionalInventory;

    public Map<String, ProductPartInventory> getMainInventory() {
        if(this.mainInventory == null) {
            this.mainInventory = new HashMap<>();
        }
        return mainInventory;
    }

    public void setMainInventory(Map<String, ProductPartInventory> mainInventory) {
        this.mainInventory = mainInventory;
    }

    public Map<String, ProductPartInventory> getOptionalInventory() {
        if(this.optionalInventory == null) {
            this.optionalInventory = new HashMap<>();
        }
        return optionalInventory;
    }

    public void setOptionalInventory(Map<String, ProductPartInventory> optionalInventory) {
        this.optionalInventory = optionalInventory;
    }
}
