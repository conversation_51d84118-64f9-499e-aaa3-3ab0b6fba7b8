package com.optahub.awi.service.promostandards.data.price;

import java.util.List;

public class Decoration {
    private String decorationId;
    private String name;
    private String geometry;

    private String height;
    private String width;
    private String diameter;

    private String uom;

    private Integer unitsIncluded;

    private String unitsIncludedUom;

    private Integer maxUnits;

    private Integer leadTime;

    private Integer rushLeadTime;

    private Boolean isDefault;

    private Boolean defaultLocationSubstitutionAllowed;

    private Boolean defaultMethodSubstitutionAllowed;

    private List<DecorationCharge> chargeList;

    public String getDecorationId() {
        return decorationId;
    }

    public void setDecorationId(String decorationId) {
        this.decorationId = decorationId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getGeometry() {
        return geometry;
    }

    public void setGeometry(String geometry) {
        this.geometry = geometry;
    }

    public String getUom() {
        return uom;
    }

    public void setUom(String uom) {
        this.uom = uom;
    }

    public List<DecorationCharge> getChargeList() {
        return chargeList;
    }

    public void setChargeList(List<DecorationCharge> chargeList) {
        this.chargeList = chargeList;
    }

    public Integer getUnitsIncluded() {
        return unitsIncluded;
    }

    public void setUnitsIncluded(Integer unitsIncluded) {
        this.unitsIncluded = unitsIncluded;
    }

    public String getUnitsIncludedUom() {
        return unitsIncludedUom;
    }

    public void setUnitsIncludedUom(String unitsIncludedUom) {
        this.unitsIncludedUom = unitsIncludedUom;
    }

    public Integer getMaxUnits() {
        return maxUnits;
    }

    public void setMaxUnits(Integer maxUnits) {
        this.maxUnits = maxUnits;
    }

    public Integer getLeadTime() {
        return leadTime;
    }

    public void setLeadTime(Integer leadTime) {
        this.leadTime = leadTime;
    }

    public Integer getRushLeadTime() {
        return rushLeadTime;
    }

    public void setRushLeadTime(Integer rushLeadTime) {
        this.rushLeadTime = rushLeadTime;
    }

    public Boolean getDefault() {
        return isDefault;
    }

    public void setDefault(Boolean aDefault) {
        isDefault = aDefault;
    }

    public String getHeight() {
        return height;
    }

    public void setHeight(String height) {
        this.height = height;
    }

    public String getWidth() {
        return width;
    }

    public void setWidth(String width) {
        this.width = width;
    }

    public String getDiameter() {
        return diameter;
    }

    public void setDiameter(String diameter) {
        this.diameter = diameter;
    }

    public Boolean getDefaultLocationSubstitutionAllowed() {
        return defaultLocationSubstitutionAllowed;
    }

    public void setDefaultLocationSubstitutionAllowed(Boolean defaultLocationSubstitutionAllowed) {
        this.defaultLocationSubstitutionAllowed = defaultLocationSubstitutionAllowed;
    }

    public Boolean getDefaultMethodSubstitutionAllowed() {
        return defaultMethodSubstitutionAllowed;
    }

    public void setDefaultMethodSubstitutionAllowed(Boolean defaultMethodSubstitutionAllowed) {
        this.defaultMethodSubstitutionAllowed = defaultMethodSubstitutionAllowed;
    }
}
