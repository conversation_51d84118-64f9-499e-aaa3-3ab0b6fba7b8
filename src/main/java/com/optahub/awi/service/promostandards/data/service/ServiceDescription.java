package com.optahub.awi.service.promostandards.data.service;

import com.fasterxml.jackson.annotation.JsonProperty;

public class ServiceDescription {

    @JsonProperty("URL")
    private String url;

    @JsonProperty("TestURL")
    private String testUrl;

    @JsonProperty("Service")
    private ServiceDetails service;

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getTestUrl() {
        return testUrl;
    }

    public void setTestUrl(String testUrl) {
        this.testUrl = testUrl;
    }

    public ServiceDetails getService() {
        return service;
    }

    public void setService(ServiceDetails service) {
        this.service = service;
    }
}
