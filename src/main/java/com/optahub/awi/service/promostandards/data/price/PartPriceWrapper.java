package com.optahub.awi.service.promostandards.data.price;

import java.util.List;
import java.util.Map;

public class PartPriceWrapper {
    private Map<String, List<PartPrice>> netPrice;
    private Map<String, List<PartPrice>> listPrice;
    private Map<String, List<PartPrice>> listBlankPrice;

    private Map<String, List<PartPrice>> netBlankPrice;

    private Map<String, List<PartPrice>> customerPrice;
    private Map<String, List<PartPrice>> customerBlankPrice;

    public Map<String, List<PartPrice>> getNetPrice() {
        return netPrice;
    }

    public void setNetPrice(Map<String, List<PartPrice>> netPrice) {
        this.netPrice = netPrice;
    }

    public Map<String, List<PartPrice>> getListPrice() {
        return listPrice;
    }

    public void setListPrice(Map<String, List<PartPrice>> listPrice) {
        this.listPrice = listPrice;
    }

    public Map<String, List<PartPrice>> getListBlankPrice() {
        return listBlankPrice;
    }

    public void setListBlankPrice(Map<String, List<PartPrice>> listBlankPrice) {
        this.listBlankPrice = listBlankPrice;
    }

    public Map<String, List<PartPrice>> getNetBlankPrice() {
        return netBlankPrice;
    }

    public void setNetBlankPrice(Map<String, List<PartPrice>> netBlankPrice) {
        this.netBlankPrice = netBlankPrice;
    }

    public Map<String, List<PartPrice>> getCustomerPrice() {
        return customerPrice;
    }

    public void setCustomerPrice(Map<String, List<PartPrice>> customerPrice) {
        this.customerPrice = customerPrice;
    }

    public Map<String, List<PartPrice>> getCustomerBlankPrice() {
        return customerBlankPrice;
    }

    public void setCustomerBlankPrice(Map<String, List<PartPrice>> customerBlankPrice) {
        this.customerBlankPrice = customerBlankPrice;
    }
}
