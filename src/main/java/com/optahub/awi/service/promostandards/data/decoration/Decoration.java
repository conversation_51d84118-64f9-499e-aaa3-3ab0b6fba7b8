package com.optahub.awi.service.promostandards.data.decoration;

import java.util.List;

public class Decoration {

    private String name;
    private String geometry;

    private Double height;

    private Double width;

    private Double diameter;

    private String uom;

    private Integer unitsIncluded;

    private String unitsIncludedUom;

    private Integer maxUnits;

    private Integer leadTime;

    private Integer rushLeadTime;

    private Boolean isDefault;

    private List<Charge> charges;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getGeometry() {
        return geometry;
    }

    public void setGeometry(String geometry) {
        this.geometry = geometry;
    }

    public Double getHeight() {
        return height;
    }

    public void setHeight(Double height) {
        this.height = height;
    }

    public Double getWidth() {
        return width;
    }

    public void setWidth(Double width) {
        this.width = width;
    }

    public Double getDiameter() {
        return diameter;
    }

    public void setDiameter(Double diameter) {
        this.diameter = diameter;
    }

    public String getUom() {
        return uom;
    }

    public void setUom(String uom) {
        this.uom = uom;
    }

    public Integer getUnitsIncluded() {
        return unitsIncluded;
    }

    public void setUnitsIncluded(Integer unitsIncluded) {
        this.unitsIncluded = unitsIncluded;
    }

    public String getUnitsIncludedUom() {
        return unitsIncludedUom;
    }

    public void setUnitsIncludedUom(String unitsIncludedUom) {
        this.unitsIncludedUom = unitsIncludedUom;
    }

    public Integer getMaxUnits() {
        return maxUnits;
    }

    public void setMaxUnits(Integer maxUnits) {
        this.maxUnits = maxUnits;
    }

    public Integer getLeadTime() {
        return leadTime;
    }

    public void setLeadTime(Integer leadTime) {
        this.leadTime = leadTime;
    }

    public Integer getRushLeadTime() {
        return rushLeadTime;
    }

    public void setRushLeadTime(Integer rushLeadTime) {
        this.rushLeadTime = rushLeadTime;
    }

    public Boolean getDefault() {
        return isDefault;
    }

    public void setDefault(Boolean aDefault) {
        isDefault = aDefault;
    }

    public List<Charge> getCharges() {
        return charges;
    }

    public void setCharges(List<Charge> charges) {
        this.charges = charges;
    }
}
