package com.optahub.awi.service.promostandards.data.media;

import org.apache.commons.lang3.StringUtils;

public class ImageReference {
    private String productId;
    private String partId;
    private String url;
    private String classType;

    private String classId;
    private String decorationType;

    private String displayName;

    private String color;

    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }

    public String getPartId() {
        return partId;
    }

    public void setPartId(String partId) {
        this.partId = partId;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getClassType() {
        return classType;
    }

    public void setClassType(String classType) {
        this.classType = classType;
    }

    public String getDecorationType() {
        return decorationType;
    }

    public void setDecorationType(String decorationType) {
        this.decorationType = decorationType;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    public void computeDisplayName() {
        StringBuilder nameBuiler = new StringBuilder();

        if(StringUtils.isEmpty(this.getPartId())) {
            nameBuiler.append(this.getProductId());
        }else {
            nameBuiler.append(this.getPartId());
        }

        nameBuiler.append(" - ");

        if(StringUtils.isEmpty(this.getDecorationType())) {
            nameBuiler.append(this.getClassType());
        } else {
            nameBuiler.append(this.getDecorationType());
        }

        this.setDisplayName(nameBuiler.toString());
    }

    public String getDisplayName() {
        return this.displayName;
    }

    public String getClassId() {
        return classId;
    }

    public void setClassId(String classId) {
        this.classId = classId;
    }

    public String getColor() {
        return color;
    }

    public void setColor(String color) {
        this.color = color;
    }
}
