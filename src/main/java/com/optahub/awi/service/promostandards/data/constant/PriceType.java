package com.optahub.awi.service.promostandards.data.constant;

import org.apache.commons.lang3.BooleanUtils;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public enum PriceType {
    LIST_DECORATED("List", "Decorated"),
    LIST_BLANK("List", "Blank"),
    NET_DECORATED("Net", "Decorated"),
    NET_BLANK("Net", "Blank"),
    CUSTOMER_DECORATED("Customer", "Decorated"),
    CUSTOMER_BLANK("Customer", "Blank");

    private String priceType;
    private String configurationType;

    PriceType(final String priceType, final String configurationType) {
        this.priceType = priceType;
        this.configurationType = configurationType;
    }

    public String getPriceType() {
        return priceType;
    }

    public String getConfigurationType() {
        return configurationType;
    }

    public static List<PriceType> supportedPriceTypes(final String supplierCode, final Boolean isApparel) {

        if(BooleanUtils.isTrue(isApparel)) {
            return Stream.of(PriceType.values())
                    .collect(Collectors.toList());
        }

        if ("PCNA".equalsIgnoreCase(supplierCode) || "alphabroder".equalsIgnoreCase(supplierCode)) {
            return Stream.of(PriceType.values())
                    .filter(val -> !"Net".equals(val.getPriceType()))
                    .collect(Collectors.toList());
        }

        return Stream.of(PriceType.values())
                .filter(val -> !"Customer".equals(val.getPriceType()))
                .collect(Collectors.toList());

    }
}

