package com.optahub.awi.service.promostandards.client;

import com.optahub.awi.service.promostandards.data.service.Company;
import com.optahub.awi.service.promostandards.data.service.ServiceDescription;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;

@Component
public class PromoStandardsServiceClient {

    private final static String PROMO_URL = "https://services.promostandards.org/WebServiceRepository/WebServiceRepository.svc/json";
    private final static String ALL_COMPANY_URL = "https://services.promostandards.org/WebServiceRepository/WebServiceRepository.svc/json/companies";
    private final static String COMPANY_SERVICES_URL = "/companies/%s/endpoints/types/%s";
    private final static String COMPANY_BY_CODE = "/companies/%s";
    private final RestTemplate restTemplate;

    @Autowired
    public PromoStandardsServiceClient() {
        this.restTemplate = new RestTemplate();
    }

    public ServiceDescription[] getEndpoints(final String companyCode, final String serviceType) {
        final String suffix = String.format(COMPANY_SERVICES_URL, companyCode, serviceType);
        final ResponseEntity<ServiceDescription[]> response = restTemplate.getForEntity(PROMO_URL + suffix, ServiceDescription[].class);

        if(response.getStatusCode().is2xxSuccessful()) {
            return response.getBody();
        }

        return new ServiceDescription[0];
    }

    public Company getCompanyByCode(final String companyCode) {
        final String suffix = String.format(COMPANY_BY_CODE, companyCode);
        try {
            final ResponseEntity<Company> response = restTemplate.getForEntity(PROMO_URL + suffix, Company.class);


            if (response.getStatusCode().is2xxSuccessful()) {
                return response.getBody();
            }
        } catch (HttpClientErrorException ex) {
            System.out.println("Unable to get details for "+companyCode);
        }

        return this.getAllCompanyByCode(companyCode);
    }

    private Company getAllCompanyByCode(final String companyCode) {
        try {
            final ResponseEntity<Company[]> response = restTemplate.getForEntity(ALL_COMPANY_URL, Company[].class);


            if (response.getStatusCode().is2xxSuccessful()) {
                final Company[] allCompanyArray =  response.getBody();

                for(Company company : allCompanyArray) {
                    if(companyCode.equalsIgnoreCase(company.getCode())) {
                        return company;
                    }
                }
            }
        } catch (HttpClientErrorException ex) {
            System.out.println("Unable to get details for "+companyCode);
        }

        Company notFoundCompany = new Company();
        notFoundCompany.setCode(companyCode);
        notFoundCompany.setName(companyCode);

        return notFoundCompany;
    }
}

