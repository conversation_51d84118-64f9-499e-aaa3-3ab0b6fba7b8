package com.optahub.awi.service.promostandards.factory;

import com.optahub.awi.service.promostandards.contants.PromostandardsTypeEnum;
import org.springframework.oxm.jaxb.Jaxb2Marshaller;
import org.springframework.stereotype.Component;

@Component
public class MarshallerFactory {

    public Jaxb2Marshaller getMarshaller(final PromostandardsTypeEnum promostandardsType) {
        final Jaxb2Marshaller marshaller = new Jaxb2Marshaller();
        marshaller.setContextPath(promostandardsType.getPath());
        return marshaller;
    }
}
