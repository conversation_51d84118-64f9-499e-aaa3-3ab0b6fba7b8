package com.optahub.awi.service.promostandards.service;

import java.io.IOException;
import java.util.Objects;

import com.optahub.awi.service.elastic.data.supplier.SupplierData;
import com.optahub.awi.service.elastic.service.ElasticQueryBuilderService;
import com.optahub.awi.service.elastic.service.ElasticSearchService;
import com.optahub.awi.service.promostandards.client.PromoStandardsServiceClient;
import com.optahub.awi.service.promostandards.data.service.Company;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.stereotype.Service;

import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch.core.SearchRequest;
import co.elastic.clients.elasticsearch.core.SearchResponse;
import co.elastic.clients.elasticsearch.core.search.Hit;

@Service
public class SupplierService {

    private final ElasticQueryBuilderService queryService;
    private final ElasticSearchService searchService;
    private final PromoStandardsServiceClient serviceClient;
    private final ElasticsearchClient elasticsearchClient;

    public SupplierService(final ElasticQueryBuilderService queryService,
                           final ElasticSearchService searchService,
                           final PromoStandardsServiceClient serviceClient,
                           final ElasticsearchClient elasticsearchClient) {

        this.queryService = queryService;
        this.searchService = searchService;
        this.serviceClient = serviceClient;
        this.elasticsearchClient = elasticsearchClient;
    }
    public SupplierData getSupplier(final String supplierCode) {
        final SearchRequest supplierQuery = queryService.buildSupplierQuery(supplierCode);
        final SearchResponse<SupplierData> supplierHits;
        try {
            supplierHits = elasticsearchClient.search(supplierQuery, SupplierData.class);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        if (supplierHits.hits().total() != null && supplierHits.hits().total().value() > 0) {
            return supplierHits.hits().hits().stream()
                .map(Hit::source)
                .filter(Objects::nonNull)
                .findFirst()
                .orElse(null);
        }
        return null;
    }

    public Company getCompanyDetails(final String supplierCode) {
       return serviceClient.getCompanyByCode(supplierCode);
    }
}
