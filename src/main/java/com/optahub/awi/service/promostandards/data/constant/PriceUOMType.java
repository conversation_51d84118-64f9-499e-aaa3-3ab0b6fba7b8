package com.optahub.awi.service.promostandards.data.constant;

import java.util.Arrays;
import java.util.Optional;

public enum PriceUOMType {

    BX("Box"),
    CA("Case"),
    DZ ("Dozen"),
    EA("Each"),
    KT("Kit"),
    PR("Pair"),
    PK ("Package"),
    RL("Roll"),
    ST("Set"),
    SL("Sleeve"),
    TH("Thousand");

    private String label;

    PriceUOMType(final String label) {
        this.label = label;
    }

    public String getLabel() {
        return label;
    }

    public static String getLabelForUom(final String uom) {
        final Optional<PriceUOMType> priceUom = Arrays.stream(PriceUOMType.values())
                .filter(val -> val.name().equalsIgnoreCase(uom))
                .findFirst();

        if (priceUom.isPresent()) {
            return priceUom.get().getLabel();
        }

        return uom;
    }
}

