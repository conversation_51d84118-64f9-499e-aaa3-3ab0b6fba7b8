package com.optahub.awi.service.promostandards.data.price;

import java.util.List;

public class Location {
    private String locationId;
    private String name;

    private boolean defaultLocation;

    private Integer maxDecoration;

    private Integer minDecoration;

    private Integer included;

    private List<Decoration> decorationList;

    public String getLocationId() {
        return locationId;
    }

    public void setLocationId(String locationId) {
        this.locationId = locationId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public boolean isDefaultLocation() {
        return defaultLocation;
    }

    public void setDefaultLocation(boolean defaultLocation) {
        this.defaultLocation = defaultLocation;
    }

    public List<Decoration> getDecorationList() {
        return decorationList;
    }

    public void setDecorationList(List<Decoration> decorationList) {
        this.decorationList = decorationList;
    }

    public Integer getMaxDecoration() {
        return maxDecoration;
    }

    public void setMaxDecoration(Integer maxDecoration) {
        this.maxDecoration = maxDecoration;
    }

    public Integer getMinDecoration() {
        return minDecoration;
    }

    public void setMinDecoration(Integer minDecoration) {
        this.minDecoration = minDecoration;
    }

    public Integer getIncluded() {
        return included;
    }

    public void setIncluded(Integer included) {
        this.included = included;
    }
}
