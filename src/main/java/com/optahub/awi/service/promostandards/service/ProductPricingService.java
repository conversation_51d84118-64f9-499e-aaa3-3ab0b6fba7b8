package com.optahub.awi.service.promostandards.service;

import com.optahub.awi.service.elastic.data.FobDetails;
import com.optahub.awi.service.elastic.data.PromoStandardsProduct;
import com.optahub.awi.service.elastic.data.supplier.SupplierData;
import com.optahub.awi.service.elastic.service.ElasticSearchObjectMapper;
import com.optahub.awi.service.promostandards.client.ProductPriceClient;
import com.optahub.awi.service.promostandards.data.constant.PriceType;
import com.optahub.awi.service.promostandards.data.price.PartPriceWrapper;
import com.optahub.awi.service.promostandards.data.price.ProductPrice;
import com.optahub.awi.service.promostandards.factory.SupplierConfigFactory;

import org.optahub.promostandards.wsdl.pricing.Configuration;
import org.optahub.promostandards.wsdl.pricing.GetConfigurationAndPricingResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;

@Service
public class ProductPricingService {

    private final SupplierService supplierService;
    private final SupplierConfigFactory configFactory;
    private final ElasticSearchObjectMapper elasticSearchObjectMapper;

    @Autowired
    public ProductPricingService(final SupplierService supplierService,
                                 final ElasticSearchObjectMapper elasticSearchObjectMapper,
                                 final SupplierConfigFactory configFactory) {

        this.supplierService = supplierService;
        this.configFactory = configFactory;
        this.elasticSearchObjectMapper = elasticSearchObjectMapper;

    }
    public ProductPrice getPrice(final PromoStandardsProduct product) {
        final SupplierData supplierData = supplierService.getSupplier(product.getSupplierCode());

        if(supplierData.isDisablePricing()) {
            final PartPriceWrapper wrapper = new PartPriceWrapper();
            final ProductPrice emptyPrice = new ProductPrice();
            emptyPrice.setLocationList(Collections.emptyList());
            emptyPrice.setBlankLocationList(Collections.emptyList());
            emptyPrice.setPartPrices(wrapper);

            return emptyPrice;
        }

        final ProductPriceClient priceClient = configFactory.configurePriceClient(supplierData);

        final List<FobDetails> fobList = product.getFobDetailsList();
        FobDetails pricingFob = null;

        if(!CollectionUtils.isEmpty(fobList)) {
            pricingFob = fobList.stream().filter(fob -> "US".equals(fob.getCountry()))
                    .findFirst()
                    .orElse(fobList.get(0));
        }
        final Map<PriceType, Configuration> configurationMap = this.buildPartPrice(product, pricingFob, priceClient);
        final ProductPrice productPrice = elasticSearchObjectMapper.buildProductPrice(configurationMap, product.getApparel());
        return productPrice;

    }

    public ProductPrice getListPrice(final String supplierCode, final String productId) {
        final SupplierData supplierData = supplierService.getSupplier(supplierCode);
        final ProductPriceClient priceClient = configFactory.configurePriceClient(supplierData);

        final String fobId = priceClient.getFobIdForProductAndCountry(productId, "US");
        final Map<PriceType, Configuration> priceMap = new HashMap<>();
        final Optional<GetConfigurationAndPricingResponse> response = priceClient.getProductPrice(productId, fobId, PriceType.LIST_DECORATED);

        if(response.isPresent()) {
            priceMap.put(PriceType.LIST_DECORATED, response.get().getConfiguration());
        }

        final ProductPrice productPrice = elasticSearchObjectMapper.buildProductPrice(priceMap, false);
        return productPrice;
    }

    private Map<PriceType, Configuration> buildPartPrice(final PromoStandardsProduct product,
                                                         final FobDetails fob,
                                                         final ProductPriceClient client) {

        final Map<PriceType, Configuration> priceMap = new HashMap<>();
        final String fobId = fob != null ? fob.getId() : client.getFobIdForProductAndCountry(product.getProductId(), "US");
        for(final PriceType priceType : PriceType.supportedPriceTypes(product.getSupplierCode(), product.getApparel())) {
            final Optional<GetConfigurationAndPricingResponse> response = client
                    .getProductPrice(product.getProductId(), fobId, priceType);
            if(response.isPresent()) {
                priceMap.put(priceType, response.get().getConfiguration());
            }
        }

        return priceMap;
    }
}
