package com.optahub.awi.service.calculator.service;

import com.optahub.awi.service.calculator.config.ExcelConfig;
import com.optahub.awi.service.calculator.model.OptionModel;
import com.optahub.awi.service.calculator.model.SuccessResponse;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.PostConstruct;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class EmbrioderyPrintingService {

    private final S3Service s3Service;
    @Value("${excel.sheet.folderPath}")
    private String excelFolderPath;

    @Value("${embriodery.printing}")
    private String excelFileName;

    private TreeMap<Integer, Map<String, Double>> data = new TreeMap<>();
    private Queue<String> stitches = new LinkedList<>();

    public EmbrioderyPrintingService(S3Service s3Service) {
        this.s3Service = s3Service;
    }

    @PostConstruct
    public void init() throws IOException {
        this.loadData();
    }

    public void loadData() throws IOException {
        InputStream inputStream = this.s3Service.readFileByName(excelFolderPath.concat(excelFileName));
        byte[] inputStreamBytes = ExcelConfig.toByteArray(inputStream);
        data = ExcelConfig.extractDataFromExcel(new ByteArrayInputStream(inputStreamBytes), false);
        stitches = ExcelConfig.extractDataFromExcel(new ByteArrayInputStream(inputStreamBytes), true);
    }

    public List<OptionModel> getStitches() {
        if (stitches.isEmpty()) {
            throw new IllegalArgumentException("No colors found in the Excel file.");
        }
        return stitches.stream()
                .map(OptionModel::new)
                .collect(Collectors.toList());
    }

    public ResponseEntity<?> getPrice(int qty, String stitches, double basePrice) {
        return ExcelConfig.getPrice(data,qty,stitches,basePrice);
    }

    public ResponseEntity<?> uploadFile(MultipartFile file) throws IOException{
        this.s3Service.uploadFile(excelFolderPath.concat(excelFileName), file);
        this.loadData();
        return ResponseEntity.ok(new SuccessResponse("200","File upload successfully"));
    }
}



