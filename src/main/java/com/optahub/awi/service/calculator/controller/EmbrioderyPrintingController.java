package com.optahub.awi.service.calculator.controller;

import com.optahub.awi.service.calculator.model.OptionModel;
import com.optahub.awi.service.calculator.model.PriceModel;
import com.optahub.awi.service.calculator.service.EmbrioderyPrintingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

@RestController
@RequestMapping("/printing/embriodery")
public class EmbrioderyPrintingController {

    private final EmbrioderyPrintingService embrioderyPrintingService;

    public EmbrioderyPrintingController(EmbrioderyPrintingService embrioderyPrintingService) {
        this.embrioderyPrintingService = embrioderyPrintingService;
    }

    @GetMapping("/stitchesOptions")
    public ResponseEntity<?> getStitches() {
        try {
            List<OptionModel> stitches = embrioderyPrintingService.getStitches();
            return new ResponseEntity<>(stitches, HttpStatus.OK);
        } catch (IllegalArgumentException ex) {
            return new ResponseEntity<>(null, HttpStatus.NOT_FOUND);
        }
    }

    @GetMapping("/priceByStitchesAndQyt/{qty}/{stitches}/{basePrice}")
    public ResponseEntity<?> priceByStitchesAndQyt(@PathVariable int qty, @PathVariable String stitches, @PathVariable double basePrice) {
        try {
            return embrioderyPrintingService.getPrice(qty, stitches, basePrice);
        } catch (IllegalArgumentException ex) {
            return new ResponseEntity<>(null, HttpStatus.NOT_FOUND);
        }
    }

    @PostMapping("/uploadPriceSheet")
    public ResponseEntity<?> uploadFile(@RequestParam("file") MultipartFile file) {
        try {
            return embrioderyPrintingService.uploadFile(file);
        } catch (IOException e) {
            return ResponseEntity.status(500).body("File upload failed: " + e.getMessage());
        }
    }
}
