package com.optahub.awi.service.calculator.controller;

import com.optahub.awi.service.calculator.service.S3Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;

@RestController
@RequestMapping("/printing")
public class DownloadPriceListController {

    private final S3Service s3Service;

    public DownloadPriceListController(S3Service s3Service) {
        this.s3Service = s3Service;
    }

    @GetMapping("/downloadPriceList/{fileName}")
    public ResponseEntity<InputStreamResource> downloadPriceList(@PathVariable String fileName) throws IOException {
        return this.s3Service.downloadFile(fileName);
    }

}
