package com.optahub.awi.service.calculator.service;

import com.optahub.awi.service.calculator.config.ExcelConfig;
import com.optahub.awi.service.calculator.model.OptionModel;
import com.optahub.awi.service.calculator.model.SuccessResponse;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.PostConstruct;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class ScreenPrintingService {

    private final S3Service s3Service;

    @Value("${screen.printing}")
    private String excelFileName;

    @Value("${excel.sheet.folderPath}")
    private String excelFolderPath;

    private TreeMap<Integer, Map<String, Double>> data = new TreeMap<>();
    private Queue<String> colors = new LinkedList<>();

    public ScreenPrintingService(S3Service s3Service) {
        this.s3Service = s3Service;
    }

    @PostConstruct
    public void init() throws IOException {
       this.loadData();
    }

    public void loadData() throws IOException {
        System.out.println("New price sheet loaded successfully");
        InputStream inputStream = this.s3Service.readFileByName(excelFolderPath.concat(excelFileName));
        byte[] inputStreamBytes = ExcelConfig.toByteArray(inputStream);
        data = ExcelConfig.extractDataFromExcel(new ByteArrayInputStream(inputStreamBytes), false);
        colors = ExcelConfig.extractDataFromExcel(new ByteArrayInputStream(inputStreamBytes), true);
    }

    public List<OptionModel> getColors() {
        if (colors.isEmpty()) {
            throw new IllegalArgumentException("No colors found in the Excel file.");
        }
        return colors.stream()
                .map(OptionModel::new)
                .collect(Collectors.toList());
    }

    public ResponseEntity<?> getPrice(int qty, String color, double basePrice) {
        return ExcelConfig.getPrice(data,qty,color,basePrice);
    }

    public ResponseEntity<?> uploadFile(MultipartFile file) throws IOException{
        this.s3Service.uploadFile(excelFolderPath.concat(excelFileName), file);
        this.loadData();
        return ResponseEntity.ok(new SuccessResponse("200","File upload successfully"));
    }
}



