package com.optahub.awi.service.calculator.controller;

import com.optahub.awi.service.calculator.model.OptionModel;
import com.optahub.awi.service.calculator.model.PriceModel;
import com.optahub.awi.service.calculator.service.ScreenPrintingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
@RestController
@RequestMapping("/printing/screen")
public class ScreenPrintingController {

    private final ScreenPrintingService screenPrintingService;

    public ScreenPrintingController(ScreenPrintingService screenPrintingService) {
        this.screenPrintingService = screenPrintingService;
    }

    @GetMapping("/colorsOptions")
    public ResponseEntity<?> getColors() {
        try {
            List<OptionModel> colors = screenPrintingService.getColors();
            return new ResponseEntity<>(colors, HttpStatus.OK);
        } catch (IllegalArgumentException ex) {
            return new ResponseEntity<>(null, HttpStatus.NOT_FOUND);
        }
    }

    @GetMapping("/priceByColorAndQyt/{qty}/{color}/{basePrice}")
    public ResponseEntity<?> priceByColorAndQyt(@PathVariable int qty, @PathVariable String color, @PathVariable double basePrice) {
        try {
            return screenPrintingService.getPrice(qty, color, basePrice);
        } catch (IllegalArgumentException ex) {
            return ResponseEntity.notFound().build();
        }
    }

    @PostMapping("/uploadPriceSheet")
    public ResponseEntity<?> uploadFile(@RequestParam("file") MultipartFile file) {
        try {
            return screenPrintingService.uploadFile(file);
        } catch (IOException e) {
            return ResponseEntity.status(500).body("File upload failed: " + e.getMessage());
        }
    }
}

