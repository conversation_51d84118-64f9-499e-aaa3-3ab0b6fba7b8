package com.optahub.awi.service.calculator.controller;

import com.optahub.awi.service.calculator.model.OptionModel;
import com.optahub.awi.service.calculator.model.PriceModel;
import com.optahub.awi.service.calculator.service.DtfPrintingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

@RestController
@RequestMapping("/printing/dtf")
public class DtfPrintingController {

    private final DtfPrintingService dtfPrintingService;

    public DtfPrintingController(DtfPrintingService dtfPrintingService) {
        this.dtfPrintingService = dtfPrintingService;
    }

    @GetMapping("/sizeOptions")
    public ResponseEntity<?> getSizes() {
        try {
            List<OptionModel> size = dtfPrintingService.getSizes();
            return new ResponseEntity<>(size, HttpStatus.OK);
        } catch (IllegalArgumentException ex) {
            return new ResponseEntity<>(null, HttpStatus.NOT_FOUND);
        }
    }

    @GetMapping("/priceBySizeAndQyt/{qty}/{size}/{basePrice}")
    public ResponseEntity<?> priceBySizeAndQyt(@PathVariable int qty, @PathVariable String size,  @PathVariable double basePrice) {
        try {
            return dtfPrintingService.getPrice(qty, size, basePrice);
        } catch (IllegalArgumentException ex) {
            return new ResponseEntity<>(null, HttpStatus.NOT_FOUND);
        }
    }

    @PostMapping("/uploadPriceSheet")
    public ResponseEntity<?> uploadFile(@RequestParam("file") MultipartFile file) {
        try {
            return dtfPrintingService.uploadFile(file);
        } catch (IOException e) {
            return ResponseEntity.status(500).body("File upload failed: " + e.getMessage());
        }
    }
}
