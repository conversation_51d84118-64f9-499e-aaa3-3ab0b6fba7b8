package com.optahub.awi.service.calculator.config;

import com.optahub.awi.service.calculator.model.PriceModel;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;

public class ExcelConfig {

    public static <T> T extractDataFromExcel(InputStream inputStream, boolean extractOptions) throws IOException {
        if (extractOptions) {
            Queue<String> options = extractOptionsFromExcel(inputStream);
            return (T) options;
        } else {
            TreeMap<Integer, Map<String, Double>> data = extractDataFromExcel(inputStream);
            return (T) data;
        }
    }

    private static TreeMap<Integer, Map<String, Double>> extractDataFromExcel(InputStream inputStream) throws IOException {
        TreeMap<Integer, Map<String, Double>> data = new TreeMap<>();
        try (Workbook workbook = new XSSFWorkbook(inputStream)) {

            Sheet sheet = workbook.getSheetAt(0);
            Row headerRow = sheet.getRow(0);

            for (int i = 1; i <= sheet.getLastRowNum(); i++) {
                Row dataRow = sheet.getRow(i);
                int qty = (int) dataRow.getCell(0).getNumericCellValue();
                Map<String, Double> prices = new HashMap<>();

                for (int j = 1; j < headerRow.getLastCellNum(); j++) {
                    String color = headerRow.getCell(j).getStringCellValue();
                    prices.put(color, (Double) dataRow.getCell(j).getNumericCellValue());
                }

                data.put(qty, prices);
            }
        }

        return data;
    }

    private static Queue<String> extractOptionsFromExcel(InputStream inputStream) throws IOException {
        Queue<String> options = new LinkedList<>();

        try (Workbook workbook = new XSSFWorkbook(inputStream)) {
            Sheet sheet = workbook.getSheetAt(0);
            Row headerRow = sheet.getRow(0);

            for (int j = 1; j < headerRow.getLastCellNum(); j++) {
                String option = headerRow.getCell(j).getStringCellValue();
                options.add(option);
            }
        }

        return options;
    }

    public static ResponseEntity<?> getPrice(TreeMap<Integer, Map<String, Double>> data, int qty, String option, double basePrice) {
        Integer key = data.floorKey(qty);
        if (key == null) {
            key = data.firstKey();
        }
        if (!data.containsKey(key) || !data.get(key).containsKey(option)) {
            throw new IllegalArgumentException("Price not found for the given quantity and color.");
        }
        if(data.get(key).get(option) == null || data.get(key).get(option) == 0){
            return new ResponseEntity<>("Please select valid quantity.", HttpStatus.BAD_REQUEST);
        }
        double price = (data.get(key).get(option) + basePrice) * qty;

        return ResponseEntity.ok(new PriceModel(price));
    }

    public static byte[] toByteArray(InputStream input) throws IOException {
        ByteArrayOutputStream buffer = new ByteArrayOutputStream();
        int nRead;
        byte[] data = new byte[1024];
        while ((nRead = input.read(data, 0, data.length)) != -1) {
            buffer.write(data, 0, nRead);
        }
        buffer.flush();
        return buffer.toByteArray();
    }

}
