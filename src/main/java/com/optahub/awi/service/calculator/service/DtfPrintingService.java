package com.optahub.awi.service.calculator.service;

import com.optahub.awi.service.calculator.config.ExcelConfig;
import com.optahub.awi.service.calculator.model.OptionModel;
import com.optahub.awi.service.calculator.model.SuccessResponse;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.PostConstruct;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class DtfPrintingService {

    private final S3Service s3Service;

    @Value("${dtf.printing}")
    private String excelFileName;

    @Value("${excel.sheet.folderPath}")
    private String excelFolderPath;

    private TreeMap<Integer, Map<String, Double>> data = new TreeMap<>();
    private Queue<String> sizes = new LinkedList<>();

    public DtfPrintingService(S3Service s3Service) {
        this.s3Service = s3Service;
    }

    @PostConstruct
    public void init() throws IOException {
        this.loadData();
    }

    public void loadData() throws IOException {
        InputStream inputStream = this.s3Service.readFileByName(excelFolderPath.concat(excelFileName));
        byte[] inputStreamBytes = ExcelConfig.toByteArray(inputStream);
        data = ExcelConfig.extractDataFromExcel(new ByteArrayInputStream(inputStreamBytes), false);
        sizes = ExcelConfig.extractDataFromExcel(new ByteArrayInputStream(inputStreamBytes), true);
    }

    public List<OptionModel> getSizes() {
        if (sizes.isEmpty()) {
            throw new IllegalArgumentException("No colors found in the Excel file.");
        }
        return sizes.stream()
                .map(OptionModel::new)
                .collect(Collectors.toList());
    }

    public ResponseEntity<?> getPrice(int qty, String size, double basePrice) {
        return ExcelConfig.getPrice(data,qty,size, basePrice);
    }

    public ResponseEntity<?> uploadFile(MultipartFile file) throws IOException{
        this.s3Service.uploadFile(excelFolderPath.concat(excelFileName), file);
        this.loadData();
        return ResponseEntity.ok(new SuccessResponse("200","File upload successfully"));
    }
}

