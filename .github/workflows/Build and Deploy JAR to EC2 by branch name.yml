name: Build and Deploy JAR to EC2 by branch

on:
  workflow_dispatch:
    inputs:
      branch:
        description: 'Branch to build and deploy'
        required: true
        default: 'main'

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        with:
          ref: ${{ github.event.inputs.branch }}

      - name: Set up Java
        uses: actions/setup-java@v3
        with:
          java-version: '17'
          distribution: 'temurin'

      - name: Make Maven Wrapper Executable
        run: chmod +x ./mvnw

      - name: Build JAR
        run: ./mvnw clean package -DskipTests

      - name: Decode PEM file
        run: |
          echo "${{ secrets.EC2_KEY }}" | base64 -d > ec2_key.pem
          chmod 600 ec2_key.pem

      - name: Add EC2 Host to Known Hosts
        run: |
          mkdir -p ~/.ssh
          ssh-keyscan -H ${{ secrets.EC2_HOST }} >> ~/.ssh/known_hosts

      - name: Copy JAR to EC2
        run: |
          scp -i ec2_key.pem target/${{ secrets.JAR_NAME }} ${{ secrets.EC2_USER }}@${{ secrets.EC2_HOST }}:/home/<USER>/

      - name: SSH and Replace Old JAR
        run: |
          ssh -i ec2_key.pem ${{ secrets.EC2_USER }}@${{ secrets.EC2_HOST }} << 'EOF'
            timestamp=$(date +%Y%m%d%H%M%S)
            cd /opt/elastic-search-api
            if [ -f ${{ secrets.JAR_NAME }} ]; then
              sudo mv ${{ secrets.JAR_NAME }} ${{ secrets.JAR_NAME }}-$timestamp
            fi
            sudo mv /home/<USER>/${{ secrets.JAR_NAME }} /opt/elastic-search-api/
            sudo ./restart.sh
          EOF
