name: Deploy Frontend

on:
  push:
    paths:
      - 'frontend/**'
      - '.github/workflows/deploy-frontend.yml'
    branches:
      - main

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repo
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'

      - name: Install dependencies
        run: |
          cd frontend
          npm install

      - name: Set VITE_BACKEND_URL from secret
        run: |
          echo "VITE_BACKEND_URL=${{ secrets.UAT_VITE_BACKEND_URL }}" > frontend/.env

      - name: Build frontend and rename dist to chat-bot
        run: |
          cd frontend
          npm run build
          mv dist chat-bot

      - name: Decode PEM file
        run: |
          echo "${{ secrets.EC2_KEY }}" | base64 -d > ec2_key.pem
          chmod 600 ec2_key.pem

      - name: Backup existing chat-bot folder on EC2
        run: |
          ssh -i ec2_key.pem -o StrictHostKeyChecking=no ${{ secrets.EC2_USER }}@${{ secrets.EC2_HOST }} << 'EOF'
            DEPLOY_PATH="/var/www/html"
            if [ -d "$DEPLOY_PATH/chat-bot" ]; then
              TIMESTAMP=$(date +%F-%H-%M-%S)
              sudo mv "$DEPLOY_PATH/chat-bot" "$DEPLOY_PATH/chat-bot-backup-$TIMESTAMP"
              echo "Existing chat-bot folder backed up."
            else
              echo "No existing chat-bot folder found to backup."
            fi
          EOF

      - name: Upload new chat-bot build to EC2
        run: |
          scp -i ec2_key.pem -o StrictHostKeyChecking=no -r frontend/chat-bot ${{ secrets.EC2_USER }}@${{ secrets.EC2_HOST }}:/home/<USER>/frontend

      - name: Move chat-bot to web directory
        run: |
          ssh -i ec2_key.pem -o StrictHostKeyChecking=no ${{ secrets.EC2_USER }}@${{ secrets.EC2_HOST }} << 'EOF'
            sudo mv /home/<USER>/frontend/chat-bot /var/www/html/
            echo "chat-bot deployed successfully to /var/www/html"
          EOF

      - name: Copy .htaccess from backup to new chat-bot folder
        run: |
          ssh -i ec2_key.pem -o StrictHostKeyChecking=no ${{ secrets.EC2_USER }}@${{ secrets.EC2_HOST }} << 'EOF'
            BACKUP_FILE="/var/www/html/htaccess-backup/.htaccess"
            CHATBOT_FOLDER="/var/www/html/chat-bot"
            if [ -f "$BACKUP_FILE" ]; then
              sudo cp "$BACKUP_FILE" "$CHATBOT_FOLDER/.htaccess"
              sudo chown www-data:www-data "$CHATBOT_FOLDER/.htaccess"
              sudo chmod 644 "$CHATBOT_FOLDER/.htaccess"
              echo ".htaccess restored to chat-bot folder."
            else
              echo "No htaccess-backup found in backup folder."
            fi
          EOF

      - name: Clean up SSH key
        if: always()
        run: rm -f ec2_key.pem
